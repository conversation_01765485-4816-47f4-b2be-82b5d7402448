import type { tokenize as Tokenize } from "./tokenize.mjs" with { "resolution-mode": "import" };
import type { createAst as CreateAst } from "./create-ast.mjs" with { "resolution-mode": "import" };
import type { validate as Validate } from "./validate.mjs" with { "resolution-mode": "import" };
import type { generateSql as GenerateSql } from "./generate-sql.mjs" with { "resolution-mode": "import" };

let tokenize: typeof Tokenize;
let createAst: typeof CreateAst;
let validate: typeof Validate;
let generateSql: typeof GenerateSql;

export const onReady = Promise.all([
    import("./tokenize.mjs"),
    import("./create-ast.mjs"),
    import("./validate.mjs"),
    import("./generate-sql.mjs"),
]).then(([_tokenize, _createAst, _validate, _generateSql]) => {
    tokenize = _tokenize.tokenize;
    createAst = _createAst.createAst;
    validate = _validate.validate;
    generateSql = _generateSql.generateSql;
});

export { tokenize, createAst, validate, generateSql };
