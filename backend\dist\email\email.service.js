"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const common_1 = require("@nestjs/common");
const nodemailer_1 = require("nodemailer");
const config_service_1 = require("../config/config.service");
const invite_text_1 = require("./invite-text");
let EmailService = class EmailService {
    constructor(configService) {
        this.configService = configService;
    }
    onModuleInit() {
        const options = {
            host: this.configService.config.email.host,
            secure: false,
            port: this.configService.config.email.port,
            tls: {
                rejectUnauthorized: this.configService.config.email.rejectUnauthorized,
            },
        };
        const otpOptions = {
            ...options,
            auth: {
                user: this.configService.config.email.otpUser,
                pass: this.configService.config.email.otpPassword,
            },
        };
        const inviteOptions = {
            ...options,
            auth: {
                user: this.configService.config.email.inviteUser,
                pass: this.configService.config.email.invitePassword,
            },
        };
        this.otpTransporter = (0, nodemailer_1.createTransport)(otpOptions);
        this.inviteTransporter = (0, nodemailer_1.createTransport)(inviteOptions);
    }
    joinAddress(sender, domain) {
        return `${sender}@${domain}`;
    }
    async send(dto) {
        if (this.configService.config.email.disableAllEmails) {
            return false;
        }
        try {
            await dto.transporter.sendMail({
                from: dto.from,
                to: dto.to,
                subject: dto.subject,
                text: dto.text,
                html: dto.html,
            });
            return true;
        }
        catch (e) {
            if (!this.configService.config.email.ignoreErrors) {
                throw e;
            }
            return false;
        }
    }
    async sendOtp(dto) {
        if (this.configService.config.email.disableOtpEmails) {
            return false;
        }
        return await this.send({
            transporter: this.otpTransporter,
            from: this.configService.config.email.otpUser,
            to: [dto.to],
            subject: `${this.configService.config.instance.name} - OTP`,
            text: `Your OTP is ${dto.otp}.`,
        });
    }
    async sendInvite(dto) {
        if (this.configService.config.email.disableInviteEmails) {
            return false;
        }
        const subject = {
            ru: "Коммуна — приглашение",
            en: "Commune — invite",
        };
        return await this.send({
            transporter: this.inviteTransporter,
            from: this.configService.config.email.inviteUser,
            to: [dto.to],
            subject: subject[dto.locale],
            html: (0, invite_text_1.generateInviteText)(dto.name, dto.locale),
        });
    }
};
exports.EmailService = EmailService;
exports.EmailService = EmailService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], EmailService);
//# sourceMappingURL=email.service.js.map