import { Common, Reactor } from "@commune/api";
import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import {
    toPrismaLocalizations,
    toPrismaLocalizationsWhere,
    toPrismaPagination,
} from "src/utils";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class ReactorCommunityService {
    private readonly logger = new Logger(ReactorCommunityService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly minioService: MinioService,
    ) {}

    async getCommunityIds() {
        return this.prisma.reactorCommunity
            .findMany({
                where: {
                    deletedAt: null,
                },
                select: {
                    id: true,
                },
            })
            .then((communities) =>
                communities.map((community) => community.id),
            );
    }

    async getCommunities(
        input: Reactor.GetCommunitiesInput,
        user: CurrentUser | null,
    ) {
        const { ids, query, hubId } = input;

        const communities = await this.prisma.reactorCommunity.findMany({
            ...toPrismaPagination(input.pagination),
            where: Object.assign(
                {},
                ids && { id: { in: ids } },
                !user?.isAdmin && { deletedAt: null },

                hubId && { hubId },

                query && {
                    OR: [
                        {
                            id: query,
                        },
                        {
                            name: toPrismaLocalizationsWhere(query),
                        },
                        {
                            description: toPrismaLocalizationsWhere(query),
                        },
                    ],
                },
            ),
            select: {
                id: true,
                hub: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                headUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
                name: true,
                description: true,
                image: true,
                isOfficial: true,
                createdAt: true,
                updatedAt: true,
                deletedAt: user?.isAdmin,
            },
        });

        return communities;
    }

    async createCommunity(
        input: Reactor.CreateCommunityInput,
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            if (input.headUserId) {
                throw new ForbiddenException(...getError("must_be_admin"));
            }
        }

        const community = await this.prisma.reactorCommunity.create({
            data: {
                headUserId: input.headUserId ?? user.id,
                hubId: input.hubId,
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
                description: {
                    create: toPrismaLocalizations(
                        input.description,
                        "description",
                    ),
                },
            },
        });

        return community;
    }

    async updateCommunity(
        input: Reactor.UpdateCommunityInput,
        user: CurrentUser,
    ) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }

            if (input.isOfficial !== undefined) {
                throw new ForbiddenException(...getError("must_be_admin"));
            }
        }

        const { name, description } = input;

        await this.prisma.reactorCommunity.update({
            where: { id: input.id },
            data: {
                name: name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(name, "name"),
                },
                description: description && {
                    deleteMany: {},
                    create: toPrismaLocalizations(description, "description"),
                },
                isOfficial: input.isOfficial,
            },
        });
    }

    async updateCommunityImage(
        id: string,
        file: Express.Multer.File,
        user: CurrentUser,
    ) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
        });

        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        await this.prisma.$transaction(async (trx) => {
            const imageUrl = await this.minioService.uploadImage(
                file,
                "reactor-community",
                id,
            );

            const image = await trx.image.create({
                data: {
                    url: imageUrl,
                },
            });

            await trx.reactorCommunity.update({
                where: { id },
                data: {
                    imageId: image.id,
                },
            });
        });
    }

    async deleteCommunityImage(id: string, user: CurrentUser) {
        const community = await this.prisma.reactorCommunity.findUniqueOrThrow({
            where: { id, deletedAt: null },
            include: {
                image: true,
            },
        });

        if (!user.isAdmin) {
            if (community.headUserId !== user.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_user"),
                );
            }
        }

        const communityImage = community.image;

        if (!communityImage) {
            return;
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorCommunity.update({
                where: { id },
                data: {
                    imageId: null,
                },
            });

            await trx.image.update({
                where: { id: communityImage.id },
                data: {
                    deletedAt: new Date(),
                },
            });

            // await this.minioService.deleteReactorCommunityImage(id, communityImage.url);
        });
    }
}
