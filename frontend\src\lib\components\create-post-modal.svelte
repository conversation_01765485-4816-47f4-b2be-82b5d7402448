<script lang="ts">
  import type { Common, Reactor } from "@commune/api";
  import type { Editor as TEditor } from "tinymce";

  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import { fetchWithAuth } from "$lib";
  import { Consts } from "@commune/api";
  import {
    Modal,
    LocalizedInput,
    LocalizedEditor,
    TagPicker,
    ReactorHubPicker,
    ReactorCommunityPicker,
    ImageEditor,
  } from "$lib/components";

  interface Props {
    show: boolean;
    locale: Common.WebsiteLocale;
    toLocaleHref: (href: string) => string;
    onClose: () => void;
    onPostCreated?: () => void;
    post?: Reactor.GetPostsOutput[number]; // Optional post for editing
  }

  const { show, locale, toLocaleHref, onClose, onPostCreated, post }: Props = $props();

  const { fetcher: api } = getClient();

  // Image upload types
  interface UploadedImage {
    id: string;
    url: string;
  }

  // i18n translations
  const i18n = {
    en: {
      createPostTitle: "Create New Post",
      editPostTitle: "Edit Post",
      cancel: "Cancel",
      create: "Create",
      save: "Save",
      hub: "Hub",
      hubPlaceholder: "Select hub (optional)...",
      community: "Community",
      communityPlaceholder: "Select community (optional)...",
      hubDisabledByCommunity: "Hub selection is disabled when Community is specified",
      communityDisabledByHub: "Community selection is disabled when Hub is specified",
      tags: "Tags",
      title: "Title",
      titlePlaceholder: "Enter post title...",
      body: "Body",
      bodyPlaceholder: "Write your post content...",
      titleRequired: "Title is required",
      bodyRequired: "Body is required",
      createSuccess: "Post created successfully!",
      createError: "Failed to create post",
      updateSuccess: "Post updated successfully!",
      updateError: "Failed to update post",
      // Image upload translations
      images: "Images",
      uploadImages: "Upload Images",
      dragDropImages: "Drag & drop images here or click to select",
      maxImages: "Maximum 10 images",
      uploading: "Uploading...",
      imageUploadSuccess: "Images uploaded successfully!",
      imageUploadError: "Failed to upload images",
      invalidFileType: "Invalid file type. Please upload JPG, PNG, or WebP images.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      tooManyFiles: "Too many files. Maximum 10 images per post.",
      insertImage: "Insert image",
      removeImage: "Remove image",
      imageGallery: "Image Gallery",
      loadingImages: "Loading images...",
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
    },
    ru: {
      createPostTitle: "Создать новый пост",
      editPostTitle: "Редактировать пост",
      cancel: "Отмена",
      create: "Создать",
      save: "Сохранить",
      hub: "Хаб",
      hubPlaceholder: "Выберите хаб (необязательно)...",
      community: "Сообщество",
      communityPlaceholder: "Выберите сообщество (необязательно)...",
      hubDisabledByCommunity: "Выбор хаба отключен, когда указано сообщество",
      communityDisabledByHub: "Выбор сообщества отключен, когда указан хаб",
      tags: "Теги",
      title: "Заголовок",
      titlePlaceholder: "Введите заголовок поста...",
      body: "Содержание",
      bodyPlaceholder: "Напишите содержание поста...",
      titleRequired: "Заголовок обязателен",
      bodyRequired: "Содержание обязательно",
      createSuccess: "Пост успешно создан!",
      createError: "Не удалось создать пост",
      updateSuccess: "Пост успешно обновлен!",
      updateError: "Не удалось обновить пост",
      // Image upload translations
      images: "Изображения",
      uploadImages: "Загрузить изображения",
      dragDropImages: "Перетащите изображения сюда или нажмите для выбора",
      maxImages: "Максимум 10 изображений",
      uploading: "Загрузка...",
      imageUploadSuccess: "Изображения загружены успешно!",
      imageUploadError: "Не удалось загрузить изображения",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображения.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      tooManyFiles: "Слишком много файлов. Максимум 10 изображений на пост.",
      insertImage: "Вставить изображение",
      removeImage: "Удалить изображение",
      imageGallery: "Галерея изображений",
      loadingImages: "Загрузка изображений...",
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
    },
  };

  const t = $derived(i18n[locale]);

  // Form state
  let postTitle = $state<Common.Localizations>([]);
  let postBody = $state<Common.Localizations>([]);
  let hubId = $state<string | null>(null);
  let communityId = $state<string | null>(null);
  let selectedTags = $state<string[]>([]);
  let isSubmitting = $state(false);
  let formError = $state<string | null>(null);
  let formSuccess = $state<string | null>(null);

  // Image upload state
  let uploadedImages = $state<UploadedImage[]>([]);
  let isUploadingImages = $state(false);
  let isLoadingImages = $state(false);
  let uploadError = $state<string | null>(null);
  let uploadSuccess = $state<string | null>(null);
  let isDragOver = $state(false);

  // Image editor state
  let showImageEditor = $state(false);
  let selectedFileForEditing = $state<File | null>(null);
  let editedFile = $state<File | null>(null);
  let pendingFiles = $state<File[]>([]);

  // Reference to the editor for inserting images
  let editorRef = $state<TEditor | null>(null);

  // Handle mutual exclusivity between hub and community selection
  $effect(() => {
    // When a hub is selected, clear the community
    if (hubId) {
      communityId = null;
    }

    // When a community is selected, clear the hub
    if (communityId) {
      hubId = null;
    }
  });

  // Reset form when modal is opened
  $effect(() => {
    if (show) {
      resetForm();
      // If editing, populate with existing post data
      if (post) {
        postTitle = [...post.title];
        postBody = [...post.body];
        selectedTags = post.tags.map((tag) => tag.id);
        // Note: hubId and communityId are not editable in edit mode
        hubId = post.hub?.id || null;
        communityId = post.community?.id || null;

        // Load existing post images
        loadPostImages(post.id);
      }
    }
  });

  function resetForm() {
    postTitle = [];
    postBody = [];
    hubId = null;
    communityId = null;
    selectedTags = [];
    formError = null;
    formSuccess = null;
    isSubmitting = false;
    // Reset image upload state
    uploadedImages = [];
    isUploadingImages = false;
    isLoadingImages = false;
    uploadError = null;
    uploadSuccess = null;
    isDragOver = false;
    // Reset image editor state
    showImageEditor = false;
    selectedFileForEditing = null;
    editedFile = null;
    pendingFiles = [];
  }

  async function handleSubmitPost() {
    // Clear previous messages
    formError = null;
    formSuccess = null;

    // Validate form
    const hasTitle = postTitle.some((item) => item.value.trim().length > 0);
    const hasBody = postBody.some((item) => item.value.trim().length > 0);

    if (!hasTitle) {
      formError = t.titleRequired;
      return;
    }

    if (!hasBody) {
      formError = t.bodyRequired;
      return;
    }

    isSubmitting = true;

    try {
      if (post) {
        // Edit mode
        await api.reactor.post.patch({
          id: post.id,
          title: postTitle.filter((item) => item.value.trim().length > 0),
          body: postBody.filter((item) => item.value.trim().length > 0),
          tagIds: selectedTags,
          imageIds: uploadedImages.map((img) => img.id),
        });

        formSuccess = t.updateSuccess;

        setTimeout(() => {
          onClose();
          window.location.reload(); // Refresh to show updated content
        }, 1500);
      } else {
        // Create mode
        const { id } = await api.reactor.post.post({
          hubId: hubId,
          communityId: communityId,
          title: postTitle.filter((item) => item.value.trim().length > 0),
          body: postBody.filter((item) => item.value.trim().length > 0),
          tagIds: selectedTags,
          imageIds: uploadedImages.map((img) => img.id),
        });

        formSuccess = t.createSuccess;

        // Call the callback if provided
        if (onPostCreated) {
          onPostCreated();
        }

        setTimeout(() => {
          goto(toLocaleHref(`/reactor/${id}`));
        }, 1500);
      }
    } catch (error) {
      console.error("Error submitting post:", error);
      formError = error instanceof Error ? error.message : post ? t.updateError : t.createError;
    } finally {
      isSubmitting = false;
    }
  }

  // Image upload functions
  function validateImageFiles(files: FileList): string | null {
    if (files.length + uploadedImages.length > 10) {
      return t.tooManyFiles;
    }

    for (const file of files) {
      if (!Consts.ALLOWED_IMAGE_FILE_TYPES.includes(file.type)) {
        return t.invalidFileType;
      }
      if (file.size > Consts.MAX_IMAGE_FILE_SIZE) {
        return t.fileTooLarge;
      }
    }

    return null;
  }

  async function uploadImages(files: FileList) {
    const validationError = validateImageFiles(files);
    if (validationError) {
      uploadError = validationError;
      return;
    }

    isUploadingImages = true;
    uploadError = null;
    uploadSuccess = null;

    try {
      const formData = new FormData();
      for (const file of files) {
        formData.append("images", file);
      }

      const response = await fetchWithAuth("/api/reactor/post/image", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`${t.imageUploadError}: ${response.statusText}`);
      }

      const newImages: UploadedImage[] = await response.json();
      uploadedImages = [...uploadedImages, ...newImages];
      uploadSuccess = t.imageUploadSuccess;

      // Clear success message after 3 seconds
      setTimeout(() => {
        uploadSuccess = null;
      }, 3000);
    } catch (err) {
      uploadError = err instanceof Error ? err.message : t.imageUploadError;
      console.error(err);
    } finally {
      isUploadingImages = false;
    }
  }

  function handleFileInput(event: Event) {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const validationError = validateImageFiles(input.files);
      if (validationError) {
        // Clear the input immediately if validation fails
        input.value = "";
        uploadError = validationError;
        return;
      }

      // Store files for editing and show editor for the first file
      pendingFiles = Array.from(input.files);
      if (pendingFiles.length > 0) {
        selectedFileForEditing = pendingFiles[0];
        showImageEditor = true;
      }

      // Clear the input so the same file can be selected again
      input.value = "";
    }
  }

  function handleDragOver(event: DragEvent) {
    event.preventDefault();
    isDragOver = true;
  }

  function handleDragLeave(event: DragEvent) {
    event.preventDefault();
    isDragOver = false;
  }

  function handleDrop(event: DragEvent) {
    event.preventDefault();
    isDragOver = false;

    if (event.dataTransfer?.files) {
      const validationError = validateImageFiles(event.dataTransfer.files);
      if (validationError) {
        uploadError = validationError;
        return;
      }

      // Store files for editing and show editor for the first file
      pendingFiles = Array.from(event.dataTransfer.files);
      if (pendingFiles.length > 0) {
        selectedFileForEditing = pendingFiles[0];
        showImageEditor = true;
      }
    }
  }

  function insertImageAtCursor(image: UploadedImage) {
    if (editorRef) {
      const imgTag = `<img src="/images/${image.url}" alt="" style="max-width: 100%; height: auto;" />`;

      editorRef.insertContent(imgTag);
    }
  }

  function removeImage(imageId: string) {
    uploadedImages = uploadedImages.filter((img) => img.id !== imageId);
  }

  // Load existing post images when editing
  async function loadPostImages(postId: string) {
    isLoadingImages = true;
    uploadError = null;

    try {
      const images = await api.reactor.post.image.list.get({ id: postId });
      uploadedImages = images;
    } catch (error) {
      console.error("Failed to load post images:", error);
      uploadError = "Failed to load existing images";
    } finally {
      isLoadingImages = false;
    }
  }

  // Image editor handlers
  function handleImageEditorSave(file: File) {
    editedFile = file;
    showImageEditor = false;

    // Upload the edited file and any remaining pending files
    const filesToUpload = [file, ...pendingFiles.slice(1)];
    const fileList = new DataTransfer();
    filesToUpload.forEach((f) => fileList.items.add(f));
    uploadImages(fileList.files);

    // Clear pending files
    pendingFiles = [];
    selectedFileForEditing = null;
    editedFile = null;
  }

  function handleImageEditorCancel() {
    showImageEditor = false;
    selectedFileForEditing = null;
    editedFile = null;
    pendingFiles = [];
  }
</script>

<Modal
  {show}
  title={post ? t.editPostTitle : t.createPostTitle}
  {onClose}
  onSubmit={showImageEditor ? undefined : handleSubmitPost}
  submitText={post ? t.save : t.create}
  cancelText={t.cancel}
  submitDisabled={showImageEditor ||
    isSubmitting ||
    !postTitle.some((item) => item.value.trim().length > 0) ||
    !postBody.some((item) => item.value.trim().length > 0)}
  {isSubmitting}
  size={showImageEditor ? "xl" : "lg"}
  showFooter={!showImageEditor}
>
  {#if showImageEditor && selectedFileForEditing}
    <ImageEditor
      imageFile={selectedFileForEditing}
      onSave={handleImageEditorSave}
      onCancel={handleImageEditorCancel}
      {locale}
      {editedFile}
    />
  {:else}
    <form>
      <!-- Hub and Community Pickers - only show in create mode -->
      {#if !post}
        <!-- Hub Picker -->
        {#if communityId}
          <div class="form-text text-muted">{t.hubDisabledByCommunity}</div>
        {:else}
          <div class="mb-3">
            <ReactorHubPicker
              bind:selectedHubId={hubId}
              {locale}
              label={t.hub}
              placeholder={t.hubPlaceholder}
            />
          </div>
        {/if}

        <!-- Community Picker -->
        {#if hubId}
          <div class="form-text text-muted">{t.communityDisabledByHub}</div>
        {:else}
          <div class="mb-3">
            <ReactorCommunityPicker
              bind:selectedCommunityId={communityId}
              {hubId}
              {locale}
              label={t.community}
              placeholder={t.communityPlaceholder}
            />
          </div>
        {/if}
      {/if}

      <!-- Title Input -->
      <LocalizedInput
        {locale}
        id="post-title"
        label={t.title}
        placeholder={t.titlePlaceholder}
        required
        bind:value={postTitle}
      />

      <!-- Body Editor -->
      <LocalizedEditor
        {locale}
        id="post-body"
        label={t.body}
        required
        bind:value={postBody}
        onEditorInit={(editor) => {
          editorRef = editor;
        }}
        languageSelectPosition="top"
      >
        <!-- Image upload button in the editor controls -->
        <button
          type="button"
          class="btn btn-outline-secondary btn-sm"
          onclick={() =>
            document
              .getElementById(post ? "edit-image-upload-input" : "image-upload-input")
              ?.click()}
          disabled={isUploadingImages || uploadedImages.length >= 10}
          title={t.uploadImages}
        >
          <i class="bi bi-image"></i>
          {t.uploadImages}
        </button>
      </LocalizedEditor>

      <!-- Hidden file input for image upload -->
      <input
        id={post ? "edit-image-upload-input" : "image-upload-input"}
        type="file"
        multiple
        accept={Consts.ALLOWED_IMAGE_FILE_TYPES.join(",")}
        style="display: none;"
        onchange={handleFileInput}
      />

      <!-- Image Upload Section -->
      <div class="mb-3">
        <!-- Image Gallery -->
        {#if isLoadingImages}
          <div class="mt-3">
            <h6>{t.imageGallery}</h6>
            <div class="text-center py-3">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              <span class="text-muted">{t.loadingImages}</span>
            </div>
          </div>
        {:else if uploadedImages.length > 0}
          <div class="mt-3">
            <h6>{t.imageGallery}</h6>
            <div class="row g-2">
              {#each uploadedImages as image (image.id)}
                <div class="col-6 col-md-4 col-lg-3">
                  <div class="position-relative">
                    <button
                      type="button"
                      class="btn p-0 border-0 bg-transparent w-100"
                      onclick={() => insertImageAtCursor(image)}
                      title={t.insertImage}
                      aria-label={t.insertImage}
                    >
                      <img
                        src={`/images/${image.url}`}
                        alt=""
                        class="img-thumbnail w-100"
                        style="height: 120px; object-fit: contain;"
                      />
                    </button>
                    <button
                      type="button"
                      class="btn btn-danger btn-sm position-absolute top-0 end-0 m-1"
                      onclick={() => removeImage(image.id)}
                      title={t.removeImage}
                      aria-label={t.removeImage}
                      style="padding: 0.25rem 0.5rem; font-size: 0.75rem;"
                    >
                      <i class="bi bi-x"></i>
                    </button>
                  </div>
                </div>
              {/each}
            </div>
          </div>
        {/if}

        <div class="form-label">{t.images}</div>

        <!-- Drag & Drop Area -->
        <div
          class="border border-2 border-dashed rounded p-4 text-center {isDragOver
            ? 'border-primary bg-light'
            : 'border-secondary'}"
          ondragover={handleDragOver}
          ondragleave={handleDragLeave}
          ondrop={handleDrop}
          onclick={() =>
            document
              .getElementById(post ? "edit-image-upload-input" : "image-upload-input")
              ?.click()}
          onkeydown={(e) =>
            e.key === "Enter" &&
            document
              .getElementById(post ? "edit-image-upload-input" : "image-upload-input")
              ?.click()}
          role="button"
          tabindex="0"
          style="cursor: pointer; min-height: 100px; display: flex; flex-direction: column; justify-content: center;"
        >
          {#if isUploadingImages}
            <div class="text-muted">
              <div class="spinner-border spinner-border-sm me-2" role="status"></div>
              {t.uploading}
            </div>
          {:else}
            <div class="text-muted">
              <i class="bi bi-cloud-upload fs-2 mb-2"></i>
              <div>{t.dragDropImages}</div>
              <small class="text-muted">{t.maxImages}</small>
            </div>
          {/if}
        </div>

        <!-- Upload Messages -->
        {#if uploadError}
          <div class="alert alert-danger mt-2 mb-0" role="alert">
            {uploadError}
            {#if uploadError.includes("Failed to load existing images")}
              <br /><small>You can still upload new images.</small>
            {/if}
          </div>
        {/if}

        {#if uploadSuccess}
          <div class="alert alert-success mt-2 mb-0" role="alert">
            {uploadSuccess}
          </div>
        {/if}
      </div>

      <!-- Tag Picker -->
      <TagPicker {locale} label={t.tags} bind:selectedTagIds={selectedTags} />

      <!-- Error Message -->
      {#if formError}
        <div class="alert alert-danger mt-3" role="alert">
          {formError}
        </div>
      {/if}

      <!-- Success Message -->
      {#if formSuccess}
        <div class="alert alert-success mt-3" role="alert">
          {formSuccess}
        </div>
      {/if}
    </form>
  {/if}
</Modal>
