import { Module } from "@nestjs/common";
import { CommuneModule } from "src/commune/commune.module";
import { ReactorModule } from "src/reactor/reactor.module";
import { SitemapService } from "./sitemap.service";
import { SitemapController } from "./sitemap.controller";

@Module({
    imports: [CommuneModule, ReactorModule],
    controllers: [SitemapController],
    providers: [SitemapService],
    exports: [SitemapService],
})
export class SitemapModule {}
