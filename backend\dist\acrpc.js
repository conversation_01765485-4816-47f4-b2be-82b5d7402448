"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getServer = getServer;
const client_1 = require("@prisma/client");
const common_1 = require("@nestjs/common");
const server_1 = require("@commune/api/acrpc/server");
const schema_1 = require("@commune/api/acrpc/schema");
let __server = undefined;
function getServer() {
    return (__server ??= (0, server_1.createServer)(schema_1.schema, {}, {
        transformer: schema_1.transformer,
        getMetadata(req, isRequired) {
            if (!req.session || !req.session.user) {
                if (isRequired) {
                    throw new common_1.UnauthorizedException();
                }
                return null;
            }
            const rawUser = req.session.user;
            if (!rawUser) {
                if (isRequired) {
                    throw new common_1.UnauthorizedException();
                }
                return null;
            }
            const currentUser = {
                ...rawUser,
                isAdmin: rawUser.role === client_1.UserRole.admin,
            };
            return {
                user: currentUser,
            };
        },
    }));
}
//# sourceMappingURL=acrpc.js.map