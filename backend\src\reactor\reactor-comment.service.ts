import { Common, Reactor } from "@commune/api";
import { ReactorRatingType } from "@prisma/client";
import {
    BadRequestException,
    ForbiddenException,
    Injectable,
    Logger,
    NotFoundException,
} from "@nestjs/common";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { RatingService } from "src/rating/rating.service";
import { Sql } from "@prisma/client/runtime/library";

type RawComment = {
    id: string;

    post_id: string;
    path: string;
    internal_number: number;

    author_id: string;
    is_anonymous: boolean;
    anonimity_reason: string | null;

    rating_likes: number;
    rating_dislikes: number;
    rating_status: ReactorRatingType | null;

    children_count: number;

    delete_reason: string | null;

    created_at: Date;
    updated_at: Date;
    deleted_at: Date | null;
};

@Injectable()
export class ReactorCommentService {
    private readonly logger = new Logger(ReactorCommentService.name);

    constructor(
        private readonly prisma: PrismaService,
        private readonly ratingService: RatingService,
    ) {}

    async getComments(
        input: Reactor.GetCommentsInput,
        user: CurrentUser | null,
    ) {
        let commentId: string | null = null;
        let masterPostId: string | null = null;

        if (input.id === undefined) {
            if (input.entityType === "comment") {
                throw new BadRequestException(
                    ...getError("get_comments_for_comment_not_implemented"),
                );
            }

            await this.prisma.reactorPost.findUniqueOrThrow({
                where: { id: input.entityId },
            });

            masterPostId = input.entityId;
        } else {
            commentId = input.id;
        }

        const rawComments = await this.prisma.$queryRaw<RawComment[]>`
            SELECT
                comments.id,

                comments.post_id,

                comments.path,
                comments.internal_number,

                COUNT(DISTINCT rating_likes.id)::INT AS rating_likes,
                COUNT(DISTINCT rating_dislikes.id)::INT AS rating_dislikes,
                rating_status.type AS rating_status,

                comments.author_id,
                comments.is_anonymous,
                comments.anonimity_reason,

                COUNT(DISTINCT children.id)::INT AS children_count,

                comments.delete_reason,

                comments.created_at,
                comments.updated_at,
                comments.deleted_at

            FROM reactor_comments comments

            LEFT JOIN reactor_comments children
                ON children.post_id = comments.post_id
                AND children.id <> comments.id
                AND children.path::ltree <@ comments.path::ltree

            LEFT JOIN reactor_ratings rating_likes
                ON rating_likes.entity_id = comments.id
                AND rating_likes.entity_type = 'comment'
                AND rating_likes.type = 'like'

            LEFT JOIN reactor_ratings rating_dislikes
                ON rating_dislikes.entity_id = comments.id
                AND rating_dislikes.entity_type = 'comment'
                AND rating_dislikes.type = 'dislike'

            LEFT JOIN reactor_ratings rating_status
                ON rating_status.entity_id = comments.id
                AND rating_status.entity_type = 'comment'
                AND rating_status.user_id = ${user?.id ?? null}

            WHERE
                ${new Sql(
                    [
                        masterPostId
                            ? `comments.post_id = '${masterPostId}'`
                            : `comments.id = '${commentId}'`,
                    ],
                    [],
                )}

            GROUP BY
                comments.id,
                rating_status.type

            ORDER BY path ASC
        `;

        const users = await this.prisma.user.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.author_id),
                },
            },
            include: {
                name: true,
                image: true,
            },
        });

        const userMap = new Map(users.map((u) => [u.id, u]));

        const commentBodies = await this.prisma.reactorComment.findMany({
            where: {
                id: {
                    in: rawComments.map((c) => c.id),
                },
            },
            include: {
                body: true,
            },
        });

        const commentBodyMap = new Map(
            commentBodies.map((c) => [c.id, c.body]),
        );

        const comments = rawComments.map<Reactor.GetCommentsOutput[number]>(
            (c) => {
                const user = userMap.get(c.author_id)!;
                const body = commentBodyMap.get(c.id)!;

                return {
                    id: c.id,

                    path: c.path,
                    internalNumber: c.internal_number,

                    author: c.is_anonymous
                        ? null
                        : {
                              id: user.id,
                              email: user.email,
                              name: user.name,
                              image: user.image?.url ?? null,
                          },
                    isAnonymous: c.is_anonymous,
                    anonimityReason: c.anonimity_reason,

                    rating: {
                        likes: c.rating_likes,
                        dislikes: c.rating_dislikes,
                        status: c.rating_status,
                    },

                    body: c.deleted_at ? null : body,

                    childrenCount: c.children_count,

                    deleteReason: c.delete_reason,

                    createdAt: c.created_at,
                    updatedAt: c.updated_at,
                    deletedAt: c.deleted_at,
                };
            },
        );

        return comments;
    }

    private async getNextCommentInternalNumber(postId: string) {
        const { internalNumber } =
            await this.prisma.reactorPostInternalNumber.upsert({
                where: { postId },
                update: { internalNumber: { increment: 1 } },
                create: { postId, internalNumber: 1 },
            });

        return internalNumber;
    }

    async createComment(
        dto: {
            entityType: "post" | "comment";
            entityId: string;
            body: Common.Localization[];
        },
        user: CurrentUser,
    ): Promise<{ id: string }> {
        const { entityType, entityId, body } = dto;

        if (entityType === "post") {
            const comment = await this.createPostComment(
                {
                    postId: entityId,
                    body,
                },
                user,
            );

            return { id: comment.id };
        }

        if (entityType === "comment") {
            const comment = await this.createCommentComment(
                {
                    commentId: entityId,
                    body,
                },
                user,
            );

            return { id: comment.id };
        }

        throw new Error("Impossible");
    }

    private async createPostComment(
        dto: {
            postId: string;
            body: Common.Localization[];
        },
        user: CurrentUser,
    ) {
        const post = await this.prisma.reactorPost.findUnique({
            where: { id: dto.postId },
        });

        if (!post) {
            throw new NotFoundException(...getError("post_not_found"));
        }

        const internalNumber = await this.getNextCommentInternalNumber(post.id);

        return await this.prisma.reactorComment.create({
            data: {
                postId: post.id,
                internalNumber,
                path: internalNumber.toString(),
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }

    private async createCommentComment(
        dto: {
            commentId: string;
            body: Common.Localization[];
        },
        user: CurrentUser,
    ) {
        const parentComment = await this.prisma.reactorComment.findUnique({
            where: { id: dto.commentId },
        });

        if (!parentComment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        const internalNumber = await this.getNextCommentInternalNumber(
            parentComment.postId,
        );

        return await this.prisma.reactorComment.create({
            data: {
                postId: parentComment.postId,
                internalNumber,
                path: `${parentComment.path}.${internalNumber}`,
                authorId: user.id,
                body: {
                    create: dto.body.map((b) => ({
                        key: "body",
                        value: b.value,
                        locale: b.locale,
                    })),
                },
            },
        });
    }

    async updateComment(input: Reactor.UpdateCommentInput, user: CurrentUser) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.reactorComment.update({
                where: { id: input.id },
                data: {
                    body: input.body && {
                        deleteMany: {},
                    },
                },
            });

            await trx.reactorComment.update({
                where: { id: input.id },
                data: {
                    body: input.body && {
                        create: input.body.map((b) => ({
                            key: "body",
                            value: b.value,
                            locale: b.locale,
                        })),
                    },
                },
            });
        });

        return true;
    }

    async updateCommentRating(
        input: Reactor.UpdateCommentRatingInput,
        user: CurrentUser,
    ) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        const existingRating = await this.prisma.reactorRating.findFirst({
            where: {
                userId: user.id,
                entityType: "comment",
                entityId: comment.id,
            },
        });

        let newStatus: ReactorRatingType | null = null;

        if (existingRating) {
            if (existingRating.type === input.type) {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.delete({
                        where: { id: existingRating.id },
                    });

                    await this.ratingService.deleteRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: comment.authorId,
                            entityType: "comment",
                            entityId: comment.id,
                        },
                        trx,
                    );
                });
            } else {
                await this.prisma.$transaction(async (trx) => {
                    await trx.reactorRating.update({
                        where: { id: existingRating.id },
                        data: {
                            type: input.type,
                        },
                    });

                    await this.ratingService.upsertRelativeUserRating(
                        {
                            sourceUserId: user.id,
                            targetUserId: comment.authorId,
                            entityType: "comment",
                            entityId: comment.id,
                            value: input.type === "like" ? 1 : -1,
                        },
                        trx,
                    );
                });

                newStatus = input.type;
            }
        } else {
            await this.prisma.$transaction(async (trx) => {
                await trx.reactorRating.create({
                    data: {
                        userId: user.id,
                        entityId: comment.id,
                        entityType: "comment",
                        type: input.type,
                    },
                });

                await this.ratingService.upsertRelativeUserRating(
                    {
                        sourceUserId: user.id,
                        targetUserId: comment.authorId,
                        entityType: "comment",
                        entityId: comment.id,
                        value: input.type === "like" ? 1 : -1,
                    },
                    trx,
                );
            });

            newStatus = input.type;
        }

        const [likes, dislikes] = await Promise.all([
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "like",
                },
            }),
            this.prisma.reactorRating.count({
                where: {
                    entityType: "comment",
                    entityId: comment.id,
                    type: "dislike",
                },
            }),
        ]);

        return {
            likes,
            dislikes,
            status: newStatus,
        };
    }

    async anonimifyComment(
        input: Reactor.AnonimifyCommentInput,
        user: CurrentUser,
    ) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorComment.update({
            where: { id: input.id },
            data: {
                isAnonymous: true,
                anonimityReason: input.reason,
            },
        });

        return true;
    }

    async deleteComment(input: Reactor.DeleteCommentInput, user: CurrentUser) {
        const comment = await this.prisma.reactorComment.findUnique({
            where: { id: input.id },
        });

        if (!comment) {
            throw new NotFoundException(...getError("comment_not_found"));
        }

        if (user.role !== "admin" && user.id !== comment.authorId) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_author"),
            );
        }

        await this.prisma.reactorComment.update({
            where: { id: input.id },
            data: {
                deleteReason: input.reason,
                deletedAt: new Date(),
            },
        });

        return true;
    }
}
