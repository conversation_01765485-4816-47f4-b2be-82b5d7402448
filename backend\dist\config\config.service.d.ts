import { z } from "zod";
import { ConfigService as NestConfigService } from "@nestjs/config";
import { OnModuleInit } from "@nestjs/common";
export type Config = Normalize<z.infer<typeof ConfigSchema>>;
export declare const ConfigSchema: z.ZodObject<{
    instance: z.ZodObject<{
        name: z.ZodString;
        emailDomain: z.ZodString;
    }, "strip", z.ZodType<PERSON>ny, {
        name: string;
        emailDomain: string;
    }, {
        name: string;
        emailDomain: string;
    }>;
    auth: z.ZodObject<{
        disableRegisterInviteCheck: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableRegisterOtpCheck: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableLoginOtpCheck: z.<PERSON>od<PERSON>cts<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        otpExpirationTimeMs: z.<PERSON>;
    }, "strip", z.ZodTypeAny, {
        disableRegisterInviteCheck: boolean;
        disableRegisterOtpCheck: boolean;
        disableLoginOtpCheck: boolean;
        otpExpirationTimeMs: number;
    }, {
        otpExpirationTimeMs: number;
        disableRegisterInviteCheck?: string | undefined;
        disableRegisterOtpCheck?: string | undefined;
        disableLoginOtpCheck?: string | undefined;
    }>;
    minio: z.ZodObject<{
        endpoint: z.ZodString;
        port: z.ZodNumber;
        accessKey: z.ZodString;
        secretKey: z.ZodString;
        useSSL: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
    }, "strip", z.ZodTypeAny, {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL: boolean;
    }, {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL?: string | undefined;
    }>;
    email: z.ZodObject<{
        host: z.ZodString;
        port: z.ZodNumber;
        otpUser: z.ZodString;
        otpPassword: z.ZodString;
        inviteUser: z.ZodString;
        invitePassword: z.ZodString;
        disableAllEmails: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableOtpEmails: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        disableInviteEmails: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        ignoreErrors: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
        rejectUnauthorized: z.ZodEffects<z.ZodOptional<z.ZodString>, boolean, string | undefined>;
    }, "strip", z.ZodTypeAny, {
        port: number;
        host: string;
        otpUser: string;
        otpPassword: string;
        inviteUser: string;
        invitePassword: string;
        disableAllEmails: boolean;
        disableOtpEmails: boolean;
        disableInviteEmails: boolean;
        ignoreErrors: boolean;
        rejectUnauthorized: boolean;
    }, {
        port: number;
        host: string;
        otpUser: string;
        otpPassword: string;
        inviteUser: string;
        invitePassword: string;
        disableAllEmails?: string | undefined;
        disableOtpEmails?: string | undefined;
        disableInviteEmails?: string | undefined;
        ignoreErrors?: string | undefined;
        rejectUnauthorized?: string | undefined;
    }>;
}, "strip", z.ZodTypeAny, {
    auth: {
        disableRegisterInviteCheck: boolean;
        disableRegisterOtpCheck: boolean;
        disableLoginOtpCheck: boolean;
        otpExpirationTimeMs: number;
    };
    instance: {
        name: string;
        emailDomain: string;
    };
    minio: {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL: boolean;
    };
    email: {
        port: number;
        host: string;
        otpUser: string;
        otpPassword: string;
        inviteUser: string;
        invitePassword: string;
        disableAllEmails: boolean;
        disableOtpEmails: boolean;
        disableInviteEmails: boolean;
        ignoreErrors: boolean;
        rejectUnauthorized: boolean;
    };
}, {
    auth: {
        otpExpirationTimeMs: number;
        disableRegisterInviteCheck?: string | undefined;
        disableRegisterOtpCheck?: string | undefined;
        disableLoginOtpCheck?: string | undefined;
    };
    instance: {
        name: string;
        emailDomain: string;
    };
    minio: {
        endpoint: string;
        port: number;
        accessKey: string;
        secretKey: string;
        useSSL?: string | undefined;
    };
    email: {
        port: number;
        host: string;
        otpUser: string;
        otpPassword: string;
        inviteUser: string;
        invitePassword: string;
        disableAllEmails?: string | undefined;
        disableOtpEmails?: string | undefined;
        disableInviteEmails?: string | undefined;
        ignoreErrors?: string | undefined;
        rejectUnauthorized?: string | undefined;
    };
}>;
export declare class ConfigService implements OnModuleInit {
    private readonly configService;
    config: DeepReadonly<Config>;
    constructor(configService: NestConfigService);
    onModuleInit(): void;
}
