"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorModule = void 0;
const common_1 = require("@nestjs/common");
const rating_module_1 = require("../rating/rating.module");
const minio_module_1 = require("../minio/minio.module");
const reactor_controller_1 = require("./reactor.controller");
const reactor_hub_service_1 = require("./reactor-hub.service");
const reactor_post_service_1 = require("./reactor-post.service");
const reactor_lens_service_1 = require("./lens/reactor-lens.service");
const reactor_comment_service_1 = require("./reactor-comment.service");
const reactor_community_service_1 = require("./reactor-community.service");
let ReactorModule = class ReactorModule {
};
exports.ReactorModule = ReactorModule;
exports.ReactorModule = ReactorModule = __decorate([
    (0, common_1.Module)({
        imports: [rating_module_1.RatingModule, minio_module_1.MinioModule],
        controllers: [reactor_controller_1.ReactorController],
        providers: [
            reactor_post_service_1.ReactorPostService,
            reactor_comment_service_1.ReactorCommentService,
            reactor_lens_service_1.ReactorLensService,
            reactor_hub_service_1.ReactorHubService,
            reactor_community_service_1.ReactorCommunityService,
        ],
        exports: [
            reactor_post_service_1.ReactorPostService,
            reactor_comment_service_1.ReactorCommentService,
            reactor_lens_service_1.ReactorLensService,
            reactor_hub_service_1.ReactorHubService,
            reactor_community_service_1.ReactorCommunityService,
        ],
    })
], ReactorModule);
//# sourceMappingURL=reactor.module.js.map