<script lang="ts">
  import { Common } from "@commune/api";
  import type { Editor as TEditor } from "tinymce";

  import Editor from "./editor.svelte";

  interface Props {
    locale: Common.WebsiteLocale;
    id: string;
    label: string;
    required?: boolean;
    value: Common.Localizations;
    languageSelectPosition?: "top" | "bottom";
    children?: import("svelte").Snippet;
    onEditorInit?: (editor: TEditor) => void;
  }

  const i18n = {
    en: {
      languages: {
        en: "English",
        ru: "Russian",
      },
      providedTranslations: "Provided translations:",
    },
    ru: {
      languages: {
        en: "Английский",
        ru: "Русский",
      },
      providedTranslations: "Указанные переводы:",
    },
  };

  let { value = $bindable(), ...props }: Props = $props();

  const { id, label, required = false, locale, languageSelectPosition = "top", children } = props;

  const t = $derived(i18n[locale]);

  let selectedLocale = $state<Common.LocalizationLocale>(locale);

  function getEditorContent(locale: Common.WebsiteLocale) {
    const localization = value.find((val) => val.locale === locale);

    return localization?.value ?? "";
  }

  let editor: TEditor | null = $state(null);

  // Reactive variable for editor content
  let editorContent = $state("");

  function handleContentChange(newValue: string) {
    if (newValue.length) {
      const localization = value.find((val) => val.locale === selectedLocale);

      if (localization) {
        localization.value = newValue;
      } else {
        value = [...value, { locale: selectedLocale, value: newValue }];
      }
    } else {
      value = value.filter((val) => val.locale !== selectedLocale);
    }

    editorContent = newValue;
  }

  // Switch the selected language
  function handleLanguageChange(locale: Common.WebsiteLocale) {
    editor?.getBody().focus();
    editor?.getBody().blur();

    selectedLocale = locale;
    editorContent = getEditorContent(locale);
  }

  // Get the display name of the current language
  function getLanguageDisplay() {
    return selectedLocale.toUpperCase();
  }

  function onEditorInit(_editor: TEditor) {
    editor = _editor;

    props.onEditorInit?.(_editor);
  }
</script>

<div class="mb-3">
  {#if languageSelectPosition === "top"}
    <div class="d-flex justify-content-between align-items-center mb-2">
      <p class="form-label mb-0">
        {label}
        {#if required}
          <span class="text-danger">*</span>
        {/if}
      </p>
      <div class="dropdown">
        <button
          class="btn btn-outline-secondary btn-sm dropdown-toggle"
          type="button"
          id={`dropdown-${id}`}
          data-bs-toggle="dropdown"
          aria-expanded="false"
          style="width: 60px; display: flex; justify-content: space-between; align-items: center;"
        >
          {getLanguageDisplay()}
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby={`dropdown-${id}`}>
          <li>
            <button
              class="dropdown-item {selectedLocale === 'en' ? 'active' : ''}"
              type="button"
              onclick={() => handleLanguageChange("en")}
            >
              {t.languages.en}
            </button>
          </li>
          <li>
            <button
              class="dropdown-item {selectedLocale === 'ru' ? 'active' : ''}"
              type="button"
              onclick={() => handleLanguageChange("ru")}
            >
              {t.languages.ru}
            </button>
          </li>
        </ul>
      </div>
    </div>
  {:else}
    <p class="form-label mb-2">
      {label}
      {#if required}
        <span class="text-danger">*</span>
      {/if}
    </p>
  {/if}

  <div {id}>
    <Editor {onEditorInit} bind:content={() => editorContent, (v) => handleContentChange(v)} />
  </div>

  {#if languageSelectPosition === "bottom"}
    <div class="d-flex justify-content-between align-items-center mt-2">
      <!-- Children (controls like buttons) on the left -->
      <div class="d-flex align-items-center">
        {#if children}
          {@render children()}
        {/if}
      </div>

      <!-- Language select on the right -->
      <div class="dropdown">
        <button
          class="btn btn-outline-secondary btn-sm dropdown-toggle"
          type="button"
          id={`dropdown-${id}`}
          data-bs-toggle="dropdown"
          aria-expanded="false"
          style="width: 60px; display: flex; justify-content: space-between; align-items: center;"
        >
          {getLanguageDisplay()}
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby={`dropdown-${id}`}>
          <li>
            <button
              class="dropdown-item {selectedLocale === 'en' ? 'active' : ''}"
              type="button"
              onclick={() => handleLanguageChange("en")}
            >
              {t.languages.en}
            </button>
          </li>
          <li>
            <button
              class="dropdown-item {selectedLocale === 'ru' ? 'active' : ''}"
              type="button"
              onclick={() => handleLanguageChange("ru")}
            >
              {t.languages.ru}
            </button>
          </li>
        </ul>
      </div>
    </div>
  {/if}

  {#if value.length > 0}
    <div class="mt-2 small text-muted">
      <div>{t.providedTranslations}</div>
      <ul class="list-unstyled mb-0 mt-1">
        {#each value.filter(Boolean) as val (val.locale)}
          <li class="badge bg-light text-dark me-1">
            {t.languages[val.locale]}
          </li>
        {/each}
      </ul>
    </div>
  {/if}
</div>

<style>
  .form-label {
    cursor: default;
  }
</style>
