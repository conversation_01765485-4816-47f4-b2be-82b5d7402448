import { User } from "@commune/api";
import { Injectable } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class UserNoteService {
    constructor(private readonly prisma: PrismaService) {}

    async getUserNote(input: User.GetUserNoteInput, currentUser: CurrentUser) {
        return await this.prisma.userNote.findUnique({
            where: {
                sourceUserId_targetUserId: {
                    sourceUserId: currentUser.id,
                    targetUserId: input.userId,
                },
            },
        });
    }

    async updateUserNote(
        input: User.UpdateUserNoteInput,
        currentUser: CurrentUser,
    ) {
        if (input.text !== null) {
            await this.prisma.userNote.upsert({
                where: {
                    sourceUserId_targetUserId: {
                        sourceUserId: currentUser.id,
                        targetUserId: input.userId,
                    },
                },
                create: {
                    sourceUserId: currentUser.id,
                    targetUserId: input.userId,
                    text: input.text,
                },
                update: {
                    text: input.text,
                },
            });
        } else {
            await this.prisma.userNote.delete({
                where: {
                    sourceUserId_targetUserId: {
                        sourceUserId: currentUser.id,
                        targetUserId: input.userId,
                    },
                },
            });
        }

        return true;
    }
}
