<script lang="ts">
  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { CreatePostModal } from "$lib/components";
  import PostCard from "./post-card.svelte";
  import RightMenu from "./right-menu.svelte";
  import LeftMenu from "./left-menu.svelte";
  import MobileControls from "./mobile-controls.svelte";

  // i18n
  const i18n = {
    en: {
      _page: {
        title: "Feed — Reactor of Commune",
      },
      createPost: "Create Post",
      noPosts: "No posts found",
      loadingMore: "Loading more posts...",
    },
    ru: {
      _page: {
        title: "Лента — Реактор Коммуны",
      },
      createPost: "Создать пост",
      noPosts: "Посты не найдены",
      loadingMore: "Загружаем больше постов...",
    },
  };

  const { fetcher: api } = getClient();

  type Lens = {
    id: string;
    name: string;
    code: string;
    createdAt: Date;
    updatedAt: Date;
    deletedAt: Date | null;
    userId: string;
    sql: string;
  };

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes - initialize with SSR data
  let posts = $state(data.posts);
  let lenses = $state(data.lenses as Lens[]);
  let currentPage = $state(1);
  let isHasMorePosts = $state(data.isHasMorePosts);
  let isLoadingMore = $state(false);
  let error = $state<string | null>(null);
  let selectedLensId = $state<string | null>(null);

  let isRightMenuExpanded = $state(false);

  let showCreatePostModal = $state(false);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  function openCreatePostModal() {
    showCreatePostModal = true;
  }

  function closeCreatePostModal() {
    showCreatePostModal = false;
  }

  function handlePostCreated() {
    // Optionally refresh the posts list when a new post is created
    refetchPosts();
  }

  async function loadMorePosts() {
    if (isLoadingMore || !isHasMorePosts) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newPosts = await api.reactor.post.list.get({
        pagination: { page: nextPage },
        lensId: selectedLensId,
      });

      posts = [...posts, ...newPosts];
      currentPage = nextPage;

      isHasMorePosts = newPosts.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : "An error occurred while fetching posts";
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  async function refetchPosts() {
    isLoadingMore = true;
    error = null;
    currentPage = 1;

    try {
      const newPosts = await api.reactor.post.list.get({
        pagination: { page: 1 },
        lensId: selectedLensId,
      });

      posts = newPosts;
      isHasMorePosts = newPosts.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : "An error occurred while fetching posts";
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  async function refetchLenses() {
    try {
      lenses = (await api.reactor.lens.list.get()) as Lens[];
    } catch (err) {
      console.error("Error fetching lenses:", err);
    }
  }

  function handleLensChange(lensId: string | null) {
    selectedLensId = lensId;
    refetchPosts();
  }

  function handleLensesUpdated() {
    refetchLenses();
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMorePosts && !isLoadingMore) {
            loadMorePosts();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<!-- Mobile Controls (visible only on mobile) -->
<div class="d-lg-none mt-3">
  <MobileControls
    {locale}
    {lenses}
    {selectedLensId}
    onLensChange={handleLensChange}
    onLensesUpdated={handleLensesUpdated}
    onCreatePost={openCreatePostModal}
  />
</div>

<!-- Desktop Layout (hidden on mobile) -->
<div class="d-none d-lg-block">
  <div class="row g-4 mt-3">
    <div class="col-1"></div>

    <!-- Left Menu (2-3 columns) -->
    <div class="col-2">
      <LeftMenu
        {locale}
        {lenses}
        {selectedLensId}
        onLensChange={handleLensChange}
        onLensesUpdated={handleLensesUpdated}
      />
    </div>

    <!-- Feed (4-9 columns) -->
    <div class="col-6">
      <div class="feed">
        {#if posts.length === 0}
          <div class="text-center py-5">
            <p class="text-muted">{t.noPosts}</p>
          </div>
        {:else}
          {#each posts as post (post.id)}
            <PostCard {post} {locale} {toLocaleHref} {getAppropriateLocalization} />
          {/each}
        {/if}

        <!-- Infinite scroll sentinel element -->
        {#if isHasMorePosts}
          <div bind:this={sentinelElement} class="text-center py-3">
            {#if isLoadingMore}
              <div class="spinner-border spinner-border-sm" role="status">
                <span class="visually-hidden">{t.loadingMore}</span>
              </div>
              <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
            {/if}
          </div>
        {/if}

        {#if error}
          <div class="alert alert-danger" role="alert">
            {error}
          </div>
        {/if}
      </div>
    </div>

    <!-- Right Menu (10-11 columns) -->
    <div class="col-2">
      <!-- Create Post Button -->
      <div
        class="mb-3 create-post-btn-container {isRightMenuExpanded
          ? 'with-right-menu-expanded'
          : ''}"
      >
        <button
          class="btn btn-outline-secondary w-100 create-post-btn"
          onclick={openCreatePostModal}
          aria-label={t.createPost}
        >
          <i class="bi bi-plus-circle me-2"></i>
          {t.createPost}
        </button>
      </div>

      <RightMenu {locale} {toLocaleHref} bind:isExpanded={isRightMenuExpanded} />
    </div>
  </div>
</div>

<!-- Mobile Feed Layout -->
<div class="d-lg-none">
  <div class="mobile-feed">
    {#if posts.length === 0}
      <div class="text-center py-5">
        <p class="text-muted">{t.noPosts}</p>
      </div>
    {:else}
      {#each posts as post (post.id)}
        <PostCard {post} {locale} {toLocaleHref} {getAppropriateLocalization} />
      {/each}
    {/if}

    <!-- Infinite scroll sentinel element -->
    {#if isHasMorePosts}
      <div bind:this={sentinelElement} class="text-center py-3">
        {#if isLoadingMore}
          <div class="spinner-border spinner-border-sm" role="status">
            <span class="visually-hidden">{t.loadingMore}</span>
          </div>
          <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
        {/if}
      </div>
    {/if}

    {#if error}
      <div class="alert alert-danger" role="alert">
        {error}
      </div>
    {/if}
  </div>
</div>

<!-- Create Post Modal -->
<CreatePostModal
  show={showCreatePostModal}
  {locale}
  {toLocaleHref}
  onClose={closeCreatePostModal}
  onPostCreated={handlePostCreated}
/>

<style lang="scss">
  .feed {
    max-width: 100%;
  }

  .mobile-feed {
    max-width: 100%;
    padding: 0 0.75rem;
  }

  .create-post-btn-container {
    opacity: 0.5;

    &:hover,
    &.with-right-menu-expanded {
      opacity: 1;
    }
  }

  .create-post-btn {
    transition: all 0.2s ease;
  }

  .create-post-btn:hover {
    border-color: var(--bs-success);
    color: var(--bs-success);
    background-color: transparent;
  }

  /* Mobile-specific styles */
  @media (max-width: 991.98px) {
    .mobile-feed {
      padding: 0 1rem;
    }
  }

  @media (max-width: 767.98px) {
    .feed {
      margin-top: 1rem;
    }

    .mobile-feed {
      padding: 0 0.5rem;
      margin-top: 0.5rem;
    }

    /* Ensure mobile controls are properly spaced */
    .d-lg-none {
      padding: 0 0.5rem;
    }
  }

  @media (max-width: 576px) {
    .mobile-feed {
      padding: 0 0.25rem;
    }

    .d-lg-none {
      padding: 0 0.25rem;
    }
  }

  /* Touch-friendly interactions for mobile */
  @media (hover: none) and (pointer: coarse) {
    .create-post-btn-container {
      opacity: 1; /* Always visible on touch devices */
    }
  }

  /* Tablet-specific adjustments */
  @media (min-width: 768px) and (max-width: 991.98px) {
    .mobile-feed {
      padding: 0 1.5rem;
    }

    .d-lg-none {
      padding: 0 1.5rem;
    }
  }
</style>
