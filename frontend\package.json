{"name": "frontend", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "production": "node -r dotenv/config server.js"}, "devDependencies": {"@commune/api": "file:*", "@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-node": "^5.2.12", "@sveltejs/kit": "^2.20.8", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@types/express": "^5.0.1", "@types/negotiator": "^0.6.3", "@types/superagent": "^8.1.9", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "sass-embedded": "^1.88.0", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "dependencies": {"@formatjs/intl-localematcher": "^0.6.1", "bootstrap": "^5.3.7", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "negotiator": "^1.0.0", "superagent": "^10.2.1"}}