<script lang="ts">
  import { HttpError } from "@commune/api/acrpc/client";
  import { dev } from "$app/environment";
  import { goto } from "$app/navigation";
  import { page } from "$app/state";
  import { getClient } from "$lib/acrpc";
  import { setCurrentUser, LocalStorageUser } from "$lib";

  const { fetcher: api } = getClient();

  const i18n = {
    en: {
      _page: {
        title: "Sign in — Commune",
      },
      authType: {
        login: "Login",
        register: "Register",
      },
      login: "Log In",
      register: "Create Account",
      email: "Email",
      otp: {
        short: "OTP",
        long: "Email Verification Code",
        send: "Send OTP",
        enterThe6DigitCodeSentToYourEmail: "Enter the 6-digit code sent to your email.",
        sent: "OTP Sent",
        sendingDisabledByServer: "OTP sending is disabled by the server",
        placeholder: "6-digit code",
        invalid: "Invalid OTP",
      },
      failedToSubmitPleaseTryAgain: "Failed to submit. Please try again.",
      userNotFound: "Account not found",
      userAlreadyExists: "Account already exists",
      invite: {
        required: "Invitation Required",
        notInvited: "You are not invited yet, write to one of our chats.",
        telegram: "Telegram",
        telegramLink: "https://t.me/ds_commune_en",
      },
      terms: {
        theLaw: theLawEn,
        rules: rulesEn,
        privacyPolicy: privacyPolicyEn,
      },
    },
    ru: {
      _page: {
        title: "Вход — Коммуна",
      },
      authType: {
        login: "Вход",
        register: "Регистрация",
      },
      login: "Войти",
      register: "Создать аккаунт",
      email: "Email",
      otp: {
        short: "OTP",
        long: "Код проверки почты",
        send: "Отправить OTP",
        enterThe6DigitCodeSentToYourEmail: "Введите 6-значный код, отправленный на ваш email.",
        sent: "OTP отправлен",
        sendingDisabledByServer: "Отправка OTP отключена сервером",
        placeholder: "Шестизначный код",
        invalid: "Неверный OTP",
      },
      failedToSubmitPleaseTryAgain: "Не удалось отправить. Пожалуйста, попробуйте снова.",
      userNotFound: "Аккаунт не найден",
      userAlreadyExists: "Аккаунт уже существует",
      invite: {
        required: "Требуется приглашение",
        notInvited: "Вы ещё не приглашены, напишите в один из наших чатов.",
        telegram: "Telegram",
        telegramLink: "https://t.me/ds_commune_ru",
      },
      terms: {
        theLaw: theLawRu,
        rules: rulesRu,
        privacyPolicy: privacyPolicyRu,
      },
    },
  };

  const { data } = $props();
  const { locale, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);

  const enum OtpStatus {
    None = "none",
    Pending = "pending",
    Sent = "sent",
    SendingDisabledByServer = "sending-disabled-by-server",
    Error = "error",
  }

  const enum SubmitStatus {
    None = "none",
    Pending = "pending",
    Error = "error",
    CustomError = "custom-error",
  }

  let activeTab = $state<"login" | "register">("login");
  let email = $state(dev ? "<EMAIL>" : "");
  let otp = $state(dev ? "123456" : "");
  let otpStatus = $state<OtpStatus>(OtpStatus.None);
  let submitStatus = $state<SubmitStatus>(SubmitStatus.None);
  let submitErrorMessage = $state<string | null>(null);
  let showInviteRequired = $state<boolean>(false);
  let acceptedRightsDocument = $state<boolean>(false);
  let acceptedRulesDocument = $state<boolean>(false);
  let acceptedPrivacyPolicy = $state<boolean>(false);

  const isSubmitting = $derived(
    otpStatus === OtpStatus.Pending || submitStatus === SubmitStatus.Pending,
  );

  // Reset invite required state when switching tabs or changing email
  $effect(() => {
    activeTab; // Track activeTab
    email; // Track email
    showInviteRequired = false;
  });

  function changeActiveTab(tab: "login" | "register") {
    activeTab = tab;
    submitStatus = SubmitStatus.None;
    submitErrorMessage = null;
    // Reset checkboxes when switching tabs
    acceptedRightsDocument = false;
    acceptedRulesDocument = false;
    acceptedPrivacyPolicy = false;
  }

  const handleSendOtp = async (e: Event) => {
    e.preventDefault();

    if (!email) return;

    otpStatus = OtpStatus.Pending;

    try {
      const { isSent } = await api.auth.otp.post({ email }, { skipInterceptor: true });

      otpStatus = isSent ? OtpStatus.Sent : OtpStatus.SendingDisabledByServer;
    } catch (error) {
      console.error("Failed to send OTP:", error);

      otpStatus = OtpStatus.Error;
    }
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();

    if (!email || !otp) return;

    submitStatus = SubmitStatus.Pending;

    try {
      if (activeTab === "login") {
        const user = await api.auth.signIn.post({ email, otp }, { skipInterceptor: true });
        const parsedUser = LocalStorageUser.parse(user);

        setCurrentUser(parsedUser);

        goto(page.url.searchParams.get("redirectFrom") ?? "/");
      } else {
        const user = await api.auth.signUp.post(
          {
            referrerId: null,
            email,
            otp,
          },
          { skipInterceptor: true },
        );

        const parsedUser = LocalStorageUser.parse(user);

        setCurrentUser(parsedUser);

        goto(page.url.searchParams.get("redirectFrom") ?? "/");
      }
    } catch (error) {
      console.error(error);

      if (error instanceof HttpError) {
        // Check if it's an HttpError with 403 status and must_have_invite description
        if (activeTab === "register") {
          if (error.status === 403 && error.description.includes("must_have_invite")) {
            showInviteRequired = true;
            submitStatus = SubmitStatus.None;
            submitErrorMessage = null;
            return;
          }

          if (error.status === 401 && error.description.includes("user_already_exists")) {
            submitStatus = SubmitStatus.CustomError;
            submitErrorMessage = t.userAlreadyExists;
            return;
          }
        }

        if (activeTab === "login") {
          if (error.status === 401) {
            if (error.description.includes("user_not_found")) {
              submitStatus = SubmitStatus.CustomError;
              submitErrorMessage = t.userNotFound;
              return;
            }

            if (error.description.includes("otp_invalid")) {
              submitStatus = SubmitStatus.CustomError;
              submitErrorMessage = t.otp.invalid;
              return;
            }
          }
        }
      }

      // Handle 401 response or other errors
      const errorMessage = (error as any).response?.body?.error || (error as Error).message;

      submitStatus = SubmitStatus.Error;
      submitErrorMessage = errorMessage;
    }
  };
</script>

{#snippet theLawEn()}
  <p>
    I have read the document
    <a href={toLocaleHref("/the-law")} target="_blank">«The Law»</a> — a set of rules for internal
    regulation of interaction in the digital environment of the project. I confirm that I
    understand: these rules apply exclusively within the digital platform,
    <strong>do not cancel my obligations to government agencies</strong>
    and <strong>do not contradict the legislation of the country in which I am located</strong>.
  </p>
{/snippet}

{#snippet theLawRu()}
  <p>
    Я ознакомился с документом
    <a href={toLocaleHref("/the-law")} target="_blank">«Право»</a> — сводом правил внутреннего
    регулирования взаимодействия в цифровой среде проекта. Подтверждаю, что понимаю: данные правила
    действуют исключительно в рамках цифровой платформы,
    <strong>не отменяют моих обязательств перед государственными органами</strong>
    и <strong>не противоречат законодательству страны, на территории которой я нахожусь</strong>.
  </p>
{/snippet}

{#snippet rulesEn()}
  <p>
    I have read the document
    <a href={toLocaleHref("/rules")} target="_blank">«Rules»</a> and voluntarily accept its terms as
    part of the user agreement. I understand that violation of the rules may result in restriction
    of access to the project services, and all my actions on the network remain
    <strong>subject to the requirements of applicable law</strong>.
  </p>
{/snippet}

{#snippet rulesRu()}
  <p>
    Я ознакомился с документом
    <a href={toLocaleHref("/rules")} target="_blank">«Правила»</a> и добровольно принимаю его
    условия как часть пользовательского соглашения. Понимаю, что нарушение правил может повлечь
    ограничение доступа к сервисам проекта, а все мои действия в сети остаются
    <strong>подконтрольными требованиям действующего законодательства</strong>.
  </p>
{/snippet}

{#snippet privacyPolicyEn()}
  <p>
    I have read and agree to the
    <a href={toLocaleHref("/privacy-policy")} target="_blank">Privacy Policy</a> and
    <a href={toLocaleHref("/terms-of-service")} target="_blank">Terms of Service</a>. I understand
    how my personal data will be processed and consent to the collection, use, and storage of my
    information as described in these documents.
  </p>
{/snippet}

{#snippet privacyPolicyRu()}
  <p>
    Я ознакомился и согласен с
    <a href={toLocaleHref("/privacy-policy")} target="_blank">Политикой конфиденциальности</a> и
    <a href={toLocaleHref("/terms-of-service")} target="_blank">Условиями использования</a>. Я
    понимаю, как будут обрабатываться мои персональные данные, и даю согласие на сбор, использование
    и хранение моей информации, как описано в этих документах.
  </p>
{/snippet}

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container min-vh-100 d-flex align-items-center justify-content-center">
  <div class="card shadow-lg border-0" style:width="100%" style:max-width="600px">
    <div class="card-header bg-white border-0 pt-4 pb-0">
      <div class="position-relative">
        <ul class="nav nav-tabs border-0 card-header-tabs">
          <li class="nav-item flex-grow-1 text-center">
            <button
              class={`nav-link border-0 w-100 ${activeTab === "login" ? "active" : ""}`}
              onclick={() => changeActiveTab("login")}
            >
              {t.authType.login}
            </button>
          </li>
          <li class="nav-item flex-grow-1 text-center">
            <button
              class={`nav-link border-0 w-100 ${activeTab === "register" ? "active" : ""}`}
              onclick={() => changeActiveTab("register")}
            >
              {t.authType.register}
            </button>
          </li>
        </ul>
        <div
          class="position-absolute bottom-0 bg-primary"
          style:height="3px"
          style:width="50%"
          style:left={activeTab === "login" ? "0" : "50%"}
          style:transition="left 0.3s ease-in-out"
          style:borderRadius="3px 3px 0 0"
        ></div>
      </div>
    </div>
    <div class="card-body p-4">
      <form onsubmit={handleSubmit}>
        <div class="mb-3">
          <label for="email" class="form-label">{t.email}</label>
          <input
            type="email"
            autoComplete="email"
            class="form-control"
            id="email"
            value={email}
            oninput={(e) => (email = (e.target as HTMLInputElement).value)}
            placeholder="<EMAIL>"
            required
          />
        </div>
        <div class="mb-3">
          <button
            type="button"
            class="btn btn-outline-primary w-100"
            onclick={handleSendOtp}
            disabled={!email || isSubmitting}
          >
            {#if otpStatus === OtpStatus.Pending}
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"
              ></span>
            {/if}

            {t.otp.send}
          </button>
        </div>
        <div class="mb-3">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <label for="otp" class="form-label mb-0">{t.otp.long}</label>

            {#if otpStatus === OtpStatus.Sent}
              <span class="badge bg-success-subtle text-success px-2 py-1 rounded-pill">
                {t.otp.sent}
              </span>
            {:else if otpStatus === OtpStatus.SendingDisabledByServer}
              <span class="badge bg-warning-subtle text-warning px-2 py-1 rounded-pill">
                {t.otp.sendingDisabledByServer}
              </span>
            {/if}
          </div>
          <input
            id="otp"
            class="form-control"
            type="text"
            value={otp}
            oninput={(e) => {
              // Only allow numbers and limit to 6 digits
              const value = (e.target as HTMLInputElement).value.replace(/\D/g, "");
              if (value.length <= 6) otp = value;
            }}
            placeholder={t.otp.placeholder}
            maxLength={6}
            aria-describedby="otpHelp"
          />
          <div id="otpHelp" class="form-text">{t.otp.enterThe6DigitCodeSentToYourEmail}</div>
        </div>

        {#if activeTab === "register"}
          <div class="mb-3">
            <div class="form-check mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="rightsDocument"
                bind:checked={acceptedRightsDocument}
                required
              />
              <label class="form-check-label" for="rightsDocument">
                {@render t.terms.theLaw()}
              </label>
            </div>
            <div class="form-check mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="rulesDocument"
                bind:checked={acceptedRulesDocument}
                required
              />
              <label class="form-check-label" for="rulesDocument">
                {@render t.terms.rules()}
              </label>
            </div>
            <div class="form-check mb-3">
              <input
                class="form-check-input"
                type="checkbox"
                id="privacyPolicy"
                bind:checked={acceptedPrivacyPolicy}
                required
              />
              <label class="form-check-label" for="privacyPolicy">
                {@render t.terms.privacyPolicy()}
              </label>
            </div>
          </div>
        {/if}

        <div class="d-grid gap-2">
          <button
            type="submit"
            class="btn btn-primary"
            disabled={!email ||
              !otp ||
              isSubmitting ||
              (activeTab === "register" &&
                (!acceptedRightsDocument || !acceptedRulesDocument || !acceptedPrivacyPolicy))}
          >
            {#if submitStatus === SubmitStatus.Pending && otp}
              <span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"
              ></span>
            {/if}

            {#if activeTab === "login"}
              {t.login}
            {:else}
              {t.register}
            {/if}
          </button>

          {#if submitStatus === SubmitStatus.Error}
            <span class="text-danger">
              {t.failedToSubmitPleaseTryAgain}
              <br />
              {submitErrorMessage}
            </span>
          {:else if submitStatus === SubmitStatus.CustomError}
            <span class="text-danger">
              {submitErrorMessage}
            </span>
          {/if}
        </div>
      </form>

      {#if showInviteRequired}
        <div class="mt-4 p-3 border rounded bg-light">
          <h6 class="text-warning mb-3">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            {t.invite.required}
          </h6>
          <p class="mb-3 text-muted">{t.invite.notInvited}</p>
          <div class="d-flex gap-2">
            <a
              href={t.invite.telegramLink}
              target="_blank"
              rel="noopener noreferrer"
              class="btn btn-outline-primary btn-sm"
            >
              <i class="bi bi-telegram me-1"></i>
              {t.invite.telegram}
            </a>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .nav-link {
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    color: #6c757d;
    transition: all 0.3s ease;
  }

  .nav-link.active {
    color: #0d6efd;
    font-weight: 600;
    background-color: transparent;
  }

  .nav-link:hover:not(.active) {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.05);
  }
</style>
