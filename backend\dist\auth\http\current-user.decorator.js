"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpCurrentUser = void 0;
const common_1 = require("@nestjs/common");
const consts_1 = require("../../consts");
const client_1 = require("@prisma/client");
exports.HttpCurrentUser = (0, common_1.createParamDecorator)((_, ctx) => {
    const request = ctx.switchToHttp().getRequest();
    const rawUser = request.user;
    if (!rawUser) {
        throw new common_1.UnauthorizedException();
    }
    const currentUser = {
        ...rawUser,
        isAdmin: rawUser.role === client_1.UserRole.admin,
    };
    request[consts_1.HTTP_CONTEXT_USER_SYMBOL] = currentUser;
    return currentUser;
});
//# sourceMappingURL=current-user.decorator.js.map