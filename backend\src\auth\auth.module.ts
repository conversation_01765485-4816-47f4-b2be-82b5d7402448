import { <PERSON>du<PERSON> } from "@nestjs/common";
import { SessionStrategy } from "./session.strategy";
import { UserModule } from "src/user/user.module";
import { EmailModule } from "src/email/email.module";
import { AuthService } from "./auth.service";
import { AuthController } from "./http/auth.controller";
import { AdminModule } from "src/admin/admin.module";

@Module({
    imports: [EmailModule, UserModule, AdminModule],
    controllers: [AuthController],
    providers: [AuthService, SessionStrategy],
})
export class AuthModule {}
