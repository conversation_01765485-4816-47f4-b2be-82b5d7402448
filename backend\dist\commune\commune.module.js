"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneModule = void 0;
const common_1 = require("@nestjs/common");
const user_module_1 = require("../user/user.module");
const minio_module_1 = require("../minio/minio.module");
const commune_core_1 = require("./commune.core");
const commune_service_1 = require("./commune.service");
const commune_controller_1 = require("./commune.controller");
const commune_member_service_1 = require("./commune-member.service");
const commune_invitation_service_1 = require("./commune-invitation.service");
const commune_join_request_service_1 = require("./commune-join-request.service");
let CommuneModule = class CommuneModule {
};
exports.CommuneModule = CommuneModule;
exports.CommuneModule = CommuneModule = __decorate([
    (0, common_1.Module)({
        imports: [user_module_1.UserModule, minio_module_1.MinioModule],
        controllers: [commune_controller_1.CommuneController],
        providers: [
            commune_core_1.CommuneCore,
            commune_service_1.CommuneService,
            commune_member_service_1.CommuneMemberService,
            commune_invitation_service_1.CommuneInvitationService,
            commune_join_request_service_1.CommuneJoinRequestService,
        ],
        exports: [
            commune_core_1.CommuneCore,
            commune_service_1.CommuneService,
            commune_member_service_1.CommuneMemberService,
            commune_invitation_service_1.CommuneInvitationService,
            commune_join_request_service_1.CommuneJoinRequestService,
        ],
    })
], CommuneModule);
//# sourceMappingURL=commune.module.js.map