import type { LayoutLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { getClient } from "$lib/acrpc";

export const load: LayoutLoad = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();

  const [
    me,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
  ]);

  // Check if user is admin
  if (me.role !== "admin") {
    throw error(403, "Access denied: Admin privileges required");
  }

  return {
    me,
  };
};
