<script lang="ts">
  import { onMount } from "svelte";
  import { LocaleSwitcher } from "$lib/components";

  const i18n = {
    ru: {
      navGap: "mx-1",
      commune: "Коммуна",
      feed: "Лента",
      hubs: "Хабы",
      communities: "Сообщества",
      profile: "Профиль",
    },

    en: {
      navGap: "mx-2",
      commune: "Commune",
      feed: "Feed",
      hubs: "Hubs",
      communities: "Communities",
      profile: "Profile",
    },
  };

  const { children, data } = $props();
  const { locale, routeLocale, toLocaleHref } = $derived(data);

  const t = $derived(i18n[locale]);

  // Function to close the navbar collapse
  function closeNavbar() {
    const navbarCollapse = document.getElementById("navbarNav");
    const navbarToggler = document.querySelector(".navbar-toggler") as HTMLElement;

    if (navbarCollapse && navbarToggler) {
      // Check if the navbar is currently expanded
      const isExpanded = navbarCollapse.classList.contains("show");

      if (isExpanded) {
        // Use Bootstrap's collapse method to hide the navbar
        const bootstrap = (window as any).bootstrap;
        if (bootstrap && bootstrap.Collapse) {
          const bsCollapse =
            bootstrap.Collapse.getInstance(navbarCollapse) ||
            new bootstrap.Collapse(navbarCollapse, { toggle: false });
          bsCollapse.hide();
        }
      }
    }
  }

  // Setup event listeners for nav links
  onMount(() => {
    const navLinks = document.querySelectorAll(".reactor-navbar .nav-link");

    navLinks.forEach((link) => {
      link.addEventListener("click", closeNavbar);
    });

    // Cleanup event listeners
    return () => {
      navLinks.forEach((link) => {
        link.removeEventListener("click", closeNavbar);
      });
    };
  });
</script>

<div class="page-wrapper">
  <nav class="navbar navbar-expand-lg sticky-top reactor-navbar">
    <div class="container">
      <a href={toLocaleHref("/reactor")} class="navbar-brand py-0 ps-5">
        <img
          src="/images/full-v3-transparent-white.svg"
          alt="Site Logo"
          height={60}
          width={60}
          style:width="auto"
        />
      </a>
      <button
        class="navbar-toggler"
        type="button"
        data-bs-toggle="collapse"
        data-bs-target="#navbarNav"
        aria-controls="navbarNav"
        aria-expanded="false"
        aria-label="Toggle navigation"
      >
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav mx-auto">
          <!-- Commune Dropdown -->
          <li class="nav-item dropdown {t.navGap} text-nowrap">
            <!-- <a
            class="nav-link dropdown-toggle"
            href={toLocaleHref("/reactor")}
            id="communeDropdown"
            role="button"
            data-bs-toggle="dropdown"
            aria-expanded="false"
          >
            {t.commune}
          </a>
          <ul class="dropdown-menu" aria-labelledby="communeDropdown">
            <li><a class="dropdown-item" href={toLocaleHref("/the-law")}>{t.theLaw}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/rules")}>{t.rules}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/new-english")}>{t.newEnglish}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/new-calendar")}>{t.newCalendar}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/news")}>{t.news}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/people")}>{t.users}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/communes")}>{t.communes}</a></li>
            <li><a class="dropdown-item" href={toLocaleHref("/profile")}>{t.profile}</a></li>
          </ul> -->

            <a href={toLocaleHref("/")} class="nav-link">{t.commune}</a>
          </li>

          <!-- Reactor Links -->
          <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor")} class="nav-link">{t.feed}</a>
          </li>
          <!-- <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor/hole")} class="nav-link">{t.hole}</a>
          </li> -->
          <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor/hubs")} class="nav-link">{t.hubs}</a>
          </li>
          <li class={`nav-item ${t.navGap} text-nowrap`}>
            <a href={toLocaleHref("/reactor/communities")} class="nav-link">{t.communities}</a>
          </li>
        </ul>
        <ul class="navbar-nav d-flex align-items-center" style:position="relative">
          <li class="nav-item me-2">
            <a href={toLocaleHref("/profile")} class="btn btn-sm profile-btn">
              {t.profile}
            </a>
          </li>
          <li class="nav-item">
            <LocaleSwitcher currentLocale={routeLocale} />
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <main class="container-fluid flex-grow-1 mb-5 reactor-container">
    {@render children()}
  </main>
</div>

<style>
  .page-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  .navbar {
    padding: 0;
  }

  .reactor-navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 1030;
    backdrop-filter: blur(5px);
    background-color: rgba(28, 29, 29, 0.95) !important;
  }

  .reactor-navbar .nav-link {
    font-size: 1.05rem;
    padding: 0.7rem 1rem !important;
    transition: color 0.2s ease;
    color: #e9ecef; /* Light color for dark background */
    font-weight: 500;
    display: block;
  }

  .reactor-navbar .nav-link:hover {
    color: #0d6efd;
  }

  .reactor-navbar .nav-link.active {
    color: #0d6efd;
  }

  .reactor-navbar .navbar-brand {
    display: flex;
    align-items: center;
  }

  .reactor-navbar .navbar-collapse {
    justify-content: space-between;
  }

  .reactor-navbar .navbar-nav.mx-auto {
    display: flex;
    justify-content: center;
  }

  .reactor-navbar .nav-item {
    display: flex;
    align-items: center;
  }

  .reactor-navbar .dropdown-menu {
    background-color: rgba(33, 37, 41, 0.95);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .reactor-navbar :global(.btn-sm) {
    font-size: 0.95rem;
    padding: 0.5rem 1.1rem;
    font-weight: 500;
    display: inline-block;
  }

  .reactor-navbar .profile-btn {
    background-color: #000000 !important;
    color: #ffffff !important;
    border: 1px solid #ffffff !important;
    transition: all 0.2s ease;
  }

  .reactor-navbar .profile-btn:hover {
    background-color: #333333 !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
  }

  .reactor-navbar .dropdown-item {
    color: #e9ecef;
  }

  .reactor-navbar .dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  .reactor-container {
    max-width: 1400px;
    margin: 0 auto;
  }

  /* Mobile container optimizations */
  @media (max-width: 767.98px) {
    .reactor-container {
      padding-left: 0.75rem;
      padding-right: 0.75rem;
    }
  }

  /* Mobile navbar improvements */
  .reactor-navbar .navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
    font-size: 1.25rem;
  }

  .reactor-navbar .navbar-toggler:focus {
    box-shadow: none;
  }

  .reactor-navbar .navbar-toggler-icon {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28233, 236, 239, 0.85%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  }

  @media (max-width: 991.98px) {
    .reactor-navbar .navbar-collapse {
      padding: 1rem 0;
      background-color: rgba(28, 29, 29, 0.98);
      margin: 0 -1rem;
      border-radius: 0 0 0.5rem 0.5rem;
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .reactor-navbar .navbar-nav {
      text-align: center;
      margin-bottom: 0.5rem;
      padding: 0 1rem;
    }

    .reactor-navbar .nav-item {
      margin: 0.3rem 0 !important;
      width: 100%;
    }

    .reactor-navbar .nav-link {
      padding: 0.75rem 1rem !important;
      border-radius: 0.375rem;
      margin: 0.1rem 0;
      transition: all 0.2s ease;
    }

    .reactor-navbar .nav-link:hover {
      background-color: rgba(13, 110, 253, 0.1);
    }

    .reactor-navbar .navbar-nav.mx-auto {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    .reactor-navbar .navbar-brand {
      padding-left: 1rem !important;
    }

    /* Ensure proper spacing for locale switcher on mobile */
    .reactor-navbar .navbar-nav:last-child {
      border-top: 1px solid rgba(255, 255, 255, 0.1);
      padding-top: 0.5rem;
      margin-top: 0.5rem;
    }
  }

  /* Tablet improvements */
  @media (max-width: 1199.98px) and (min-width: 992px) {
    .reactor-navbar .nav-link {
      font-size: 0.95rem;
      padding: 0.6rem 0.8rem !important;
    }
  }
</style>
