// @ts-nocheck
import type { Commune } from "@commune/api";
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export type InvitationWithDetails = Commune.GetCommuneInvitationsOutput[number] & {
  commune: Commune.GetCommunesOutput[number];
}

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });

  const communes = invitations.length
    ? await api.commune.list.get(
      { ids: invitations.map(({ communeId }) => communeId) },
      { fetch, ctx: { url } },
    )
    : [];
  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));

  const invitationsWithDetails = invitations.map<InvitationWithDetails>((invitation) => ({
    ...invitation,
    commune: communeMap.get(invitation.communeId)!,
  }));

  return {
    invitations: invitationsWithDetails,
    isHasMoreInvitations: invitations.length === Consts.PAGE_SIZE,
  };
};
