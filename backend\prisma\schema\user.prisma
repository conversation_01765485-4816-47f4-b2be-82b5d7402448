// user-role
enum UserRole {
    @@map("user_role")

    admin
    moderator
    user
}

enum UserAlignmentSystemType {
    @@map("user_alignment_system_type")

    lawfulGood
    neutralGood
    chaoticGood
    lawfulNeutral
    trueNeutral
    chaoticNeutral
    lawfulEvil
    neutralEvil
    chaoticEvil
}

// user
model User {
    @@map("users")

    id String @id @default(nanoid())

    referrerId String? @map("referrer_id")
    referrer   User?   @relation("referrer", fields: [referrerId], references: [id])
    referrals  User[]  @relation("referrer")

    email String @unique

    role UserRole @default(user)

    imageId String? @map("image_id")
    image   Image?  @relation("user_image", fields: [imageId], references: [id])

    alignmentSystemType UserAlignmentSystemType? @map("alignment_system_type")

    name        Localization[] @relation("user_name")
    description Localization[] @relation("user_description")

    titles UserTitle[] @relation("user_titles")

    communeInvitations  CommuneInvitation[]  @relation("commune_invitations")
    communeJoinRequests CommuneJoinRequest[] @relation("commune_join_requests")

    reactorPosts       ReactorPost[]       @relation("user_reactor_posts")
    reactorComments    ReactorComment[]    @relation("user_reactor_comments")
    reactorRatings     ReactorRating[]     @relation("user_reactor_ratings")
    reactorUsefulness  ReactorUsefulness[] @relation("user_reactor_usefulnesses")
    reactorHubs        ReactorHub[]        @relation("user_reactor_hubs")
    reactorCommunities ReactorCommunity[]  @relation("user_reactor_communities")
    reactorLenses      ReactorLens[]       @relation("user_reactor_lenses")

    userRatingSourceUser UserRating[] @relation("user_rating_source_user_user")
    userRatingTargetUser UserRating[] @relation("user_rating_target_user_user")

    userKarmaGivenPointSourceUser UserKarmaGivenPoint[] @relation("user_karma_given_point_source_user_user")
    userKarmaGivenPointTargetUser UserKarmaGivenPoint[] @relation("user_karma_given_point_target_user_user")

    userFeedbackSourceUser UserFeedback[] @relation("user_feedback_source_user_user")
    userFeedbackTargetUser UserFeedback[] @relation("user_feedback_target_user_user")

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// user-title
model UserTitle {
    @@map("user_titles")

    id String @id @default(nanoid())

    userId String @map("user_id")
    user   User   @relation("user_titles", fields: [userId], references: [id])

    name Localization[] @relation("user_title_name")

    isActive Boolean @map("is_active") @default(false)
    color    String?

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

// user-otp
model UserOtp {
    @@map("user_otps")

    id String @id @default(nanoid())

    email String @map("email")

    otp String

    ipAddress String? @map("ip_address")
    userAgent String? @map("user_agent")

    expiresAt DateTime @map("expires_at") @db.Timestamptz(3)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}

model UserNote {
    @@map("user_notes")

    id String @id @default(nanoid())

    sourceUserId String @map("source_user_id")
    targetUserId String @map("target_user_id")

    text String

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt

    @@unique([sourceUserId, targetUserId])
}

model UserInvite {
    @@map("user_invites")

    id String @id @default(nanoid())

    email String  @unique
    name  String?

    locale Locale

    isUsed Boolean @map("is_used") @default(false)

    createdAt DateTime @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime @map("updated_at") @db.Timestamptz(3) @updatedAt
}
