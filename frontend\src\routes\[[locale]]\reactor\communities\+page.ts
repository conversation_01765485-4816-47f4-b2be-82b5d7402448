import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();

  const searchQuery = url.searchParams.get("search");

  const [
    me,
    communities,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.reactor.community.list.get({ query: searchQuery ?? undefined }, { fetch, ctx: { url } }),
  ]);

  return {
    me,
    communities,
    searchQuery,
    isHasMoreCommunities: communities.length === Consts.PAGE_SIZE,
  };
};
