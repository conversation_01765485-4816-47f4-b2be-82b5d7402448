"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.errors = void 0;
exports.getError = getError;
exports.errors = {
    user_not_found: "User not found",
    user_email_is_busy: "User email is busy",
    refresh_token_invalid: "Refresh token is invalid",
    otp_invalid: "OTP is invalid",
    email_already_exists: "Email already taken",
    post_not_found: "Post not found",
    must_be_admin: "Must be an admin to perform this action",
    must_have_invite: "You must have an invite to register",
    must_have_at_least_one_spendable_point: "You must have at least one spendable karma point",
};
function getError(errorCode, additionalInfo) {
    const error = exports.errors[errorCode];
    const errorMessage = error ?? errorCode;
    return [
        errorCode,
        errorMessage + (additionalInfo ? ` (${additionalInfo})` : ""),
    ];
}
//# sourceMappingURL=errors.js.map