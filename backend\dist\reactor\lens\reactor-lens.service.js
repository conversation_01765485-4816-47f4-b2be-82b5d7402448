"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ReactorLensService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReactorLensService = void 0;
const common_1 = require("@nestjs/common");
const errors_1 = require("../../common/errors");
const prisma_service_1 = require("../../prisma/prisma.service");
const functions_1 = require("./functions");
let ReactorLensService = ReactorLensService_1 = class ReactorLensService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(ReactorLensService_1.name);
    }
    generateSql(code) {
        const { tokens } = (0, functions_1.tokenize)(code);
        const ast = (0, functions_1.createAst)(tokens);
        const statement = (0, functions_1.validate)(ast);
        const sql = (0, functions_1.generateSql)(statement);
        return sql;
    }
    async getLenses(user) {
        return await this.prisma.reactorLens.findMany({
            where: {
                userId: user.id,
                deletedAt: null,
            },
        });
    }
    generateLensSql(code) {
        try {
            return this.generateSql(code);
        }
        catch (error) {
            this.logger.error(error);
            throw new common_1.BadRequestException(String(error));
        }
    }
    async createLens(dto, user) {
        const sql = this.generateLensSql(dto.code);
        const lens = await this.prisma.reactorLens.create({
            data: {
                userId: user.id,
                name: dto.name,
                code: dto.code,
                sql,
            },
        });
        return { id: lens.id };
    }
    async updateLens(input, user) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });
        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        const data = Object.assign({}, input.name ? { name: input.name } : undefined, input.code
            ? { code: input.code, sql: this.generateLensSql(input.code) }
            : null);
        await this.prisma.reactorLens.update({
            where: { id: input.id },
            data,
        });
        return true;
    }
    async deleteLens(input, user) {
        const lens = await this.prisma.reactorLens.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });
        if (user.role !== "admin" && lens.userId !== user.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_author"));
        }
        await this.prisma.reactorLens.update({
            where: { id: input.id },
            data: {
                deletedAt: new Date(),
            },
        });
    }
};
exports.ReactorLensService = ReactorLensService;
exports.ReactorLensService = ReactorLensService = ReactorLensService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ReactorLensService);
//# sourceMappingURL=reactor-lens.service.js.map