import type { Commune, User } from "@commune/api";
import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export type JoinRequestWithDetails = Commune.GetCommuneJoinRequestsOutput[number] & {
  user: User.GetUsersOutput[number];
}

export const load: PageLoad = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();

  const [
    user,
    [commune],
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
  ]);

  if (!commune) {
    throw error(404, "Commune not found");
  }
  
  // Check if user has permission to view join requests (admin or head member)
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view join requests");
  }

  // Fetch join requests for this commune
  const joinRequests = await api.commune.joinRequest.list.get(
    { communeId: params.id },
    { fetch, ctx: { url } },
  );

  const users = joinRequests.length
    ? await api.user.list.get(
      { ids: joinRequests.map(({ userId }) => userId) },
      { fetch, ctx: { url } },
    )
    : [];
  const userMap = new Map(users.map((user) => [user.id, user]));

  const joinRequestsWithUserDetails = joinRequests.map<JoinRequestWithDetails>((joinRequest) => ({
    ...joinRequest,
    user: userMap.get(joinRequest.userId)!,
  }));

  return {
    commune,
    joinRequests: joinRequestsWithUserDetails,
    isHasMoreJoinRequests: joinRequests.length === Consts.PAGE_SIZE,
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageJoinRequests: isAdmin || isHeadMember,
    },
  };
};
