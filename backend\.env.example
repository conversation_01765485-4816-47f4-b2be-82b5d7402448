# Database Configuration
DATABASE_URL = "******************************************/commune"

# MinIO Configuration
MINIO_ENDPOINT = "localhost"
MINIO_PORT = "9000"
MINIO_ACCESS_KEY = "admin"
MINIO_SECRET_KEY = "password"
MINIO_USE_SSL = "false"

# Instance Configuration
INSTANCE_NAME = "Commune Dev (local)"
INSTANCE_EMAIL_DOMAIN = "dev.commune.my"

# Session Configuration
SESSION_SECRET = "session_secret_very_long_key_change_in_production"

# Email Configuration
EMAIL_HOST = "mail.dev.commune.my"
EMAIL_PORT = "587"

EMAIL_OTP_USER = "<EMAIL>"
EMAIL_OTP_PASSWORD = "12345678"

EMAIL_INVITE_USER = "<EMAIL>"
EMAIL_INVITE_PASSWORD = "12345678"

OTP_EXPIRATION_TIME_MS = "600000"

# Dev Mode Configuration
DISABLE_LOGIN_OTP_CHECK = "1"
DISABLE_REGISTER_OTP_CHECK = "1"
DISABLE_REGISTER_INVITE_CHECK = "1"

EMAIL_REJECT_UNAUTHORIZED = ""
IGNORE_EMAIL_ERRORS = ""
DISABLE_OTP_EMAIL = ""
DISABLE_ALL_EMAILS = ""
DISABLE_OTP_EMAILS = ""
DISABLE_INVITE_EMAILS = ""
