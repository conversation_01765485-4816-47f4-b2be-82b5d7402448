# Backend Dockerfile
FROM node:18-alpine AS base

# Install curl for health checks
RUN apk add --no-cache curl

# Install dependencies only when needed
FROM base AS deps
WORKDIR /app

# Copy root package files and workspace configuration
COPY package*.json ./
COPY tsconfig.json ./
COPY libs/api/package.json ./libs/api/
COPY libs/api/src/ ./libs/api/src/
COPY libs/api/tsconfig.json ./libs/api/
COPY libs/api/tsup.config.ts ./libs/api/
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# Install all workspace dependencies
RUN npm ci --only=production && npm cache clean --force

# Build stage
FROM base AS builder
WORKDIR /app

# Copy root package files and workspace configuration
COPY package*.json ./
COPY tsconfig.json ./
COPY libs/api/package.json ./libs/api/
COPY libs/api/src/ ./libs/api/src/
COPY libs/api/tsconfig.json ./libs/api/
COPY libs/api/tsup.config.ts ./libs/api/
COPY backend/package*.json ./backend/
COPY frontend/package*.json ./frontend/

# Install all dependencies (including dev dependencies)
RUN npm ci

# Build libs first
RUN npm run api:build

# Copy backend source code (excluding node_modules and build artifacts)
COPY backend/src/ ./backend/src/
COPY backend/prisma/ ./backend/prisma/
COPY backend/nest-cli.json ./backend/
COPY backend/tsconfig*.json ./backend/

# Generate Prisma client
WORKDIR /app/backend
RUN npx prisma generate

# Build the application
RUN npm run build

# Production stage
FROM base AS runner
WORKDIR /app

# Create a non-root user
RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nestjs

# Copy built application
COPY --from=builder --chown=nestjs:nodejs /app/backend/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/backend/package*.json ./
COPY --from=builder --chown=nestjs:nodejs /app/backend/prisma ./prisma
COPY --from=builder --chown=nestjs:nodejs /app/libs ./libs

# Create sessions directory
RUN mkdir -p .sessions && chown nestjs:nodejs .sessions

USER nestjs

# Expose port
EXPOSE 4000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:4000 || exit 1

# Start the application
CMD ["npm", "run", "start:deploy"]
