import { Common, User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
export declare class UserTitleService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    getUserTitles(input: User.GetUserTitlesInput, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
        }[];
        userId: string;
        isActive: boolean;
        color: string | null;
    }[]>;
    createUserTitle(input: User.CreateUserTitleInput, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        isActive: boolean;
        color: string | null;
    }>;
    updateUserTitle(input: User.UpdateUserTitleInput, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        isActive: boolean;
        color: string | null;
    }>;
    deleteUserTitle(input: Common.ObjectWithId, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        isActive: boolean;
        color: string | null;
    }>;
}
