"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SitemapService = void 0;
const common_1 = require("@nestjs/common");
const commune_service_1 = require("../commune/commune.service");
const reactor_hub_service_1 = require("../reactor/reactor-hub.service");
const reactor_post_service_1 = require("../reactor/reactor-post.service");
const reactor_community_service_1 = require("../reactor/reactor-community.service");
const SITEMAP_KEY = "BycS9tEQKMvasaJGni12BUBVl5xl1E7fsGqocvG5xKe5es3XWlrMXeRGYvJ7r3iS";
let SitemapService = class SitemapService {
    constructor(communeService, reactorPostService, reactorHubService, reactorCommunityService) {
        this.communeService = communeService;
        this.reactorPostService = reactorPostService;
        this.reactorHubService = reactorHubService;
        this.reactorCommunityService = reactorCommunityService;
    }
    async getSitemapGenerationData(key) {
        if (key !== SITEMAP_KEY) {
            throw new common_1.ForbiddenException();
        }
        const [communeIds, reactorPostIds, reactorHubIds, reactorCommunityIds] = await Promise.all([
            this.communeService.getCommuneIds(),
            this.reactorPostService.getPostIds(),
            this.reactorHubService.getHubIds(),
            this.reactorCommunityService.getCommunityIds(),
        ]);
        return {
            communeIds,
            reactorPostIds,
            reactorHubIds,
            reactorCommunityIds,
        };
    }
};
exports.SitemapService = SitemapService;
exports.SitemapService = SitemapService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [commune_service_1.CommuneService,
        reactor_post_service_1.ReactorPostService,
        reactor_hub_service_1.ReactorHubService,
        reactor_community_service_1.ReactorCommunityService])
], SitemapService);
//# sourceMappingURL=sitemap.service.js.map