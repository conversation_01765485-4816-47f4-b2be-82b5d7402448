import { z } from "zod";

import { Common, User } from "@commune/api";
import { browser } from "$app/environment";

export type LocalStorageUser = z.infer<typeof LocalStorageUser>;
export const LocalStorageUser = z.object({
  id: Common.id,
  email: Common.email,
  role: User.UserRoleSchema,
});

export function getCurrentUser(): LocalStorageUser | null {
  if (!browser) {
    return null;
  }

  const maybeUser = localStorage.getItem("user");

  if (maybeUser) {
    try {
      const jsonUser = JSON.parse(maybeUser);

      const user = LocalStorageUser.safeParse(jsonUser);

      if (user.success) {
        return user.data;
      }

      console.error("Invalid user in localStorage", user.error);
    }
    catch (error) {
      console.error("Error parsing user from localStorage", error);
    }
  }

  removeCurrentUser();

  return null;
}

export function setCurrentUser(user: LocalStorageUser) {
  if (!browser) {
    return;
  }

  localStorage.setItem("user", JSON.stringify(user));
}

export function removeCurrentUser() {
  if (!browser) {
    return;
  }

  localStorage.removeItem("user");
}
