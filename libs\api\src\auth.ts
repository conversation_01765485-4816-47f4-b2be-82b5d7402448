import type { Infer } from "./types";

import { z } from "zod";
import { email, id, imageUrl } from "./common";
import { userDescription, userName, UserRoleSchema } from "./user";

export const otp = z.string().nonempty().length(6);

export type SendOtpInput = Infer<typeof SendOtpInputSchema>;
export const SendOtpInputSchema = z.object({
    email,
});

export type SendOtpOutput = Infer<typeof SendOtpOutputSchema>;
export const SendOtpOutputSchema = z.object({
    isSent: z.boolean(),
});

export type SignupInput = Infer<typeof SignupInputSchema>;
export const SignupInputSchema = z.object({
    referrerId: id.nullable(),
    email,
    otp,
});

export type SigninInput = Infer<typeof SigninInputSchema>;
export const SigninInputSchema = z.object({
    email,
    otp,
});

export type SuccessfulOutput = Infer<typeof SuccessfulOutputSchema>;
export const SuccessfulOutputSchema = z.object({
    id,
    email,
    role: UserRoleSchema,
});
