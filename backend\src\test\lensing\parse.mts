/*
Example:

(
    (
        hub = ["guid", "guid", "guid"]
        && group = ["guid", "guid"]
    )
    || (
        hub = ["guid"]
        && group != ["guid", "guid"]
        && author != ["guid"]
    )
)
&& rating >= 200
&& usefulness >= 8
&& difficulty = ["easy", "medium"]
&& duration < 10
&& age < 3d
&& tag != ["guid", "guid"]
&& title ~ "python"
&& body ~ "pandas"

and:
    or:
        and:
            hub = ["guid", "guid", "guid"]
            group = ["guid", "guid"]
        or:
            hub = ["guid"]
            group != ["guid", "guid"]
            author != ["guid"]
    rating >= 200
    usefulness >= 8
    difficulty = ["easy", "medium"]
    duration < 10
    age < 3d
    tag != ["guid", "guid"]
    title ~ "python"
    body ~ "pandas"
*/

import { createToken, Lexer } from "chevrotain";

const Identifier = createToken({
    name: "Identifier",
    pattern: new RegExp(
        [
            "hub",
            "group",
            "author",
            "rating",
            "usefulness",
            "tag",
            "difficulty",
            "title",
            "body",
            "duration",
            "age",
        ].join("|"),
    ),
});

const Whitespace = createToken({
    name: "Whitespace",
    pattern: /\s+/,
    group: Lexer.SKIPPED,
});

const And = createToken({
    name: "And",
    pattern: "&&",
});
const Or = createToken({
    name: "Or",
    pattern: "||",
});

const Equals = createToken({
    name: "Equals",
    pattern: "=",
});
const NotEquals = createToken({
    name: "NotEquals",
    pattern: "!=",
});

const GreaterThan = createToken({
    name: "GreaterThan",
    pattern: ">",
});
const GreaterThanOrEqual = createToken({
    name: "GreaterThanOrEqual",
    pattern: ">=",
});

const LessThan = createToken({
    name: "LessThan",
    pattern: "<",
});
const LessThanOrEqual = createToken({
    name: "LessThanOrEqual",
    pattern: "<=",
});

const Like = createToken({
    name: "Like",
    pattern: "~",
});

const LeftParen = createToken({
    name: "LeftParen",
    pattern: "(",
});
const RightParen = createToken({
    name: "RightParen",
    pattern: ")",
});

const LeftSquareBracket = createToken({
    name: "LeftSquareBracket",
    pattern: "[",
});
const RightSquareBracket = createToken({
    name: "RightSquareBracket",
    pattern: "]",
});

const Comma = createToken({
    name: "Comma",
    pattern: ",",
});

const Integer = createToken({
    name: "Integer",
    pattern: /\d+/,
});

const Duration = createToken({
    name: "Duration",
    pattern: /\d+(y|mo|w|d|h|m)/,
});

const StringLiteral = createToken({
    name: "StringLiteral",
    // pattern: /"(?:[^"]|"")*"/,
    // pattern: /"[^"]*"/,
    // pattern: /"(?:[^"]|\\"|\\\\)*"/,
    // pattern: /"(?:[^"\\]|\\")"/,

    pattern: (text, startOffset) => {
        if (text[startOffset] !== '"') return null;

        for (let i = startOffset + 1; i < text.length; i++) {
            if (text[i] === '"' && text[i + 1] === '"') {
                i++;
                continue;
            }

            if (text[i] === '"') {
                return [text.slice(startOffset, i + 1)];
            }
        }

        return null;
    },

    line_breaks: true,
});

const tokens = [
    Whitespace,

    And,
    Or,

    LeftParen,
    RightParen,

    LeftSquareBracket,
    RightSquareBracket,

    Comma,

    Duration,
    Integer,

    StringLiteral,

    NotEquals,
    Equals,

    GreaterThanOrEqual,
    GreaterThan,

    LessThanOrEqual,
    LessThan,

    Like,

    Identifier,
];

const lexer = new Lexer(tokens);

function tokenize(input: string) {
    return lexer.tokenize(input);
}

// const input = `
// (
//     (
//         hub = ["guid", "guid", "guid"]
//         && group = ["guid", "guid"]
//     )
//     || (
//         hub = ["guid"]
//         && group != ["guid", "guid"]
//         && author != ["guid"]
//     )
// )
// && rating >= 200
// && usefulness >= 8
// && difficulty = ["easy", "medium"]
// && duration < 10
// && age < 3d
// && tag != ["guid", "guid"]
// && (
//     title ~ "python ""cool"""
//     || body ~ "pandas"
// )
// `;

const exampleWithGuids = `
(
    (
        hub = ["5dbc698f-1774-4c49-ab9b-49d7fa6366b1", "8f7d6dd5-ae00-4ef6-b805-d7727028e8fb", "9d2ba75a-5a15-4136-935a-3628c33acb78"]
        && group = ["927f5a47-5b40-4841-895c-c9f2501b64bc", "0f193301-**************-8a1cad244dc3"]
    )
    || (
        hub = ["cec892f1-a68d-4c88-be6f-14323edea469"]
        && group != ["9f05df24-e484-49d2-b707-1c3e384b462a", "666a20dc-30d6-4592-8162-33ee421992f5"]
        && author != ["cecd8c02-58fa-4a73-9a4c-11d30dc9cc4c"]
    )
)
&& rating >= 200
&& usefulness >= 8
&& difficulty = ["easy", "medium"]
&& duration < 10
&& age < 3d
&& tag != ["300e3ac1-2165-4db6-8d2e-c806cdf63c49", "5d757a1e-1ddb-495d-9b99-b78a8402c404"]
&& (
    title ~ "python ""cool"""
    || body ~ "pandas"
)
`;

export const sampleTokens = tokenize(exampleWithGuids);

console.dir(
    sampleTokens.tokens.map((t) => t.image),
    { depth: null },
);

/*
statement
    :

hubStatement
    : "hub" eqNeqStatement arrayOfStringLiteralsStatement

groupStatement
    : "group" eqNeqStatement arrayOfStringLiteralsStatement

authorStatement
    : "author" eqNeqStatement arrayOfStringLiteralsStatement

ratingStatement
    : "rating" eqNeqStatement Integer

usefulnessStatement
    : "usefulness" eqNeqStatement Integer

difficultyStatement
    : "difficulty" eqNeqStatement arrayOfStringLiteralsStatement

durationStatement
    : "duration" neqComparisonStatement Integer

ageStatement
    : "age" neqComparisonStatement Duration

tagStatement
    : "tag" eqNeqStatement arrayOfStringLiteralsStatement

titleStatement
    : "title" Like StringLiteral

bodyStatement
    : "body" Like StringLiteral

comparisonStatement
    : eqNeqStatement | neqComparisonStatement

eqNeqStatement
    : Equals | NotEquals

neqComparisonStatement
    : GreaterThan | GreaterThanOrEqual | LessThan | LessThanOrEqual

comparisonStatement
    : neqComparisonStatement | Equals | NotEquals

arrayOfStringLiteralsStatement
    : LeftSquareBracket StringLiteral (Comma StringLiteral)* RightSquareBracket
*/
