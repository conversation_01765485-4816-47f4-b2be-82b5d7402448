import { NotFoundException } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
export declare abstract class BaseService {
    readonly entityType: string;
    constructor(entityType: string);
    abstract canGet(id: string, user: CurrentUser): Promise<true>;
    canChange(id: string, user: CurrentUser): Promise<true>;
    protected createNotFoundException(): NotFoundException;
    protected _check<T = unknown>(ids: string[], repo: {
        findMany: (params: {
            where: {
                id: {
                    in: string[];
                };
                deletedAt?: null;
            };
        }) => Promise<T[]>;
    }, notFoundDescription: string): Promise<T[]>;
}
