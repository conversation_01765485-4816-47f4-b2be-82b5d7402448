import { Common, Commune } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";
export declare class CommuneJoinRequestService {
    private readonly prisma;
    private readonly communeCore;
    constructor(prisma: PrismaService, communeCore: CommuneCore);
    getJoinRequests(input: Commune.GetCommuneJoinRequestsInput, user: CurrentUser): Promise<{
        status: import("@prisma/client").$Enums.CommuneJoinRequestStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        communeId: string;
    }[]>;
    createJoinRequest(input: Commune.CreateCommuneJoinRequestInput, user: CurrentUser): Promise<{
        status: import("@prisma/client").$Enums.CommuneJoinRequestStatus;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        userId: string;
        communeId: string;
    }>;
    deleteJoinRequest(input: Common.ObjectWithId, user: CurrentUser): Promise<boolean>;
    acceptJoinRequest(input: Common.ObjectWithId, user: CurrentUser): Promise<boolean>;
    rejectJoinRequest(input: Common.ObjectWithId, user: CurrentUser): Promise<boolean>;
}
