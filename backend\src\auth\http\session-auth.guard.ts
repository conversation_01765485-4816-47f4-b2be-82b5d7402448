import {
    ExecutionContext,
    Injectable,
    CanActivate,
    UnauthorizedException,
} from "@nestjs/common";

@Injectable()
export class HttpSessionAuthGuard implements CanActivate {
    canActivate(context: ExecutionContext): boolean {
        const request = context.switchToHttp().getRequest();

        if (!request.session || !request.session.user) {
            throw new UnauthorizedException();
        }

        // Set user on request for compatibility with existing decorators
        request.user = request.session.user;

        return true;
    }
}
