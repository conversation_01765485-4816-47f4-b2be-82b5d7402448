"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateSql = exports.validate = exports.createAst = exports.tokenize = exports.onReady = void 0;
let tokenize;
let createAst;
let validate;
let generateSql;
exports.onReady = Promise.all([
    import("./tokenize.mjs"),
    import("./create-ast.mjs"),
    import("./validate.mjs"),
    import("./generate-sql.mjs"),
]).then(([_tokenize, _createAst, _validate, _generateSql]) => {
    exports.tokenize = tokenize = _tokenize.tokenize;
    exports.createAst = createAst = _createAst.createAst;
    exports.validate = validate = _validate.validate;
    exports.generateSql = generateSql = _generateSql.generateSql;
});
//# sourceMappingURL=functions.js.map