import type { Rating } from "@commune/api";
import type { CurrentUser } from "src/auth/types";

import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import { toPrismaLocalizations, toPrismaPagination } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { getError } from "src/common/errors";
import { Prisma, UserRatingEntityType } from "@prisma/client";

@Injectable()
export class RatingService {
    private readonly logger = new Logger(RatingService.name);

    constructor(private readonly prisma: PrismaService) {}

    async getUserFeedbacks(input: Rating.GetUserFeedbacksInput) {
        return await this.prisma.userFeedback.findMany({
            ...toPrismaPagination(input.pagination),
            where: {
                targetUserId: input.userId,
            },
            include: {
                text: true,
                sourceUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
    }

    async createUserFeedback(
        data: Rating.CreateUserFeedbackInput,
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new ForbiddenException(
                    getError("must_be_admin_or_source_user"),
                );
            }

            if (data.sourceUserId === data.targetUserId) {
                throw new ForbiddenException(
                    getError("source_and_target_users_must_differ"),
                );
            }
        }

        const { id } = await this.prisma.userFeedback.create({
            data: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                value: data.value,
                isAnonymous: data.isAnonymous,
                text: {
                    create: toPrismaLocalizations(data.text, "text"),
                },
            },
        });

        return { id };
    }

    async getUserSummary(input: Rating.GetUserSummaryInput) {
        const [rating, karma, rate] = await Promise.all([
            this.prisma.userRating.aggregate({
                where: {
                    targetUserId: input.userId,
                },
                _sum: {
                    value: true,
                },
            }),

            this.prisma.userKarmaGivenPoint.aggregate({
                where: {
                    targetUserId: input.userId,
                },
                _sum: {
                    quantity: true,
                },
            }),

            this.prisma.userFeedback.aggregate({
                where: {
                    targetUserId: input.userId,
                },
                _avg: {
                    value: true,
                },
            }),
        ]);

        return {
            rating: rating._sum.value ?? 0,
            karma: karma._sum.quantity ?? 0,
            rate: rate._avg.value,
        };
    }

    async upsertRelativeUserRating(
        data: {
            sourceUserId: string;
            targetUserId: string;
            entityType: UserRatingEntityType;
            entityId: string;
            value: number;
        },
        prisma: Prisma.TransactionClient = this.prisma,
    ) {
        await prisma.userRating.upsert({
            where: {
                sourceUserId_targetUserId_entityType_entityId: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    entityType: data.entityType,
                    entityId: data.entityId,
                },
            },
            create: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                entityType: data.entityType,
                entityId: data.entityId,
                value: data.value,
            },
            update: {
                value: data.value,
            },
        });
    }

    async deleteRelativeUserRating(
        data: {
            sourceUserId: string;
            targetUserId: string;
            entityType: UserRatingEntityType;
            entityId: string;
        },
        prisma: Prisma.TransactionClient = this.prisma,
    ) {
        await prisma.userRating.delete({
            where: {
                sourceUserId_targetUserId_entityType_entityId: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    entityType: data.entityType,
                    entityId: data.entityId,
                },
            },
        });
    }
}
