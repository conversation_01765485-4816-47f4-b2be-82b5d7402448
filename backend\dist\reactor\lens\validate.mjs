import { z } from "zod";
const op = {
    equals: z.literal("="),
    notEquals: z.literal("!="),
    greaterThan: z.literal(">"),
    greaterThanOrEqual: z.literal(">="),
    lessThan: z.literal("<"),
    lessThanOrEqual: z.literal("<="),
    like: z.literal("~"),
};
const Hub = z.object({
    identifier: z.literal("hub"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Community = z.object({
    identifier: z.literal("community"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Author = z.object({
    identifier: z.literal("author"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Rating = z.object({
    identifier: z.literal("rating"),
    operator: z.union([
        op.equals,
        op.notEquals,
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z.coerce.number().int(),
});
const Usefulness = z.object({
    identifier: z.literal("usefulness"),
    operator: z.union([
        op.equals,
        op.notEquals,
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z.coerce.number().int().min(0).max(10),
});
const Tag = z.object({
    identifier: z.literal("tag"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.string().nanoid()),
});
const Difficulty = z.object({
    identifier: z.literal("difficulty"),
    operator: z.union([op.equals, op.notEquals]),
    value: z.array(z.union([z.literal("easy"), z.literal("medium"), z.literal("hard")])),
});
const Title = z.object({
    identifier: z.literal("title"),
    operator: op.like,
    value: z
        .string()
        .nonempty()
        .max(2 ** 10),
});
const Content = z.object({
    identifier: z.literal("content"),
    operator: op.like,
    value: z
        .string()
        .nonempty()
        .max(2 ** 10),
});
const Duration = z.object({
    identifier: z.literal("duration"),
    operator: z.union([
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z.coerce.number().int().min(0),
});
const MINUTE = 60;
const HOUR = 60 * MINUTE;
const DAY = 24 * HOUR;
const WEEK = 7 * DAY;
const MONTH = 30 * DAY;
const YEAR = 365 * DAY;
const ageUnitMapping = {
    y: YEAR,
    mo: MONTH,
    w: WEEK,
    d: DAY,
    h: HOUR,
    m: MINUTE,
};
const Age = z.object({
    identifier: z.literal("age"),
    operator: z.union([
        op.greaterThan,
        op.greaterThanOrEqual,
        op.lessThan,
        op.lessThanOrEqual,
    ]),
    value: z
        .string()
        .regex(/^\d+(mo|[ywdhm])$/)
        .transform((v) => {
        const match = v.match(/^(?<amount>\d+)(?<unit>mo|[ywdhm])$/);
        if (!match) {
            throw new Error("Invalid duration format");
        }
        const groups = match.groups;
        const amount = parseInt(groups.amount);
        return amount * ageUnitMapping[groups.unit];
    }),
});
const ComparisonSchema = z.intersection(z.object({
    type: z.literal("comparison"),
}), z.union([
    Hub,
    Community,
    Author,
    Rating,
    Usefulness,
    Tag,
    Difficulty,
    Title,
    Content,
    Duration,
    Age,
]));
export function validate(statement) {
    switch (statement.type) {
        case "and":
        case "or":
            return {
                ...statement,
                statements: statement.statements.map(validate),
            };
        case "comparison": {
            const result = ComparisonSchema.safeParse(statement);
            if (!result.success) {
                throw result.error;
            }
            return result.data;
        }
    }
}
//# sourceMappingURL=validate.mjs.map