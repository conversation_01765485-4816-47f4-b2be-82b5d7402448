import { z } from "zod";
import { Common, Types } from "@commune/api";

export const votesRequired = z.number().int().positive();

export type Voting = Types.Infer<typeof Voting>;
export const Voting = z.object({
    id: Common.id,

    votesRequired,
    endsAt: Common.stringToDate,

    title: Common.LocalizationsSchema,
    description: Common.LocalizationsSchema,

    options: z.array(
        z.object({
            id: Common.id,

            title: Common.LocalizationsSchema,
        }),
    ),

    createdAt: Common.stringToDate,
    updatedAt: Common.stringToDate,
});

export const Votings = z.array(Voting);

export type CreateVoting = Types.Infer<typeof CreateVoting>;
export const CreateVoting = z.object({
    votesRequired,
    endsAt: Common.stringToDate,

    title: Common.LocalizationsSchema,
    description: Common.LocalizationsSchema,

    options: z.array(
        z.object({
            title: Common.LocalizationsSchema,
        }),
    ),
});
