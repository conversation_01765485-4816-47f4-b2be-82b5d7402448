{"name": "@commune/api", "private": true, "version": "1.0.0", "type": "module", "main": "dist/index.cjs", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./acrpc/core": {"types": "./dist/acrpc/core.d.ts", "import": "./dist/acrpc/core.mjs", "require": "./dist/acrpc/core.cjs"}, "./acrpc/client": {"types": "./dist/acrpc/client.d.ts", "import": "./dist/acrpc/client.mjs", "require": "./dist/acrpc/client.cjs"}, "./acrpc/server": {"types": "./dist/acrpc/server.d.ts", "import": "./dist/acrpc/server.mjs", "require": "./dist/acrpc/server.cjs"}, "./acrpc/schema": {"types": "./dist/acrpc/schema.d.ts", "import": "./dist/acrpc/schema.mjs", "require": "./dist/acrpc/schema.cjs"}}, "scripts": {"build": "tsup"}}