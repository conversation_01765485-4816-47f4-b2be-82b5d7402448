import { Controller, HttpStatus } from "@nestjs/common";
import { getServer } from "src/acrpc";
import { AuthService } from "../auth.service";

@Controller("auth")
export class AuthController {
    constructor(private readonly authService: AuthService) {
        const acrpcServer = getServer();

        acrpcServer.register({
            auth: {
                otp: {
                    post: async (input, _, rest) => {
                        const isSent = await this.authService.otp({
                            ...input,
                            ipAddress: rest.req.ip ?? null,
                            userAgent: rest.req.headers["user-agent"] ?? null,
                        });

                        return {
                            isSent,
                        };
                    },
                },
                signUp: {
                    post: async (input, _, rest) => {
                        const { user } = await this.authService.register({
                            ...input,
                            ipAddress: rest.req.ip ?? null,
                            userAgent: rest.req.headers["user-agent"] ?? null,
                        });

                        rest.req.session.user = user;

                        return user;
                    },
                },
                signIn: {
                    post: async (input, _, rest) => {
                        const { user } = await this.authService.login({
                            ...input,
                            ipAddress: rest.req.ip ?? null,
                            userAgent: rest.req.headers["user-agent"] ?? null,
                        });

                        rest.req.session.user = user;

                        return user;
                    },
                },
                signOut: {
                    get: async (_, __, rest) => {
                        await new Promise<void>((resolve) => {
                            // Destroy the session
                            rest.req.session.destroy((err) => {
                                if (err) {
                                    console.error(
                                        "Error destroying session:",
                                        err,
                                    );
                                }

                                rest.res.clearCookie("session");
                                rest.res.sendStatus(HttpStatus.OK);

                                resolve();
                            });
                        });
                    },
                },
            },
        });
    }

    // @Post("otp")
    // @HttpCode(HttpStatus.CREATED)
    // async otp(
    //     @Body(new ZodPipe(Auth.SendOtpInputSchema)) body: Auth.SendOtpInput,
    //     @Ip() ipAddress: string,
    //     @Headers("user-agent") userAgent: string,
    // ) {
    //     const isSent = await this.authService.otp({
    //         ...body,
    //         ipAddress,
    //         userAgent,
    //     });

    //     return Common.parseInput(Auth.SendOtpOutputSchema, {
    //         isSent,
    //     });
    // }

    // @Post("register")
    // @HttpCode(HttpStatus.CREATED)
    // async register(
    //     @Req() req: Request,
    //     @Body(new ZodPipe(Auth.SignupInputSchema))
    //     body: Auth.SignupInput,
    //     @Ip() ipAddress: string,
    //     @Headers("user-agent") userAgent: string,
    // ) {
    //     const { user } = await this.authService.register({
    //         ...body,
    //         referrerId: body.referrerId ?? null,
    //         ipAddress,
    //         userAgent,
    //     });

    //     // Store user in session
    //     req.session.user = user;

    //     return Common.parseInput(Auth.SuccessfulOutputSchema, user);
    // }

    // @Post("login")
    // @HttpCode(HttpStatus.OK)
    // async login(
    //     @Req() req: Request,
    //     @Body(new ZodPipe(Auth.SigninInputSchema)) body: Auth.SigninInput,
    //     @Ip() ipAddress: string,
    //     @Headers("user-agent") userAgent: string,
    // ) {
    //     const { user } = await this.authService.login({
    //         ...body,
    //         ipAddress,
    //         userAgent,
    //     });

    //     // Store user in session
    //     req.session.user = user;

    //     return Common.parseInput(Auth.SuccessfulOutputSchema, user);
    // }

    // @Get("sign-out")
    // @HttpCode(HttpStatus.OK)
    // async signOut(@Req() req: Request, @Res() res: Response) {
    //     // Destroy the session
    //     req.session.destroy((err) => {
    //         if (err) {
    //             console.error("Error destroying session:", err);
    //         }

    //         res.clearCookie("session");
    //         res.sendStatus(HttpStatus.OK);
    //     });
    // }
}
