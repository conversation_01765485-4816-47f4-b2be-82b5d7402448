<script lang="ts">
  interface Props {
    imageFile: File;
    onSave: (editedFile: File) => void;
    onCancel: () => void;
    locale: "en" | "ru";
  }

  const i18n = {
    en: {
      imageEditor: "Image Editor",
      crop: "Crop",
      rotate: "Rotate",
      mirror: "Mirror",
      reset: "Reset",
      save: "Save",
      cancel: "Cancel",
      rotateLeft: "Rotate Left",
      rotateRight: "Rotate Right",
      flipHorizontal: "Flip Horizontal",
      flipVertical: "Flip Vertical",
      processing: "Processing...",
      freeRotate: "Free Rotate",
      done: "Done",
    },
    ru: {
      imageEditor: "Редактор изображений",
      crop: "Обрезка",
      rotate: "Поворот",
      mirror: "Отражение",
      reset: "Сброс",
      save: "Сохранить",
      cancel: "Отменить",
      rotateLeft: "Повернуть влево",
      rotateRight: "Повернуть вправо",
      flipHorizontal: "Отразить горизонтально",
      flipVertical: "Отразить вертикально",
      processing: "Обработка...",
      freeRotate: "Свободный поворот",
      done: "Готово",
    },
  };

  const { imageFile, onSave, onCancel, locale }: Props = $props();
  const t = $derived(i18n[locale]);

  // Editor state
  let canvas = $state<HTMLCanvasElement>();
  let ctx = $state<CanvasRenderingContext2D>();
  let image = $state<HTMLImageElement>();
  let imageLoaded = $state(false);
  let processing = $state(false);

  // Active tool
  type Tool = "crop" | "rotate" | "mirror" | null;
  let activeTool = $state<Tool>(null);
  let showFreeRotate = $state(false);

  // Image transformations (persistent across all operations)
  let rotation = $state(0); // Free rotation angle
  let snapRotation = $state(0); // 90-degree snap rotations
  let flipX = $state(false);
  let flipY = $state(false);
  let scale = $state(1);

  // Original image dimensions
  let originalWidth = $state(0);
  let originalHeight = $state(0);

  // Canvas dimensions
  let canvasWidth = $state(0);
  let canvasHeight = $state(0);
  let maxCanvasWidth = $state(800);
  let maxCanvasHeight = $state(600);

  // Crop settings (persistent and always visible)
  let cropX = $state(0);
  let cropY = $state(0);
  let cropWidth = $state(0);
  let cropHeight = $state(0);
  let cropEnabled = $state(true);

  // Interaction state
  let isDragging = $state(false);
  let isResizing = $state(false);
  let dragStartX = $state(0);
  let dragStartY = $state(0);
  let dragHandle = $state<string | null>(null); // 'move', 'nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'

  // Free rotation state
  let rotationSliderValue = $state(0);
  let isRotating = $state(false);

  // Initialize image
  $effect(() => {
    if (imageFile) {
      loadImage();
    }
  });

  async function loadImage() {
    try {
      const img = new Image();
      const fileToLoad = imageFile;
      const url = URL.createObjectURL(fileToLoad);

      img.onerror = () => {
        console.error("Failed to load image");
        URL.revokeObjectURL(url);
      };

      img.onload = () => {
        image = img;
        originalWidth = img.width;
        originalHeight = img.height;

        // Calculate canvas size to fit image while maintaining aspect ratio
        const aspectRatio = img.width / img.height;
        const containerWidth = Math.min(window.innerWidth - 40, maxCanvasWidth);
        const containerHeight = Math.min(window.innerHeight - 200, maxCanvasHeight);

        if (aspectRatio > containerWidth / containerHeight) {
          canvasWidth = containerWidth;
          canvasHeight = containerWidth / aspectRatio;
        } else {
          canvasHeight = containerHeight;
          canvasWidth = containerHeight * aspectRatio;
        }

        scale = canvasWidth / img.width;

        // Initialize crop to full image (100%)
        cropX = 0;
        cropY = 0;
        cropWidth = canvasWidth;
        cropHeight = canvasHeight;

        imageLoaded = true;
        URL.revokeObjectURL(url);

        // Draw image after it loads
        setTimeout(() => {
          if (canvas && ctx) {
            drawImage();
          }
        }, 100);
      };

      img.src = url;
    } catch (error) {
      console.error("Error loading image:", error);
    }
  }

  function drawImage() {
    if (!canvas || !ctx || !image) return;

    canvas.width = canvasWidth;
    canvas.height = canvasHeight;

    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // Draw grid background pattern
    drawGridBackground();

    ctx.save();

    // Apply all transformations
    ctx.translate(canvasWidth / 2, canvasHeight / 2);

    // Apply snap rotation first (90-degree increments)
    ctx.rotate((snapRotation * Math.PI) / 180);

    // Apply free rotation
    ctx.rotate((rotation * Math.PI) / 180);

    // Apply mirroring
    ctx.scale(flipX ? -1 : 1, flipY ? -1 : 1);

    // Draw image centered
    const drawWidth = canvasWidth;
    const drawHeight = canvasHeight;
    ctx.drawImage(image, -drawWidth / 2, -drawHeight / 2, drawWidth, drawHeight);

    ctx.restore();

    // Always draw crop overlay (persistent across all tools)
    if (cropEnabled) {
      drawCropOverlay();
    }
  }

  function drawGridBackground() {
    if (!ctx) return;

    const gridSize = 20; // Size of each grid square
    const lightColor = "#f0f0f0"; // Light gray
    const darkColor = "#e0e0e0"; // Slightly darker gray

    // Fill the entire canvas with the light color first
    ctx.fillStyle = lightColor;
    ctx.fillRect(0, 0, canvasWidth, canvasHeight);

    // Draw the checkerboard pattern
    ctx.fillStyle = darkColor;
    for (let x = 0; x < canvasWidth; x += gridSize) {
      for (let y = 0; y < canvasHeight; y += gridSize) {
        // Create checkerboard pattern by alternating squares
        const isEvenRow = Math.floor(y / gridSize) % 2 === 0;
        const isEvenCol = Math.floor(x / gridSize) % 2 === 0;

        // Draw dark square if (even row and odd col) or (odd row and even col)
        if ((isEvenRow && !isEvenCol) || (!isEvenRow && isEvenCol)) {
          ctx.fillRect(x, y, gridSize, gridSize);
        }
      }
    }
  }

  function drawCropOverlay() {
    if (!ctx) return;

    // Semi-transparent overlay on non-cropped areas
    ctx.fillStyle = "rgba(0, 0, 0, 0.6)";

    // Top area
    ctx.fillRect(0, 0, canvasWidth, cropY);
    // Bottom area
    ctx.fillRect(0, cropY + cropHeight, canvasWidth, canvasHeight - cropY - cropHeight);
    // Left area
    ctx.fillRect(0, cropY, cropX, cropHeight);
    // Right area
    ctx.fillRect(cropX + cropWidth, cropY, canvasWidth - cropX - cropWidth, cropHeight);

    // Crop border - Telegram style
    ctx.strokeStyle = activeTool === "crop" ? "#ffffff" : "rgba(255, 255, 255, 0.8)";
    ctx.lineWidth = activeTool === "crop" ? 3 : 2;
    ctx.strokeRect(cropX, cropY, cropWidth, cropHeight);

    // Grid lines for rule of thirds (when crop tool is active)
    if (activeTool === "crop") {
      ctx.strokeStyle = "rgba(255, 255, 255, 0.4)";
      ctx.lineWidth = 1;

      // Vertical lines
      const thirdWidth = cropWidth / 3;
      ctx.beginPath();
      ctx.moveTo(cropX + thirdWidth, cropY);
      ctx.lineTo(cropX + thirdWidth, cropY + cropHeight);
      ctx.moveTo(cropX + thirdWidth * 2, cropY);
      ctx.lineTo(cropX + thirdWidth * 2, cropY + cropHeight);

      // Horizontal lines
      const thirdHeight = cropHeight / 3;
      ctx.moveTo(cropX, cropY + thirdHeight);
      ctx.lineTo(cropX + cropWidth, cropY + thirdHeight);
      ctx.moveTo(cropX, cropY + thirdHeight * 2);
      ctx.lineTo(cropX + cropWidth, cropY + thirdHeight * 2);
      ctx.stroke();
    }

    // Corner and edge handles (when crop tool is active)
    if (activeTool === "crop") {
      const handleSize = 20;
      const handleThickness = 3;
      ctx.strokeStyle = "#ffffff";
      ctx.lineWidth = handleThickness;

      // Corner handles (L-shaped)
      const cornerLength = 15;

      // Top-left corner
      ctx.beginPath();
      ctx.moveTo(cropX, cropY + cornerLength);
      ctx.lineTo(cropX, cropY);
      ctx.lineTo(cropX + cornerLength, cropY);
      ctx.stroke();

      // Top-right corner
      ctx.beginPath();
      ctx.moveTo(cropX + cropWidth - cornerLength, cropY);
      ctx.lineTo(cropX + cropWidth, cropY);
      ctx.lineTo(cropX + cropWidth, cropY + cornerLength);
      ctx.stroke();

      // Bottom-left corner
      ctx.beginPath();
      ctx.moveTo(cropX, cropY + cropHeight - cornerLength);
      ctx.lineTo(cropX, cropY + cropHeight);
      ctx.lineTo(cropX + cornerLength, cropY + cropHeight);
      ctx.stroke();

      // Bottom-right corner
      ctx.beginPath();
      ctx.moveTo(cropX + cropWidth - cornerLength, cropY + cropHeight);
      ctx.lineTo(cropX + cropWidth, cropY + cropHeight);
      ctx.lineTo(cropX + cropWidth, cropY + cropHeight - cornerLength);
      ctx.stroke();

      // Edge handles (small lines in the middle of each edge)
      const edgeHandleLength = 8;

      // Top edge
      ctx.beginPath();
      ctx.moveTo(cropX + cropWidth / 2 - edgeHandleLength / 2, cropY);
      ctx.lineTo(cropX + cropWidth / 2 + edgeHandleLength / 2, cropY);
      ctx.stroke();

      // Bottom edge
      ctx.beginPath();
      ctx.moveTo(cropX + cropWidth / 2 - edgeHandleLength / 2, cropY + cropHeight);
      ctx.lineTo(cropX + cropWidth / 2 + edgeHandleLength / 2, cropY + cropHeight);
      ctx.stroke();

      // Left edge
      ctx.beginPath();
      ctx.moveTo(cropX, cropY + cropHeight / 2 - edgeHandleLength / 2);
      ctx.lineTo(cropX, cropY + cropHeight / 2 + edgeHandleLength / 2);
      ctx.stroke();

      // Right edge
      ctx.beginPath();
      ctx.moveTo(cropX + cropWidth, cropY + cropHeight / 2 - edgeHandleLength / 2);
      ctx.lineTo(cropX + cropWidth, cropY + cropHeight / 2 + edgeHandleLength / 2);
      ctx.stroke();
    }
  }

  // Detect which handle or area is being clicked
  function getHandleAtPosition(x: number, y: number): string | null {
    const tolerance = 20; // Increased tolerance for better touch interaction

    // Check corner handles first (priority over edges)
    if (Math.abs(x - cropX) <= tolerance && Math.abs(y - cropY) <= tolerance) return "nw";
    if (Math.abs(x - (cropX + cropWidth)) <= tolerance && Math.abs(y - cropY) <= tolerance)
      return "ne";
    if (Math.abs(x - cropX) <= tolerance && Math.abs(y - (cropY + cropHeight)) <= tolerance)
      return "sw";
    if (
      Math.abs(x - (cropX + cropWidth)) <= tolerance &&
      Math.abs(y - (cropY + cropHeight)) <= tolerance
    )
      return "se";

    // Check edge handles (smaller tolerance to avoid conflicts)
    const edgeTolerance = 15;
    if (
      Math.abs(y - cropY) <= edgeTolerance &&
      x >= cropX + tolerance &&
      x <= cropX + cropWidth - tolerance
    )
      return "n";
    if (
      Math.abs(y - (cropY + cropHeight)) <= edgeTolerance &&
      x >= cropX + tolerance &&
      x <= cropX + cropWidth - tolerance
    )
      return "s";
    if (
      Math.abs(x - cropX) <= edgeTolerance &&
      y >= cropY + tolerance &&
      y <= cropY + cropHeight - tolerance
    )
      return "w";
    if (
      Math.abs(x - (cropX + cropWidth)) <= edgeTolerance &&
      y >= cropY + tolerance &&
      y <= cropY + cropHeight - tolerance
    )
      return "e";

    // Check if inside crop area (for moving) - with margin to avoid edge conflicts
    const moveMargin = 25;
    if (
      x >= cropX + moveMargin &&
      x <= cropX + cropWidth - moveMargin &&
      y >= cropY + moveMargin &&
      y <= cropY + cropHeight - moveMargin
    )
      return "move";

    return null;
  }

  // Store initial values for stable dragging
  let initialCropX = $state(0);
  let initialCropY = $state(0);
  let initialCropWidth = $state(0);
  let initialCropHeight = $state(0);

  // Canvas event handlers
  function handleMouseDown(e: MouseEvent) {
    if (activeTool !== "crop") return;

    const rect = canvas?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    dragHandle = getHandleAtPosition(x, y);

    if (dragHandle) {
      isDragging = true;
      dragStartX = x;
      dragStartY = y;

      // Store initial crop values for stable relative adjustments
      initialCropX = cropX;
      initialCropY = cropY;
      initialCropWidth = cropWidth;
      initialCropHeight = cropHeight;
    }
  }

  function handleMouseMove(e: MouseEvent) {
    const rect = canvas?.getBoundingClientRect();
    if (!rect) return;

    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    // Update cursor based on handle position
    if (activeTool === "crop" && canvas) {
      const handle = getHandleAtPosition(x, y);
      switch (handle) {
        case "nw":
        case "se":
          canvas.style.cursor = "nw-resize";
          break;
        case "ne":
        case "sw":
          canvas.style.cursor = "ne-resize";
          break;
        case "n":
        case "s":
          canvas.style.cursor = "ns-resize";
          break;
        case "e":
        case "w":
          canvas.style.cursor = "ew-resize";
          break;
        case "move":
          canvas.style.cursor = "move";
          break;
        default:
          canvas.style.cursor = "crosshair";
      }
    }

    if (!isDragging || !dragHandle) return;

    const deltaX = x - dragStartX;
    const deltaY = y - dragStartY;

    // Handle different types of dragging using initial values for stability
    const minSize = 30; // Minimum crop size

    switch (dragHandle) {
      case "move":
        // Move the entire crop area
        const newCropX = initialCropX + deltaX;
        const newCropY = initialCropY + deltaY;
        cropX = Math.max(0, Math.min(canvasWidth - cropWidth, newCropX));
        cropY = Math.max(0, Math.min(canvasHeight - cropHeight, newCropY));
        break;

      case "nw":
        // Resize from top-left corner
        const newX = Math.max(
          0,
          Math.min(initialCropX + initialCropWidth - minSize, initialCropX + deltaX),
        );
        const newY = Math.max(
          0,
          Math.min(initialCropY + initialCropHeight - minSize, initialCropY + deltaY),
        );
        cropWidth = initialCropX + initialCropWidth - newX;
        cropHeight = initialCropY + initialCropHeight - newY;
        cropX = newX;
        cropY = newY;
        break;

      case "ne":
        // Resize from top-right corner
        const newY2 = Math.max(
          0,
          Math.min(initialCropY + initialCropHeight - minSize, initialCropY + deltaY),
        );
        cropWidth = Math.max(
          minSize,
          Math.min(canvasWidth - initialCropX, initialCropWidth + deltaX),
        );
        cropHeight = initialCropY + initialCropHeight - newY2;
        cropX = initialCropX;
        cropY = newY2;
        break;

      case "sw":
        // Resize from bottom-left corner
        const newX2 = Math.max(
          0,
          Math.min(initialCropX + initialCropWidth - minSize, initialCropX + deltaX),
        );
        cropWidth = initialCropX + initialCropWidth - newX2;
        cropHeight = Math.max(
          minSize,
          Math.min(canvasHeight - initialCropY, initialCropHeight + deltaY),
        );
        cropX = newX2;
        cropY = initialCropY;
        break;

      case "se":
        // Resize from bottom-right corner
        cropWidth = Math.max(
          minSize,
          Math.min(canvasWidth - initialCropX, initialCropWidth + deltaX),
        );
        cropHeight = Math.max(
          minSize,
          Math.min(canvasHeight - initialCropY, initialCropHeight + deltaY),
        );
        cropX = initialCropX;
        cropY = initialCropY;
        break;

      case "n":
        // Resize from top edge
        const newY3 = Math.max(
          0,
          Math.min(initialCropY + initialCropHeight - minSize, initialCropY + deltaY),
        );
        cropHeight = initialCropY + initialCropHeight - newY3;
        cropX = initialCropX;
        cropY = newY3;
        break;

      case "s":
        // Resize from bottom edge
        cropHeight = Math.max(
          minSize,
          Math.min(canvasHeight - initialCropY, initialCropHeight + deltaY),
        );
        cropX = initialCropX;
        cropY = initialCropY;
        break;

      case "w":
        // Resize from left edge
        const newX3 = Math.max(
          0,
          Math.min(initialCropX + initialCropWidth - minSize, initialCropX + deltaX),
        );
        cropWidth = initialCropX + initialCropWidth - newX3;
        cropX = newX3;
        cropY = initialCropY;
        break;

      case "e":
        // Resize from right edge
        cropWidth = Math.max(
          minSize,
          Math.min(canvasWidth - initialCropX, initialCropWidth + deltaX),
        );
        cropX = initialCropX;
        cropY = initialCropY;
        break;
    }

    drawImage();
  }

  function handleMouseUp() {
    isDragging = false;
    dragHandle = null;
    if (canvas && activeTool === "crop") {
      canvas.style.cursor = "crosshair";
    }
  }

  // Transform functions
  function rotateLeft() {
    snapRotation = (snapRotation - 90) % 360;
    // Transform crop area for counter-clockwise rotation
    transformCropForRotation(false);
    drawImage();
  }

  function rotateRight() {
    snapRotation = (snapRotation + 90) % 360;
    // Transform crop area for clockwise rotation
    transformCropForRotation(true);
    drawImage();
  }

  function transformCropForRotation(clockwise: boolean) {
    // Transform crop coordinates for 90-degree rotation
    // Store current crop as ratios (0-1) relative to canvas
    const cropRatioX = cropX / canvasWidth;
    const cropRatioY = cropY / canvasHeight;
    const cropRatioWidth = cropWidth / canvasWidth;
    const cropRatioHeight = cropHeight / canvasHeight;

    if (clockwise) {
      // 90° clockwise: (x,y) -> (1-y-h, x)
      const newCropRatioX = 1 - cropRatioY - cropRatioHeight;
      const newCropRatioY = cropRatioX;
      const newCropRatioWidth = cropRatioHeight;
      const newCropRatioHeight = cropRatioWidth;

      cropX = newCropRatioX * canvasWidth;
      cropY = newCropRatioY * canvasHeight;
      cropWidth = newCropRatioWidth * canvasWidth;
      cropHeight = newCropRatioHeight * canvasHeight;
    } else {
      // 90° counter-clockwise: (x,y) -> (y, 1-x-w)
      const newCropRatioX = cropRatioY;
      const newCropRatioY = 1 - cropRatioX - cropRatioWidth;
      const newCropRatioWidth = cropRatioHeight;
      const newCropRatioHeight = cropRatioWidth;

      cropX = newCropRatioX * canvasWidth;
      cropY = newCropRatioY * canvasHeight;
      cropWidth = newCropRatioWidth * canvasWidth;
      cropHeight = newCropRatioHeight * canvasHeight;
    }

    // Ensure crop stays within bounds
    cropX = Math.max(0, Math.min(cropX, canvasWidth - cropWidth));
    cropY = Math.max(0, Math.min(cropY, canvasHeight - cropHeight));
    cropWidth = Math.max(30, Math.min(cropWidth, canvasWidth - cropX));
    cropHeight = Math.max(30, Math.min(cropHeight, canvasHeight - cropY));
  }

  function transformCropForMirror(horizontal: boolean) {
    // Transform crop coordinates for mirroring
    const cropRatioX = cropX / canvasWidth;
    const cropRatioY = cropY / canvasHeight;
    const cropRatioWidth = cropWidth / canvasWidth;
    const cropRatioHeight = cropHeight / canvasHeight;

    if (horizontal) {
      // Horizontal flip: x -> 1-x-width
      const newCropRatioX = 1 - cropRatioX - cropRatioWidth;
      cropX = newCropRatioX * canvasWidth;
    } else {
      // Vertical flip: y -> 1-y-height
      const newCropRatioY = 1 - cropRatioY - cropRatioHeight;
      cropY = newCropRatioY * canvasHeight;
    }

    // Ensure crop stays within bounds
    cropX = Math.max(0, Math.min(cropX, canvasWidth - cropWidth));
    cropY = Math.max(0, Math.min(cropY, canvasHeight - cropHeight));
  }

  function toggleFlipX() {
    flipX = !flipX;
    // Transform crop area for horizontal mirroring
    transformCropForMirror(true);
    drawImage();
  }

  function toggleFlipY() {
    flipY = !flipY;
    // Transform crop area for vertical mirroring
    transformCropForMirror(false);
    drawImage();
  }

  function updateFreeRotation(angle: number) {
    rotation = angle;
    rotationSliderValue = angle;
    drawImage();
  }

  function resetTransforms() {
    rotation = 0;
    snapRotation = 0;
    flipX = false;
    flipY = false;

    // Reset crop to full image (100%)
    cropX = 0;
    cropY = 0;
    cropWidth = canvasWidth;
    cropHeight = canvasHeight;

    rotationSliderValue = 0;
    activeTool = null;
    showFreeRotate = false;
    drawImage();
  }

  // Tool activation functions
  function activateCropTool() {
    activeTool = activeTool === "crop" ? null : "crop";
    showFreeRotate = false;
    drawImage();
  }

  function activateRotateTool() {
    activeTool = activeTool === "rotate" ? null : "rotate";
    showFreeRotate = false;
    drawImage();
  }

  function activateMirrorTool() {
    activeTool = activeTool === "mirror" ? null : "mirror";
    showFreeRotate = false;
    drawImage();
  }

  function toggleFreeRotate() {
    showFreeRotate = !showFreeRotate;
    if (showFreeRotate) {
      activeTool = "rotate";
    }
    drawImage();
  }

  // Touch event handlers for mobile support
  function handleTouchStart(e: TouchEvent) {
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      const rect = canvas?.getBoundingClientRect();
      if (!rect) return;

      const mouseEvent = new MouseEvent("mousedown", {
        clientX: touch.clientX,
        clientY: touch.clientY,
        bubbles: true,
        cancelable: true,
      });
      handleMouseDown(mouseEvent);
    }
    e.preventDefault();
  }

  function handleTouchMove(e: TouchEvent) {
    if (e.touches.length === 1) {
      const touch = e.touches[0];
      const rect = canvas?.getBoundingClientRect();
      if (!rect) return;

      const mouseEvent = new MouseEvent("mousemove", {
        clientX: touch.clientX,
        clientY: touch.clientY,
        bubbles: true,
        cancelable: true,
      });
      handleMouseMove(mouseEvent);
    }
    e.preventDefault();
  }

  function handleTouchEnd(e: TouchEvent) {
    handleMouseUp();
    e.preventDefault();
  }

  // Save edited image
  async function saveImage() {
    if (!canvas || !ctx || !image) return;

    processing = true;

    try {
      // Create a new canvas for the final image
      const outputCanvas = document.createElement("canvas");
      const outputCtx = outputCanvas.getContext("2d");
      if (!outputCtx) throw new Error("Could not get canvas context");

      // Calculate final dimensions based on crop area
      const cropScaleX = originalWidth / canvasWidth;
      const cropScaleY = originalHeight / canvasHeight;
      const finalWidth = Math.round(cropWidth * cropScaleX);
      const finalHeight = Math.round(cropHeight * cropScaleY);

      outputCanvas.width = finalWidth;
      outputCanvas.height = finalHeight;

      outputCtx.save();

      // Apply all transformations in the correct order
      outputCtx.translate(finalWidth / 2, finalHeight / 2);

      // Apply snap rotation first (90-degree increments)
      outputCtx.rotate((snapRotation * Math.PI) / 180);

      // Apply free rotation
      outputCtx.rotate((rotation * Math.PI) / 180);

      // Apply mirroring
      outputCtx.scale(flipX ? -1 : 1, flipY ? -1 : 1);

      // Draw cropped portion of the image
      const sourceCropX = cropX * cropScaleX;
      const sourceCropY = cropY * cropScaleY;
      const sourceCropWidth = cropWidth * cropScaleX;
      const sourceCropHeight = cropHeight * cropScaleY;

      outputCtx.drawImage(
        image,
        sourceCropX,
        sourceCropY,
        sourceCropWidth,
        sourceCropHeight,
        -finalWidth / 2,
        -finalHeight / 2,
        finalWidth,
        finalHeight,
      );

      outputCtx.restore();

      // Convert to blob and create file
      const blob = await new Promise<Blob>((resolve) => {
        outputCanvas.toBlob(
          (blob) => {
            resolve(blob!);
          },
          imageFile.type,
          0.9,
        );
      });

      const editedFile = new File([blob], imageFile.name, {
        type: imageFile.type,
        lastModified: Date.now(),
      });

      onSave(editedFile);
    } catch (error) {
      console.error("Error saving image:", error);
    } finally {
      processing = false;
    }
  }

  // Initialize canvas context
  $effect(() => {
    if (canvas) {
      ctx = canvas.getContext("2d") || undefined;
    }
  });

  // Redraw image when transformations change
  $effect(() => {
    if (canvas && ctx && image && imageLoaded) {
      drawImage();
    }
  });

  // Handle window resize
  $effect(() => {
    function handleResize() {
      if (image && imageLoaded) {
        // Recalculate canvas size
        const aspectRatio = originalWidth / originalHeight;
        const containerWidth = Math.min(window.innerWidth - 40, maxCanvasWidth);
        const containerHeight = Math.min(window.innerHeight - 200, maxCanvasHeight);

        if (aspectRatio > containerWidth / containerHeight) {
          canvasWidth = containerWidth;
          canvasHeight = containerWidth / aspectRatio;
        } else {
          canvasHeight = containerHeight;
          canvasWidth = containerHeight * aspectRatio;
        }

        scale = canvasWidth / originalWidth;

        // Adjust crop area to maintain relative position
        const cropRatioX = cropX / (canvasWidth || 1);
        const cropRatioY = cropY / (canvasHeight || 1);
        const cropRatioWidth = cropWidth / (canvasWidth || 1);
        const cropRatioHeight = cropHeight / (canvasHeight || 1);

        cropX = cropRatioX * canvasWidth;
        cropY = cropRatioY * canvasHeight;
        cropWidth = cropRatioWidth * canvasWidth;
        cropHeight = cropRatioHeight * canvasHeight;

        drawImage();
      }
    }

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  });
</script>

<div class="telegram-image-editor">
  <!-- Header -->
  <div class="editor-header">
    <button
      type="button"
      class="btn-close"
      onclick={onCancel}
      disabled={processing}
      aria-label={t.cancel}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
      >
        <line x1="18" y1="6" x2="6" y2="18"></line>
        <line x1="6" y1="6" x2="18" y2="18"></line>
      </svg>
    </button>
    <div class="editor-title"></div>
    <button
      type="button"
      class="btn-done"
      onclick={saveImage}
      disabled={processing || !imageLoaded}
    >
      {#if processing}
        <div class="spinner"></div>
      {:else}
        {t.done}
      {/if}
    </button>
  </div>

  <!-- Main Canvas Area -->
  <div class="editor-main">
    <div class="canvas-container">
      {#if imageLoaded}
        <canvas
          bind:this={canvas}
          onmousedown={handleMouseDown}
          onmousemove={handleMouseMove}
          onmouseup={handleMouseUp}
          onmouseleave={handleMouseUp}
          ontouchstart={handleTouchStart}
          ontouchmove={handleTouchMove}
          ontouchend={handleTouchEnd}
          class="editor-canvas"
          style:cursor={activeTool === "crop" ? "crosshair" : "default"}
        ></canvas>
      {:else}
        <div class="loading-placeholder">
          <div class="spinner"></div>
        </div>
      {/if}
    </div>

    <!-- Free Rotation Slider (when active) -->
    {#if showFreeRotate}
      <div class="rotation-slider-container">
        <input
          type="range"
          min="-180"
          max="180"
          step="1"
          bind:value={rotationSliderValue}
          oninput={(e) => updateFreeRotation(Number((e.target as HTMLInputElement)?.value || 0))}
          class="rotation-slider"
        />
        <div class="rotation-value">{rotationSliderValue}°</div>
      </div>
    {/if}
  </div>

  <!-- Reserved area for additional controls (fixed height) -->
  <div class="additional-controls-area">
    {#if activeTool === "rotate"}
      <div class="tool-controls">
        <div class="rotate-controls">
          <button type="button" class="control-btn" onclick={rotateLeft}>
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M1 4v6h6"></path>
              <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
            </svg>
            <span>90°</span>
          </button>

          <button type="button" class="control-btn" onclick={toggleFreeRotate}>
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <circle cx="12" cy="12" r="3"></circle>
              <path d="M12 1v6"></path>
              <path d="M12 17v6"></path>
              <path d="M4.22 4.22l4.24 4.24"></path>
              <path d="M15.54 15.54l4.24 4.24"></path>
              <path d="M1 12h6"></path>
              <path d="M17 12h6"></path>
              <path d="M4.22 19.78l4.24-4.24"></path>
              <path d="M15.54 8.46l4.24-4.24"></path>
            </svg>
            <span>{t.freeRotate}</span>
          </button>

          <button type="button" class="control-btn" onclick={rotateRight}>
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              style="transform: scaleX(-1)"
            >
              <path d="M1 4v6h6"></path>
              <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
            </svg>
            <span>90°</span>
          </button>
        </div>
      </div>
    {/if}

    {#if activeTool === "mirror"}
      <div class="tool-controls">
        <div class="mirror-controls">
          <button type="button" class="control-btn {flipX ? 'active' : ''}" onclick={toggleFlipX}>
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <path d="M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3"></path>
              <path d="M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3"></path>
              <path d="M12 20v2"></path>
              <path d="M12 14v2"></path>
              <path d="M12 8v2"></path>
              <path d="M12 2v2"></path>
            </svg>
            <span>{t.flipHorizontal}</span>
          </button>

          <button type="button" class="control-btn {flipY ? 'active' : ''}" onclick={toggleFlipY}>
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              style="transform: rotate(90deg)"
            >
              <path d="M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3"></path>
              <path d="M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3"></path>
              <path d="M12 20v2"></path>
              <path d="M12 14v2"></path>
              <path d="M12 8v2"></path>
              <path d="M12 2v2"></path>
            </svg>
            <span>{t.flipVertical}</span>
          </button>
        </div>
      </div>
    {/if}
  </div>

  <!-- Bottom Toolbar (Telegram Style) -->
  <div class="bottom-toolbar">
    <!-- Crop Tool -->
    <button
      type="button"
      class="tool-btn {activeTool === 'crop' ? 'active' : ''}"
      onclick={activateCropTool}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
      >
        <path d="M6.13 1L6 16a2 2 0 0 0 2 2h15"></path>
        <path d="M1 6.13L16 6a2 2 0 0 1 2 2v15"></path>
      </svg>
      <span>{t.crop}</span>
    </button>

    <!-- Rotate Tool -->
    <button
      type="button"
      class="tool-btn {activeTool === 'rotate' ? 'active' : ''}"
      onclick={activateRotateTool}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
      >
        <path d="M1 4v6h6"></path>
        <path d="M3.51 15a9 9 0 1 0 2.13-9.36L1 10"></path>
      </svg>
      <span>{t.rotate}</span>
    </button>

    <!-- Mirror Tool -->
    <button
      type="button"
      class="tool-btn {activeTool === 'mirror' ? 'active' : ''}"
      onclick={activateMirrorTool}
    >
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
      >
        <path d="M8 3H5a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h3"></path>
        <path d="M16 3h3a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-3"></path>
        <path d="M12 20v2"></path>
        <path d="M12 14v2"></path>
        <path d="M12 8v2"></path>
        <path d="M12 2v2"></path>
      </svg>
      <span>{t.mirror}</span>
    </button>

    <!-- Reset Tool -->
    <button type="button" class="tool-btn" onclick={resetTransforms}>
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
      >
        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
        <path d="M21 3v5h-5"></path>
        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
        <path d="M3 21v-5h5"></path>
      </svg>
      <span>{t.reset}</span>
    </button>
  </div>
</div>

<style>
  .telegram-image-editor {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0; /* Allow flex shrinking */
    background: #000;
    color: #fff;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    overflow: hidden;
  }

  /* Header */
  .editor-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 10;
  }

  .btn-close {
    background: none;
    border: none;
    color: #fff;
    padding: 8px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
  }

  .btn-close:hover {
    background: rgba(255, 255, 255, 0.1);
  }

  .editor-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    flex: 1;
  }

  .btn-done {
    background: #007aff;
    border: none;
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    min-width: 80px;
    justify-content: center;
  }

  .btn-done:hover:not(:disabled) {
    background: #0056cc;
    transform: scale(1.02);
  }

  .btn-done:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  /* Main editor area */
  .editor-main {
    flex: 1;
    min-height: 0; /* Allow flex shrinking */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
    overflow: hidden;
  }

  .canvas-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    max-width: 100%;
    max-height: 100%;
  }

  .editor-canvas {
    max-width: 100%;
    max-height: 100%;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    background: transparent;
  }

  .loading-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 300px;
    height: 300px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.1);
  }

  /* Spinner */
  .spinner {
    width: 24px;
    height: 24px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  /* Rotation slider */
  .rotation-slider-container {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    background: rgba(0, 0, 0, 0.8);
    padding: 16px 24px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
  }

  .rotation-slider {
    width: 200px;
    height: 4px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    outline: none;
    appearance: none;
  }

  .rotation-slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: #007aff;
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  }

  .rotation-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #007aff;
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 8px rgba(0, 122, 255, 0.3);
  }

  .rotation-value {
    color: #fff;
    font-size: 14px;
    font-weight: 500;
  }

  /* Bottom toolbar */
  .bottom-toolbar {
    display: flex;
    justify-content: space-around;
    align-items: center;
    padding: 16px 20px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }

  .tool-btn {
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 8px;
    border-radius: 12px;
    transition: all 0.2s ease;
    min-width: 60px;
  }

  .tool-btn:hover {
    color: #fff;
    background: rgba(255, 255, 255, 0.1);
  }

  .tool-btn.active {
    color: #007aff;
    background: rgba(0, 122, 255, 0.15);
  }

  .tool-btn span {
    font-size: 12px;
    font-weight: 500;
  }

  /* Reserved area for additional controls */
  .additional-controls-area {
    height: 80px; /* Fixed height to prevent layout shifts */
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.9);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Tool-specific controls */
  .tool-controls {
    padding: 12px 20px;
    background: transparent;
  }

  .rotate-controls,
  .mirror-controls {
    display: flex;
    justify-content: center;
    gap: 16px;
  }

  .control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 12px 16px;
    border-radius: 12px;
    transition: all 0.2s ease;
    min-width: 80px;
  }

  .control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: #fff;
  }

  .control-btn.active {
    background: rgba(0, 122, 255, 0.3);
    color: #007aff;
  }

  .control-btn span {
    font-size: 11px;
    font-weight: 500;
  }

  /* Responsive design */
  @media (max-width: 768px) {
    .editor-header {
      padding: 8px 12px;
    }

    .editor-title {
      font-size: 16px;
    }

    .editor-main {
      padding: 12px;
    }

    .bottom-toolbar {
      padding: 12px 16px;
    }

    .tool-btn {
      min-width: 50px;
      padding: 8px 4px;
    }

    .tool-btn span {
      font-size: 10px;
    }

    .control-btn {
      min-width: 60px;
      padding: 8px 12px;
    }

    .rotation-slider-container {
      bottom: 10px;
      padding: 12px 16px;
    }

    .rotation-slider {
      width: 150px;
    }
  }

  /* Accessibility */
  .tool-btn:focus,
  .control-btn:focus,
  .btn-close:focus,
  .btn-done:focus {
    outline: 2px solid #007aff;
    outline-offset: 2px;
  }

  /* Smooth animations */
  .tool-controls {
    animation: fadeIn 0.3s ease-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .rotation-slider-container {
    animation: slideInFromBottom 0.3s ease-out;
  }

  @keyframes slideInFromBottom {
    from {
      opacity: 0;
      transform: translateX(-50%) translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateX(-50%) translateY(0);
    }
  }
</style>
