<script lang="ts">
  import type { Snippet } from "svelte";

  interface Props {
    title: string;
    children: Snippet;
    isOpen?: boolean;
    onToggle?: () => void;
  }

  const { title, children, isOpen, onToggle }: Props = $props();

  let isExpanded = $state(isOpen ?? false);

  function handleToggle() {
    isExpanded = !isExpanded;

    onToggle?.();
  }
</script>

<div class="card mb-2">
  <!-- svelte-ignore a11y_click_events_have_key_events -->
  <div
    class="card-header d-flex justify-content-between align-items-center"
    onclick={handleToggle}
    style:cursor="pointer"
    role="switch"
    tabindex="0"
    aria-checked={isExpanded}
  >
    <div>{title}</div>
    <button class="btn btn-link" type="button" aria-expanded={isExpanded}>
      {isExpanded ? "−" : "+"}
    </button>
  </div>

  {#if isExpanded}
    <div class="card-body">
      {@render children()}
    </div>
  {/if}
</div>
