import { Prisma, PrismaClient, User<PERSON><PERSON> } from "@prisma/client";
import { fakerEN as Faker } from "@faker-js/faker";

const prisma = new PrismaClient();

const ID_0 = "0".repeat(21);
const ID_1 = "0".repeat(20) + "1";
const ID_2 = "0".repeat(20) + "2";

const users = [
    {
        id: ID_0,
        email: "<EMAIL>",
        role: "user",
        name: {
            en: "Test User",
            ru: "Тестовый Пользователь",
        },
        description: {
            en: "Test description.",
            ru: "Тестовое описание.",
        },
    },
    {
        id: ID_1,
        email: "<EMAIL>",
        role: "admin",
        name: {
            en: "Admin",
            ru: "Администратор",
        },
        description: {
            en: "Admin description.",
            ru: "Описание администратора.",
        },
    },
    {
        id: ID_2,
        email: "<EMAIL>",
        role: "user",
        name: {
            en: "User",
            ru: "Пользователь",
        },
        description: {
            en: "User description.",
            ru: "Описание пользователя.",
        },
    },
];

async function main() {
    await prisma.$transaction(async (trx) => {
        await Promise.all(
            users.map((user) =>
                trx.user.create({
                    data: {
                        id: user.id,
                        email: user.email,
                        role: user.role as UserRole,
                        name: {
                            create: [
                                {
                                    locale: "en",
                                    key: "name",
                                    value: user.name.en,
                                },
                                {
                                    locale: "ru",
                                    key: "name",
                                    value: user.name.ru,
                                },
                            ],
                        },
                        description: {
                            create: [
                                {
                                    locale: "en",
                                    key: "description",
                                    value: user.description.en,
                                },
                                {
                                    locale: "ru",
                                    key: "description",
                                    value: user.description.ru,
                                },
                            ],
                        },
                    },
                }),
            ),
        );

        // await trx.commune.create({
        //     data: {
        //         id: ID_0,
        //         name: {
        //             create: [
        //                 {
        //                     locale: "en",
        //                     key: "name",
        //                     value: "Test Commune",
        //                 },
        //                 {
        //                     locale: "ru",
        //                     key: "name",
        //                     value: "Тестовая Коммуна",
        //                 },
        //             ],
        //         },
        //         description: {
        //             create: [
        //                 {
        //                     locale: "en",
        //                     key: "description",
        //                     value: "Test description.",
        //                 },
        //                 {
        //                     locale: "ru",
        //                     key: "description",
        //                     value: "Тестовое описание.",
        //                 },
        //             ],
        //         },
        //         members: {
        //             create: [
        //                 {
        //                     id: ID_0,
        //                     actorType: "user",
        //                     actorId: ID_0,
        //                     isHead: true,
        //                 },
        //             ],
        //         },
        //     },
        // });

        await seedManyCommunes(trx, 60);
    });
}

async function seedManyCommunes(trx: Prisma.TransactionClient, count = 20) {
    await Promise.all(
        new Array(count).fill(0).map(async (_, index) => {
            await trx.commune.create({
                data: {
                    name: {
                        create: [
                            {
                                locale: "en",
                                key: "name",
                                value: Faker.company.name(),
                            },
                        ],
                    },

                    members: {
                        create: {
                            actorType: "user",
                            actorId: ID_0,
                            isHead: true,
                        },
                    },
                },
            });
        }),
    );
}

main()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
