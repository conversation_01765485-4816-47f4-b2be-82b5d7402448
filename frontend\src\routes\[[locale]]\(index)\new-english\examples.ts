import type { DictionaryK<PERSON>, DictionaryKeyOrAny } from "./dictionaries";

type NewEnglishTranslation = Record<DictionaryKey, string> | string;

type BreakdownBase = {
  sounds: string[];
  mapping: string;
}

export type PlainBreakdown = BreakdownBase;
export type DictionaryBreakdown = ({ dictionaryKey: DictionaryKeyOrAny } & BreakdownBase)[];
export type Breakdown = (PlainBreakdown | DictionaryBreakdown)[];

export const examples: {
  english: string;
  transcriptions: Record<DictionaryKey, string>;
  newEnglish: NewEnglishTranslation;
  breakdown: Breakdown;
}[] = [
  /**
    1. approach (C /əˈproʊtʃ/, O /əˈpɹoʊtʃ/) - aprōč
      1. ə - a
      2. p - p
      3. r/ɹ - r
      4. oʊ - ō
      5. tʃ - č
   */
  {
    english: "approach",
    transcriptions: {
      cambridge: "əˈproʊtʃ",
      opendict: "əˈpɹoʊtʃ",
    },
    newEnglish: "aprōč",
    breakdown: [
      {
        sounds: ["ə"],
        mapping: "a",
      },
      {
        sounds: ["p"],
        mapping: "p",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["oʊ"],
        mapping: "ō",
      },
      {
        sounds: ["tʃ"],
        mapping: "č",
      },
    ],
  },

  /**
    2. pig (C /pɪɡ/, O /ˈpɪɡ/) - pik
      1. p - p
      2. ɪ - i
      3. ɡ$ - k
   */
  {
    english: "pig",
    transcriptions: {
      cambridge: "pɪɡ",
      opendict: "ˈpɪɡ",
    },
    newEnglish: "pik",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p",
      },
      {
        sounds: ["ɪ"],
        mapping: "i",
      },
      {
        sounds: ["ɡ$"],
        mapping: "k",
      },
    ],
  },

  /**
    3. dog (C /dɑːɡ/, O /ˈdɔɡ/) - dak
      1. d - d
      2. ɑː/ɔ - a
      3. ɡ$ - k
   */
  {
    english: "dog",
    transcriptions: {
      cambridge: "dɑːɡ",
      opendict: "ˈdɔɡ",
    },
    newEnglish: "dak",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɑː"],
          mapping: "a",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔ"],
          mapping: "a",
        },
      ],
      {
        sounds: ["ɡ$"],
        mapping: "k",
      },
    ],
  },

  /**
    4. red (C /red/, O /ˈɹɛd/) - ret
      1. r/ɹ - r
      2. e/ɛ - e
      3. d$ - t
   */
  {
    english: "red",
    transcriptions: {
      cambridge: "red",
      opendict: "ˈɹɛd",
    },
    newEnglish: "ret",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e",
        },
      ],
      {
        sounds: ["d$"],
        mapping: "t",
      },
    ],
  },

  /**
    5. rat (C /ræt/, O /ˈɹæt/) - rāt
      1. r/ɹ - r
      2. æ - ā
      3. t - t
   */
  {
    english: "rat",
    transcriptions: {
      cambridge: "ræt",
      opendict: "ˈɹæt",
    },
    newEnglish: "rāt",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["æ"],
        mapping: "ā",
      },
      {
        sounds: ["t"],
        mapping: "t",
      },
    ],
  },

  /**
    6. turtle (C /ˈtɝː.t̬əl/, O /ˈtɝtəɫ/) - tëtl
      2. t - t
      3. ɝ - ë
      4. t̬/t - t
      5. əl/əɫ$ - l
   */
  {
    english: "turtle",
    transcriptions: {
      cambridge: "ˈtɝː.t̬əl",
      opendict: "ˈtɝtəɫ",
    },
    newEnglish: "tëtl",
    breakdown: [
      {
        sounds: ["t"],
        mapping: "t",
      },
      {
        sounds: ["ɝ"],
        mapping: "ë",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["t̬"],
          mapping: "t",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["t"],
          mapping: "t",
        },
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["əl$"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["əɫ$"],
          mapping: "l",
        },
      ],
    ],
  },

  /**
    7. goat (C /ɡoʊt/, O /ˈɡoʊt/) - gōt
      1. ɡ - g
      2. oʊ - ō
      3. t - t
   */
  {
    english: "goat",
    transcriptions: {
      cambridge: "ɡoʊt",
      opendict: "ˈɡoʊt",
    },
    newEnglish: "gōt",
    breakdown: [
      {
        sounds: ["ɡ"],
        mapping: "g",
      },
      {
        sounds: ["oʊ"],
        mapping: "ō",
      },
      {
        sounds: ["t"],
        mapping: "t",
      },
    ],
  },

  /**
    8. bear (C /ber/, O /ˈbɛɹ/) - be
      1. b - b
      2. e/ɛ - e
      3. r/ɹ$ - *
   */
  {
    english: "bear",
    transcriptions: {
      cambridge: "ber",
      opendict: "ˈbɛɹ",
    },
    newEnglish: "be",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e",
        },
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r$"],
          mapping: "",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ$"],
          mapping: "",
        },
      ],
    ],
  },

  /**
    9. panther (C /ˈpæn.θɚ/, O /ˈpænθɝ/) - pānfe
      1. p - p
      2. æ - ā
      3. n - n
      4. θ - f
      5. ɚ/ɝ$ - e
   */
  {
    english: "panther",
    transcriptions: {
      cambridge: "ˈpæn.θɚ",
      opendict: "ˈpænθɝ",
    },
    newEnglish: "pānfe",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p",
      },
      {
        sounds: ["æ"],
        mapping: "ā",
      },
      {
        sounds: ["n"],
        mapping: "n",
      },
      {
        sounds: ["θ"],
        mapping: "f",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɚ"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝ$"],
          mapping: "e",
        },
      ],
    ],
  },

  /**
    10. frog (C /frɑːɡ/, O /ˈfɹɑɡ/) - frak
      1. f - f
      2. r/ɹ - r
      3. ɑ - a
      4. ɡ$ - k
   */
  {
    english: "frog",
    transcriptions: {
      cambridge: "frɑːɡ",
      opendict: "ˈfɹɑɡ",
    },
    newEnglish: "frak",
    breakdown: [
      {
        sounds: ["f"],
        mapping: "f",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["ɑ"],
        mapping: "a",
      },
      {
        sounds: ["ɡ$"],
        mapping: "k",
      },
    ],
  },

  /**
    11. feather (C /ˈfeð.ɚ/, O /ˈfɛðɝ/) - feve
      1. f - f
      2. e/ɛ - e
      3. ð - v
      4. ɚ/ɝ - e
   */
  {
    english: "feather",
    transcriptions: {
      cambridge: "ˈfeð.ɚ",
      opendict: "ˈfɛðɝ",
    },
    newEnglish: "feve",
    breakdown: [
      {
        sounds: ["f"],
        mapping: "f",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e",
        },
      ],
      {
        sounds: ["ð"],
        mapping: "v",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɚ"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝ"],
          mapping: "e",
        },
      ],
    ],
  },

  /**
    12. beaver (C /ˈbiː.vɚ/, O /ˈbivɝ/) - bive
      1. b - b
      2. i - i
      3. v - v
      4. ɚ/ɝ - e
   */
  {
    english: "beaver",
    transcriptions: {
      cambridge: "ˈbiː.vɚ",
      opendict: "ˈbivɝ",
    },
    newEnglish: "bive",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b",
      },
      {
        sounds: ["i"],
        mapping: "i",
      },
      {
        sounds: ["v"],
        mapping: "v",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɚ"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝ"],
          mapping: "e",
        },
      ],
    ],
  },

  /**
    13. snake (C /sneɪk/, O /ˈsneɪk/) - sneík
      1. s - s
      2. n - n
      3. eɪ - eí
      4. k - k
   */
  {
    english: "snake",
    transcriptions: {
      cambridge: "sneɪk",
      opendict: "ˈsneɪk",
    },
    newEnglish: "sneík",
    breakdown: [
      {
        sounds: ["s"],
        mapping: "s",
      },
      {
        sounds: ["n"],
        mapping: "n",
      },
      {
        sounds: ["eɪ"],
        mapping: "eí",
      },
      {
        sounds: ["k"],
        mapping: "k",
      },
    ],
  },

  /**
    14. sheep (C /ʃiːp/, O /ˈʃip/) - šip
      1. ʃ - š
      2. i - i
      3. p - p
   */
  {
    english: "sheep",
    transcriptions: {
      cambridge: "ʃiːp",
      opendict: "ˈʃip",
    },
    newEnglish: "šip",
    breakdown: [
      {
        sounds: ["ʃ"],
        mapping: "š",
      },
      {
        sounds: ["i"],
        mapping: "i",
      },
      {
        sounds: ["p"],
        mapping: "p",
      },
    ],
  },

  /**
    15. chicken (C /ˈtʃɪk.ɪn/, O /ˈtʃɪkən/) - čikn
      1. tʃ - č
      2. ɪ - i
      3. k - k
      4. ɪn/$, ən - n
   */
  {
    english: "chicken",
    transcriptions: {
      cambridge: "ˈtʃɪk.ɪn",
      opendict: "ˈtʃɪkən",
    },
    newEnglish: "čikn",
    breakdown: [
      {
        sounds: ["tʃ"],
        mapping: "č",
      },
      {
        sounds: ["ɪ"],
        mapping: "i",
      },
      {
        sounds: ["k"],
        mapping: "k",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɪn$"],
          mapping: "n",
        },
        {
          dictionaryKey: "any",
          sounds: ["ən"],
          mapping: "n",
        },
      ],
    ],
  },

  /**
    16. zebra (C /ˈziː.brə/, O /ˈzibɹə/) - zibra
      1. z - z
      2. i - i
      3. b - b
      4. r/ɹ - r
      5. ə$ - a
   */
  {
    english: "zebra",
    transcriptions: {
      cambridge: "ˈziː.brə",
      opendict: "ˈzibɹə",
    },
    newEnglish: "zibra",
    breakdown: [
      {
        sounds: ["z"],
        mapping: "z",
      },
      {
        sounds: ["i"],
        mapping: "i",
      },
      {
        sounds: ["b"],
        mapping: "b",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["ə$"],
        mapping: "a",
      },
    ],
  },

  /**
    17. television (C /ˈtel.ə.vɪʒ.ən/, O /ˈtɛɫəˌvɪʒən/) - televijn
      1. t - t
      2. e/ɛ - e
      3. lə/ɫə - le
      4. v - v
      5. ɪ - i
      6. ʒ - j
      7. ən - n
   */
  {
    english: "television",
    transcriptions: {
      cambridge: "ˈtel.ə.vɪʒ.ən",
      opendict: "ˈtɛɫəˌvɪʒən",
    },
    newEnglish: "televijn",
    breakdown: [
      {
        sounds: ["t"],
        mapping: "t",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e",
        },
      ],
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["lə"],
          mapping: "le",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫə"],
          mapping: "le",
        },
      ],
      {
        sounds: ["v"],
        mapping: "v",
      },
      {
        sounds: ["ɪ"],
        mapping: "i",
      },
      {
        sounds: ["ʒ"],
        mapping: "j",
      },
      {
        sounds: ["ən"],
        mapping: "n",
      },
    ],
  },

  /**
    18. giraffe (C /dʒɪˈræf/, O /dʒɝˈæf/) - djirāf
      1. d - d
      2. ʒ - j
      3. ɪˈr/ɝˈ - ir
      4. æ - ā
      5. f - f
   */
  {
    english: "giraffe",
    transcriptions: {
      cambridge: "dʒɪˈræf",
      opendict: "dʒɝˈæf",
    },
    newEnglish: "djirāf",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d",
      },
      {
        sounds: ["ʒ"],
        mapping: "j",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɪˈr"],
          mapping: "ir",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɝˈ"],
          mapping: "ir",
        },
      ],
      {
        sounds: ["æ"],
        mapping: "ā",
      },
      {
        sounds: ["f"],
        mapping: "f",
      },
    ],
  },

  /**
    19. wolf (C /wʊlf/, O /ˈwʊɫf/) - wolf
      1. w - w
      2. ʊ - o
      3. l/ɫ - l
      4. f - f
   */
  {
    english: "wolf",
    transcriptions: {
      cambridge: "wʊlf",
      opendict: "ˈwʊɫf",
    },
    newEnglish: "wolf",
    breakdown: [
      {
        sounds: ["w"],
        mapping: "w",
      },
      {
        sounds: ["ʊ"],
        mapping: "o",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l",
        },
      ],
      {
        sounds: ["f"],
        mapping: "f",
      },
    ],
  },

  /**
    20. lion (C /ˈlaɪ.ən/, O /ˈɫaɪən/) - laín
      1. l/ɫ - l
      2. aɪ - aí
      3. ən - n
   */
  {
    english: "lion",
    transcriptions: {
      cambridge: "ˈlaɪ.ən",
      opendict: "ˈɫaɪən",
    },
    newEnglish: "laín",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l",
        },
      ],
      {
        sounds: ["aɪ"],
        mapping: "aí",
      },
      {
        sounds: ["ən"],
        mapping: "n",
      },
    ],
  },

  /**
    21. mouse (C /maʊs/, O /ˈmaʊs/) - mays
      1. m - m
      2. aʊ - ay
      3. s - s
   */
  {
    english: "mouse",
    transcriptions: {
      cambridge: "maʊs",
      opendict: "ˈmaʊs",
    },
    newEnglish: "mays",
    breakdown: [
      {
        sounds: ["m"],
        mapping: "m",
      },
      {
        sounds: ["aʊ"],
        mapping: "ay",
      },
      {
        sounds: ["s"],
        mapping: "s",
      },
    ],
  },

  /**
    22. dinosaur (C /ˈdaɪ.nə.sɔːr/, O /ˈdaɪnəˌsɔɹ/) - daínaso
      1. d - d
      2. aɪ - aí
      3. n - n
      4. ə - a
      5. s - s
      6. ɔːr/ɔɹ - o
   */
  {
    english: "dinosaur",
    transcriptions: {
      cambridge: "ˈdaɪ.nə.sɔːr",
      opendict: "ˈdaɪnəˌsɔɹ",
    },
    newEnglish: "daínaso",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d",
      },
      {
        sounds: ["aɪ"],
        mapping: "aí",
      },
      {
        sounds: ["n"],
        mapping: "n",
      },
      {
        sounds: ["ə"],
        mapping: "a",
      },
      {
        sounds: ["s"],
        mapping: "s",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɔːr"],
          mapping: "o",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔɹ"],
          mapping: "o",
        },
      ],
    ],
  },

  /**
    23. penguin (C /ˈpeŋ.ɡwɪn/, O /ˈpɛŋɡwən/) - pengwn
      1. p - p
      2. e/ɛ - e
      3. ŋ - n
      4. ɡ - g
      5. w - w
      6. ɪn$/, ən - n
   */
  {
    english: "penguin",
    transcriptions: {
      cambridge: "ˈpeŋ.ɡwɪn",
      opendict: "ˈpɛŋɡwən",
    },
    newEnglish: "pengwn",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["e"],
          mapping: "e",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɛ"],
          mapping: "e",
        },
      ],
      {
        sounds: ["ŋ"],
        mapping: "n",
      },
      {
        sounds: ["ɡ"],
        mapping: "g",
      },
      {
        sounds: ["w"],
        mapping: "w",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɪn$"],
          mapping: "n",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ən"],
          mapping: "n",
        },
      ],
    ],
  },

  /**
    24. rabbit (C /ˈræb.ɪt/, O /ˈɹæbət/, /ˈɹæbɪt/) - rābit
      1. r/ɹ - r
      2. æ - ā
      3. bi//bə/bɪ - bi
      4. t - t
   */
  {
    english: "rabbit",
    transcriptions: {
      cambridge: "ˈræb.ɪt",
      opendict: "ˈɹæbət",
    },
    newEnglish: "rābit",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["æ"],
        mapping: "ā",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["bi"],
          mapping: "bi",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["bə"],
          mapping: "bi",
        },
      ],
      {
        sounds: ["t"],
        mapping: "t",
      },
    ],
  },

  /**
    25. yak (C /jæk/, O /ˈjæk/) - íāk
      1. j - í
      2. æ - ā
      3. k - k
   */
  {
    english: "yak",
    transcriptions: {
      cambridge: "jæk",
      opendict: "ˈjæk",
    },
    newEnglish: "íāk",
    breakdown: [
      {
        sounds: ["j"],
        mapping: "í",
      },
      {
        sounds: ["æ"],
        mapping: "ā",
      },
      {
        sounds: ["k"],
        mapping: "k",
      },
    ],
  },

  /**
    26. horse (C /hɔːrs/, O /ˈhɔɹs/) - hos
      1. h - h
      2. ɔːr/ɔɹ - o
      3. s - s
   */
  {
    english: "horse",
    transcriptions: {
      cambridge: "hɔːrs",
      opendict: "ˈhɔɹs",
    },
    newEnglish: "hos",
    breakdown: [
      {
        sounds: ["h"],
        mapping: "h",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["ɔːr"],
          mapping: "o",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔɹ"],
          mapping: "o",
        },
      ],
      {
        sounds: ["s"],
        mapping: "s",
      },
    ],
  },

  /**
    27. green (C /ɡriːn/, O /ˈɡɹin/) - grin
      1. ɡ - g
      2. r/ɹ - r
      3. i - i
      4. n - n
   */
  {
    english: "green",
    transcriptions: {
      cambridge: "ɡriːn",
      opendict: "ˈɡɹin",
    },
    newEnglish: "grin",
    breakdown: [
      {
        sounds: ["ɡ"],
        mapping: "g",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["i"],
        mapping: "i",
      },
      {
        sounds: ["n"],
        mapping: "n",
      },
    ],
  },

  /**
    28. pink (C /pɪŋk/, O /ˈpɪŋk/) - pink
      1. p - p
      2. ɪ - i
      3. ŋ - n
      4. k - k
   */
  {
    english: "pink",
    transcriptions: {
      cambridge: "pɪŋk",
      opendict: "ˈpɪŋk",
    },
    newEnglish: "pink",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p",
      },
      {
        sounds: ["ɪ"],
        mapping: "i",
      },
      {
        sounds: ["ŋ"],
        mapping: "n",
      },
      {
        sounds: ["k"],
        mapping: "k",
      },
    ],
  },

  /**
    29. wood (C /wʊd/, O /ˈwʊd/) - wot
      1. w - w
      2. ʊ - o
      3. d$ - t
   */
  {
    english: "wood",
    transcriptions: {
      cambridge: "wʊd",
      opendict: "ˈwʊd",
    },
    newEnglish: "wot",
    breakdown: [
      {
        sounds: ["w"],
        mapping: "w",
      },
      {
        sounds: ["ʊ"],
        mapping: "o",
      },
      {
        sounds: ["d$"],
        mapping: "t",
      },
    ],
  },

  /**
    30. blue (C /bluː/, O /ˈbɫu/) - blu
      1. b - b
      2. l/ɫ - l
      3. u - u
   */
  {
    english: "blue",
    transcriptions: {
      cambridge: "bluː",
      opendict: "ˈbɫu",
    },
    newEnglish: "blu",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l",
        },
      ],
      {
        sounds: ["u"],
        mapping: "u",
      },
    ],
  },

  /**
    31. dust (C /dʌst/, O /ˈdəst/) - dast
      1. dʌ/də - da
      2. s - s
      3. t - t
   */
  {
    english: "dust",
    transcriptions: {
      cambridge: "dʌst",
      opendict: "ˈdəst",
    },
    newEnglish: "dast",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["dʌ"],
          mapping: "da",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["də"],
          mapping: "da",
        },
      ],
      {
        sounds: ["s"],
        mapping: "s",
      },
      {
        sounds: ["t"],
        mapping: "t",
      },
    ],
  },

  /**
    32. purple (C /ˈpɝː.pəl/, O /ˈpɝpəɫ/) - pëpl
      1. p - p
      2. ɝ - ë
      3. p - p
      4. əl/əɫ$ - l
   */
  {
    english: "purple",
    transcriptions: {
      cambridge: "ˈpɝː.pəl",
      opendict: "ˈpɝpəɫ",
    },
    newEnglish: "pëpl",
    breakdown: [
      {
        sounds: ["p"],
        mapping: "p",
      },
      {
        sounds: ["ɝ"],
        mapping: "ë",
      },
      {
        sounds: ["p"],
        mapping: "p",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["əl$"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["əɫ$"],
          mapping: "l",
        },
      ],
    ],
  },

  /**
    33. mauve (C /moʊv/, O /ˈmɔv/) - mōf, maf
      1. m - m
      2. oʊ - ō, ɔ - a
      3. v$ - f
   */
  {
    english: "mauve",
    transcriptions: {
      cambridge: "moʊv",
      opendict: "ˈmɔv",
    },
    newEnglish: {
      cambridge: "mōf",
      opendict: "maf",
    },
    breakdown: [
      {
        sounds: ["m"],
        mapping: "m",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["oʊ"],
          mapping: "ō",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɔ"],
          mapping: "a",
        },
      ],
      {
        sounds: ["v$"],
        mapping: "f",
      },
    ],
  },

  /**
    34. sand (C /sænd/, O /ˈsænd/) - sānt
      1. s - s
      2. æ - ā
      3. n - n
      4. d$ - t
   */
  {
    english: "sand",
    transcriptions: {
      cambridge: "sænd",
      opendict: "ˈsænd",
    },
    newEnglish: "sānt",
    breakdown: [
      {
        sounds: ["s"],
        mapping: "s",
      },
      {
        sounds: ["æ"],
        mapping: "ā",
      },
      {
        sounds: ["n"],
        mapping: "n",
      },
      {
        sounds: ["d$"],
        mapping: "t",
      },
    ],
  },

  /**
    35. coffee (C /ˈkɑː.fi/, O /ˈkɑfi/, /ˈkɔfi/) - kafi
      1. k - k
      2. ɑ, //ɔ  - a
      3. f - f
      4. i - i
   */
  {
    english: "coffee",
    transcriptions: {
      cambridge: "ˈkɑː.fi",
      opendict: "ˈkɑfi",
    },
    newEnglish: "kafi",
    breakdown: [
      {
        sounds: ["k"],
        mapping: "k",
      },
      [
        {
          dictionaryKey: "opendict",
          sounds: ["ɔ"],
          mapping: "a",
        },
        {
          dictionaryKey: "any",
          sounds: ["ɑ"],
          mapping: "a",
        },
      ],
      {
        sounds: ["f"],
        mapping: "f",
      },
      {
        sounds: ["i"],
        mapping: "i",
      },
    ],
  },

  /**
    36. jade (C /dʒeɪd/, O /ˈdʒeɪd/) - djeit
      1. d - d
      2. ʒ - j
      3. eɪ - eí
      4. d$ - t
   */
  {
    english: "jade",
    transcriptions: {
      cambridge: "dʒeɪd",
      opendict: "ˈdʒeɪd",
    },
    newEnglish: "djeit",
    breakdown: [
      {
        sounds: ["d"],
        mapping: "d",
      },
      {
        sounds: ["ʒ"],
        mapping: "j",
      },
      {
        sounds: ["eɪ"],
        mapping: "eí",
      },
      {
        sounds: ["d$"],
        mapping: "t",
      },
    ],
  },

  /**
    37. gold (C /ɡoʊld/, O /ˈɡoʊɫd/) - gōlt
      1. ɡ - g
      2. oʊ - ō
      3. l/ɫ - l
      4. d$ - t
   */
  {
    english: "gold",
    transcriptions: {
      cambridge: "ɡoʊld",
      opendict: "ˈɡoʊɫd",
    },
    newEnglish: "gōlt",
    breakdown: [
      {
        sounds: ["ɡ"],
        mapping: "g",
      },
      {
        sounds: ["oʊ"],
        mapping: "ō",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l",
        },
      ],
      {
        sounds: ["d$"],
        mapping: "t",
      },
    ],
  },

  /**
    38. turquoise (C /ˈtɝː.kɔɪz/, O /ˈtɝkwɔɪz/) - tëkoís
      1. t - t
      2. ɝ - ë
      3. k - k
      4. ɔɪ - oí
      5. z$ - s
   */
  {
    english: "turquoise",
    transcriptions: {
      cambridge: "ˈtɝː.kɔɪz",
      opendict: "ˈtɝkwɔɪz",
    },
    newEnglish: "tëkoís",
    breakdown: [
      {
        sounds: ["t"],
        mapping: "t",
      },
      {
        sounds: ["ɝ"],
        mapping: "ë",
      },
      {
        sounds: ["k"],
        mapping: "k",
      },
      {
        sounds: ["ɔɪ"],
        mapping: "oí",
      },
      {
        sounds: ["z$"],
        mapping: "s",
      },
    ],
  },

  /**
    39. lime (C /laɪm/, O /ˈɫaɪm/) - laím
      1. l/ɫ - l
      2. aɪ - aí
      3. m - m
   */
  {
    english: "lime",
    transcriptions: {
      cambridge: "laɪm",
      opendict: "ˈɫaɪm",
    },
    newEnglish: "laím",
    breakdown: [
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["l"],
          mapping: "l",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɫ"],
          mapping: "l",
        },
      ],
      {
        sounds: ["aɪ"],
        mapping: "aí",
      },
      {
        sounds: ["m"],
        mapping: "m",
      },
    ],
  },

  /**
    40. brown (C /braʊn/, O /ˈbɹaʊn/) - brayn
      1. b - b
      2. r/ɹ - r
      3. aʊ - ay
      4. n - n
   */
  {
    english: "brown",
    transcriptions: {
      cambridge: "braʊn",
      opendict: "ˈbɹaʊn",
    },
    newEnglish: "brayn",
    breakdown: [
      {
        sounds: ["b"],
        mapping: "b",
      },
      [
        {
          dictionaryKey: "cambridge",
          sounds: ["r"],
          mapping: "r",
        },
        {
          dictionaryKey: "opendict",
          sounds: ["ɹ"],
          mapping: "r",
        },
      ],
      {
        sounds: ["aʊ"],
        mapping: "ay",
      },
      {
        sounds: ["n"],
        mapping: "n",
      },
    ],
  },
];
