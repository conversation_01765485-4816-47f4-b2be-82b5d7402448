"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SitemapController_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SitemapController = void 0;
const common_1 = require("@nestjs/common");
const sitemap_service_1 = require("./sitemap.service");
const zod_1 = require("../zod");
const api_1 = require("@commune/api");
let SitemapController = SitemapController_1 = class SitemapController {
    constructor(sitemapService) {
        this.sitemapService = sitemapService;
        this.logger = new common_1.Logger(SitemapController_1.name);
    }
    async getSitemapGenerationData(key) {
        const data = await this.sitemapService.getSitemapGenerationData(key);
        const parsedData = api_1.Sitemap.GetSitemapGenerationDataOutputSchema.safeParse(data);
        if (!parsedData.success) {
            this.logger.error(parsedData.error.issues);
            throw new common_1.InternalServerErrorException();
        }
        return parsedData.data;
    }
};
exports.SitemapController = SitemapController;
__decorate([
    (0, common_1.Get)("generation-data"),
    __param(0, (0, common_1.Query)("key", new zod_1.ZodPipe(zod_1.z.string().nonempty()))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], SitemapController.prototype, "getSitemapGenerationData", null);
exports.SitemapController = SitemapController = SitemapController_1 = __decorate([
    (0, common_1.Controller)("sitemap"),
    __metadata("design:paramtypes", [sitemap_service_1.SitemapService])
], SitemapController);
//# sourceMappingURL=sitemap.controller.js.map