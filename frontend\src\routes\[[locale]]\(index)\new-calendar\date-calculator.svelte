<script lang="ts">
  import type { Common } from "@commune/api";

  import { NewCalendarDate } from "$lib";

  interface Props {
    locale: Common.WebsiteLocale;
  }

  const i18n = {
    en: {
      description: "Convert any date to our new calendar system",
      selectDate: "Select a date",
      isLeapYear: {
        title: "Is Leap Year",
        yes: "yes",
        no: "no",
      },
      dayOfYear: "Day of Year",
      day: "Day",
      month: "Month",
      year: "Year",
      commonString: "Common Format",
      isoString: "ISO Format",
      peaceDay: {
        title: "Peace Day",
        description: "The 365th day of the year that falls outside the regular month structure.",
      },
      leapDay: {
        title: "Leap Day",
        description: "The 366th day of the year that only occurs in leap years.",
      },
    },
    ru: {
      description: "Конвертировать любую дату в наш новый календарь",
      selectDate: "Выберите дату",
      isLeapYear: {
        title: "Високосный год",
        yes: "да",
        no: "нет",
      },
      dayOfYear: "День года",
      day: "День",
      month: "Месяц",
      year: "Год",
      commonString: "Бытовой формат",
      isoString: "Формат ISO",
      peaceDay: {
        title: "День мира",
        description: "365-й день года, который выпадает за пределы регулярной структуры месяцев.",
      },
      leapDay: {
        title: "Високосный день",
        description: "366-й день года, который встречается только в високосных годах.",
      },
    },
  };

  const { locale }: Props = $props();

  const t = $derived(i18n[locale]);

  function calculateNewCalendarDate(date: Date) {
    const ncDate = new NewCalendarDate(date);
    const isLeapYear = ncDate.getIsLeapYear();
    const dayOfYear = ncDate.getDayOfYear();
    const parsed = ncDate.getParsed();
    const commonString = ncDate.toString().slice(0, 10);

    return {
      newCalendarDate: ncDate,
      isLeapYear,
      dayOfYear,
      parsed,
      commonString,
      isoString: ncDate.toISOString().slice(0, 10),
    };
  }

  let selectedDate = $state(new Date());
  const calculatedDate = $derived(calculateNewCalendarDate(selectedDate));

  function handleSelectedDateChange(e: Event) {
    const input = e.target as HTMLInputElement;
    const newDate = new Date(input.value);

    if (!Number.isNaN(newDate.getTime())) {
      selectedDate = newDate;
    }
  }
</script>

<div class="card">
  <div class="card-body">
    <p class="lead">{t.description}</p>

    <div class="row mb-4">
      <div class="col-md-6">
        <label for="date-picker" class="form-label">{t.selectDate}:</label>
        <input
          id="date-picker"
          class="form-control"
          type="date"
          value={selectedDate.toISOString().split("T")[0]}
          onchange={handleSelectedDateChange}
        />
      </div>
    </div>

    {#if calculatedDate}
      <div class="row">
        <div class="col-md-12">
          <div class="alert alert-success">
            <h4 class="alert-heading">
              {selectedDate.toLocaleDateString()} → {calculatedDate.commonString}
            </h4>
            <hr />
            <div class="row">
              <div class="col-md-6">
                <ul class="list-unstyled">
                  <li>
                    <strong>{t.isLeapYear.title}:</strong>
                    {" "}
                    {calculatedDate.isLeapYear ? t.isLeapYear.yes : t.isLeapYear.no}
                  </li>
                  <li>
                    <strong>{t.dayOfYear}:</strong>
                    {" "}
                    {calculatedDate.dayOfYear}
                  </li>
                  <li>
                    <strong>{t.day}:</strong>
                    {" "}
                    {calculatedDate.parsed.day}
                  </li>
                  <li>
                    <strong>{t.month}:</strong>
                    {" "}
                    {calculatedDate.parsed.month}
                  </li>
                  <li>
                    <strong>{t.year}:</strong>
                    {" "}
                    {calculatedDate.parsed.year}
                  </li>
                  <li>
                    <strong>{t.commonString}:</strong>
                    {" "}
                    {calculatedDate.commonString}
                  </li>
                  <li>
                    <strong>{t.isoString}:</strong>
                    {" "}
                    {calculatedDate.isoString}
                  </li>
                </ul>
              </div>
              <div class="col-md-6">
                {#if calculatedDate.parsed.month === 14}
                  <div class="special-day-info">
                    <h5>
                      {calculatedDate.parsed.day === 1 ? t.peaceDay.title : t.leapDay.title}
                    </h5>
                    <p>
                      {calculatedDate.parsed.day === 1
                        ? t.peaceDay.description
                        : t.leapDay.description}
                    </p>
                  </div>
                {/if}
              </div>
            </div>
          </div>
        </div>
      </div>
    {/if}
  </div>
</div>
