// @ts-nocheck
import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const [
    me,
    [user],
    karmaPoints,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.user.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.rating.karma.list.get({ userId: params.id }, { fetch, ctx: { url } }),
  ]);

  if (!user) {
    throw error(404, "User not found");
  }

  return {
    me,
    user,
    karmaPoints,
    isHasMoreKarma: karmaPoints.length === Consts.PAGE_SIZE,
  };
};