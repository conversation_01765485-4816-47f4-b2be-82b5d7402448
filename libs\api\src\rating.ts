import type { Infer } from "./types";

import { z } from "zod";
import { id, LocalizationsSchema, PaginationSchema } from "./common";
import { SimpleUserSchema } from "./user";

// karma

export const karmaPointQuantity = z.number().int();
export const karmaPointComment = LocalizationsSchema.min(1);

export type GetKarmaPointsInput = Infer<typeof GetKarmaPointsInputSchema>;
export const GetKarmaPointsInputSchema = z.object({
    pagination: PaginationSchema,

    userId: id,
});

export type GetKarmaPointsOutput = Infer<typeof GetKarmaPointsOutputSchema>;
export const GetKarmaPointsOutputSchema = z.array(
    z.object({
        id,
        author: SimpleUserSchema,
        quantity: karmaPointQuantity,
        comment: karmaPointComment,
    }),
);

export type SpendKarmaPointInput = Infer<typeof SpendKarmaPointInputSchema>;
export const SpendKarmaPointInputSchema = z.object({
    sourceUserId: id,
    targetUserId: id,
    quantity: karmaPointQuantity,
    comment: karmaPointComment,
});

// feedback

export const userFeedbackValue = z.number().int().min(0).max(10);
export const userFeedbackText = LocalizationsSchema.min(1);

export type GetUserFeedbacksInput = Infer<typeof GetUserFeedbacksInputSchema>;
export const GetUserFeedbacksInputSchema = z.object({
    pagination: PaginationSchema,

    userId: id,
});

export type GetUserFeedbacksOutput = Infer<typeof GetUserFeedbacksOutputSchema>;
export const GetUserFeedbacksOutputSchema = z.array(
    z.object({
        id,
        author: SimpleUserSchema.nullable(),
        isAnonymous: z.boolean(),
        value: userFeedbackValue,
        text: userFeedbackText,
    }),
);

export type CreateUserFeedbackInput = Infer<typeof CreateUserFeedbackInputSchema>;
export const CreateUserFeedbackInputSchema = z.object({
    sourceUserId: id,
    targetUserId: id,
    value: userFeedbackValue,
    isAnonymous: z.boolean(),
    text: userFeedbackText,
});

// summary

export type GetUserSummaryInput = Infer<typeof GetUserSummaryInputSchema>;
export const GetUserSummaryInputSchema = z.object({
    userId: id,
});

export type GetUserSummaryOutput = Infer<typeof GetUserSummaryOutputSchema>;
export const GetUserSummaryOutputSchema = z.object({
    rating: z.number().int(),
    karma: z.number().int(),
    rate: z.number().min(0).max(10).nullable(),
});
