"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VARIANT_COUNT = exports.getRandomRuAnimal = exports.getRandomEnAnimal = void 0;
const en_1 = require("./en");
const ru_1 = require("./ru");
var en_2 = require("./en");
Object.defineProperty(exports, "getRandomEnAnimal", { enumerable: true, get: function () { return en_2.getRandomAnimal; } });
var ru_2 = require("./ru");
Object.defineProperty(exports, "getRandomRuAnimal", { enumerable: true, get: function () { return ru_2.getRandomAnimal; } });
if (en_1.WORDS.length !== ru_1.WORDS.length) {
    throw new Error("Animals WORDS_EN and WORDS_RU must have the same length");
}
exports.VARIANT_COUNT = en_1.WORDS.length;
//# sourceMappingURL=index.js.map