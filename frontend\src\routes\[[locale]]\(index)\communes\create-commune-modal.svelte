<script lang="ts">
  import { Consts, type Common } from "@commune/api";

  import { preventDefault } from "$lib";
  import { goto } from "$app/navigation";
  import { getClient } from "$lib/acrpc";
  import { Modal, LocalizedInput, LocalizedTextarea } from "$lib/components";
  import { HttpError } from "@commune/api/acrpc/client";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    toLocaleHref: (href: string) => string;
  }

  const i18n = {
    en: {
      createNewCommune: "Create New Commune",
      communeCreatedSuccess: "Commune created successfully!",
      name: "Name",
      enterCommuneName: "Enter commune name",
      description: "Description (optional)",
      enterCommuneDescription: "Enter commune description",
      cancel: "Cancel",
      create: "Create",
      creating: "Creating...",
      provideName: "Please provide name for the commune.",
      failedToCreate: "Failed to create commune",
      unexpectedError: "An unexpected error occurred. Please try again.",
      youReachedMaxCommunesLimit: `You have reached the maximum number of communes where you are member (${Consts.MAX_COMMUNES_TO_BE_MEMBER}).`,
      youReachedMaxCommuneHeadsLimit: `You have reached the maximum number of communes where you are head (${Consts.MAX_COMMUNES_TO_BE_HEAD}).`,
    },
    ru: {
      createNewCommune: "Создать новую коммуну",
      communeCreatedSuccess: "Коммуна успешно создана!",
      name: "Название",
      enterCommuneName: "Введите название коммуны",
      description: "Описание (опционально)",
      enterCommuneDescription: "Введите описание коммуны",
      cancel: "Отмена",
      create: "Создать",
      creating: "Создание...",
      provideName: "Пожалуйста, укажите название коммуны.",
      failedToCreate: "Не удалось создать коммуну",
      unexpectedError: "Произошла непредвиденная ошибка. Пожалуйста, попробуйте снова.",
      youReachedMaxCommunesLimit: `Вы достигли максимального количества коммун, где вы являетесь участником (${Consts.MAX_COMMUNES_TO_BE_MEMBER}).`,
      youReachedMaxCommuneHeadsLimit: `Вы достигли максимального количества коммун, где вы являетесь главой (${Consts.MAX_COMMUNES_TO_BE_HEAD}).`,
    },
  };

  const { fetcher: api } = getClient();

  const { show, locale, onHide, toLocaleHref }: Props = $props();

  const t = $derived(i18n[locale]);

  let fileInputRef = $state<HTMLInputElement | null>(null);
  let communeName = $state<Common.Localizations>([]);
  let communeDescription = $state<Common.Localizations>([]);
  let error = $state<string>("");
  let isSubmitting = $state(false);
  let submitSuccess = $state(false);

  function handleClose() {
    onHide();
    communeName = [];
    communeDescription = [];
    error = "";
    submitSuccess = false;

    if (fileInputRef) {
      fileInputRef.value = "";
    }
  }

  async function handleSubmit() {
    error = "";

    // Validate required fields
    if (!communeName.some((item) => item.value.trim().length)) {
      error = t.provideName;
      return;
    }

    isSubmitting = true;

    try {
      const { id } = await api.commune.post({
        name: communeName,
        description: communeDescription,
      });

      submitSuccess = true;

      setTimeout(() => {
        goto(toLocaleHref(`/communes/${id}`));
      }, 1500);
    } catch (err) {
      if (err instanceof HttpError) {
        if (err.description.includes("user_reached_max_communes_limit")) {
          error = t.youReachedMaxCommunesLimit;

          return;
        }

        if (err.description.includes("user_reached_max_commune_heads_limit")) {
          error = t.youReachedMaxCommuneHeadsLimit;

          return;
        }
      }

      console.error("Error creating commune:", err);
      error = t.unexpectedError;
    } finally {
      isSubmitting = false;
    }
  }
</script>

<Modal
  {show}
  title={t.createNewCommune}
  onClose={handleClose}
  onSubmit={handleSubmit}
  submitText={isSubmitting ? t.creating : t.create}
  cancelText={t.cancel}
  submitDisabled={!communeName.some((item) => item.value.trim().length) || isSubmitting}
  cancelDisabled={isSubmitting}
  {isSubmitting}
>
  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.communeCreatedSuccess}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <form onsubmit={preventDefault(handleSubmit)}>
    <LocalizedInput
      id="communeName"
      label={t.name}
      placeholder={t.enterCommuneName}
      required={true}
      {locale}
      bind:value={communeName}
    />

    <LocalizedTextarea
      label={t.description}
      placeholder={t.enterCommuneDescription}
      rows={4}
      {locale}
      bind:value={communeDescription}
    />
  </form>
</Modal>
