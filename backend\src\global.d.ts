declare type NonNullableUnknown = NonNullable<unknown>;

declare type SuggestableString<T extends string> =
    | T
    | (string & NonNullableUnknown);

declare type Satisfies<T, U extends T> = U;

declare type Normalize<T> = { [K in keyof T]: T[K] } & NonNullableUnknown;

declare type DeepPartialUnknown<T> = Normalize<{
    [K in keyof T]-?: T[K] extends object ? DeepPartialUnknown<T[K]> : unknown;
}>;

declare type DeepReadonly<T> = Normalize<{
    readonly [K in keyof T]: T[K] extends object ? DeepReadonly<T[K]> : T[K];
}>;
