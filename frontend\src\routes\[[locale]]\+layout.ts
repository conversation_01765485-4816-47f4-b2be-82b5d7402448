import type { LayoutLoad, LayoutLoadEvent } from "./$types";

import { Common } from "@commune/api";
import { browser } from "$app/environment";
import { getAppropriateLocalizationFactory, LocalStorageUser, removeCurrentUser, setCurrentUser } from "$lib";

export const load: LayoutLoad = (event) => {
  const me = event.data.me;
  const user: LocalStorageUser | null = me
    ? {
      id: me.id,
      email: me.email,
      role: me.role,
    }
    : null;

  if (browser) {
    if (!me) {
      removeCurrentUser();
    }

    if (me) {
      setCurrentUser(user!);
    }
  }

  const routeLocale = getRouteLocale(event);
  const hrefLocale = routeLocale ? `/${routeLocale}` : "";
  const locale = routeLocale ?? event.data.preferredLocale ?? "en";

  return {
    routeLocale,
    preferredLocale: event.data.preferredLocale,
    locale,
    user,
    toLocaleHref(href: string) {
      return `${hrefLocale}${href}`;
    },
    getAppropriateLocalization: getAppropriateLocalizationFactory(
      routeLocale,
      event.data.userLocales,
    ),
  };
};

function getRouteLocale(event: LayoutLoadEvent) {
  const parsedLocale = Common.WebsiteLocaleSchema.safeParse(event.params.locale);

  if (parsedLocale.success) {
    return parsedLocale.data;
  }

  return null;
}
