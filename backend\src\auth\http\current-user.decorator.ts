import {
    createParamDecorator,
    ExecutionContext,
    UnauthorizedException,
} from "@nestjs/common";
import { HTTP_CONTEXT_USER_SYMBOL } from "src/consts";
import { CurrentUser, RawCurrentUser } from "../types";
import { UserRole } from "@prisma/client";

export const HttpCurrentUser = createParamDecorator(
    (_: unknown, ctx: ExecutionContext) => {
        const request = ctx.switchToHttp().getRequest();

        const rawUser: RawCurrentUser | undefined = request.user;

        if (!rawUser) {
            throw new UnauthorizedException();
        }

        const currentUser: CurrentUser = {
            ...rawUser,

            isAdmin: rawUser.role === UserRole.admin,
        };

        request[HTTP_CONTEXT_USER_SYMBOL] = currentUser;

        return currentUser;
    },
);
