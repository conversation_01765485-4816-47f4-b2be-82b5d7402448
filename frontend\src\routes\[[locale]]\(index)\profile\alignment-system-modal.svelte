<script lang="ts">
  import type { Common, User } from "@commune/api";

  import { getClient } from "$lib/acrpc";
  import { Modal } from "$lib/components";

  interface Props {
    locale: Common.WebsiteLocale;
    show: boolean;
    onHide: () => void;
    currentAlignment: User.UserAlignmentSystemType | null;
    userId: string;
    onAlignmentUpdated: () => void;
  }

  const i18n = {
    en: {
      title: "Select Alignment",
      alignmentTypes: {
        lawfulGood: "Lawful Good",
        neutralGood: "Neutral Good",
        chaoticGood: "Chaotic Good",
        lawfulNeutral: "Lawful Neutral",
        trueNeutral: "True Neutral",
        chaoticNeutral: "Chaotic Neutral",
        lawfulEvil: "Lawful Evil",
        neutralEvil: "Neutral Evil",
        chaoticEvil: "Chaotic Evil",
      },
      selectAlignment: "Select your alignment",
      currentAlignment: "Current alignment",
      clearAlignment: "Clear alignment",
      updating: "Updating...",
      updated: "Alignment updated successfully!",
      errorOccurred: "An error occurred while updating alignment",
      cancel: "Cancel",
      alignmentSystemLink: "https://en.wikipedia.org/wiki/Alignment_(Dungeons_%26_Dragons)",
      alignmentSystemLinkLabel: "Alignment System in D&D (Wikipedia)",
    },
    ru: {
      title: "Выбор мировоззрения",
      alignmentTypes: {
        lawfulGood: "Lawful Good",
        neutralGood: "Neutral Good",
        chaoticGood: "Chaotic Good",
        lawfulNeutral: "Lawful Neutral",
        trueNeutral: "True Neutral",
        chaoticNeutral: "Chaotic Neutral",
        lawfulEvil: "Lawful Evil",
        neutralEvil: "Neutral Evil",
        chaoticEvil: "Chaotic Evil",
      },
      selectAlignment: "Выберите ваше мировоззрение",
      currentAlignment: "Текущее мировоззрение",
      clearAlignment: "Очистить мировоззрение",
      updating: "Обновление...",
      updated: "Мировоззрение успешно обновлено!",
      errorOccurred: "Произошла ошибка при обновлении мировоззрения",
      cancel: "Отмена",
      alignmentSystemLink: "https://ru.wikipedia.org/wiki/Мировоззрение_в_Dungeons_%26_Dragons",
      alignmentSystemLinkLabel: "Мировоззрение в D&D (Википедия)",
    },
  };

  const { locale, show, onHide, currentAlignment, userId, onAlignmentUpdated }: Props = $props();

  const { fetcher: api } = getClient();
  const t = $derived(i18n[locale]);

  let isSubmitting = $state(false);
  let submitSuccess = $state(false);
  let error = $state("");

  const alignmentOptions: User.UserAlignmentSystemType[] = [
    "lawfulGood",
    "neutralGood",
    "chaoticGood",
    "lawfulNeutral",
    "trueNeutral",
    "chaoticNeutral",
    "lawfulEvil",
    "neutralEvil",
    "chaoticEvil",
  ];

  const alignmentImages: Record<User.UserAlignmentSystemType, string> = {
    lawfulGood: "lawful-good",
    neutralGood: "neutral-good",
    chaoticGood: "chaotic-good",
    lawfulNeutral: "lawful-neutral",
    trueNeutral: "true-neutral",
    chaoticNeutral: "chaotic-neutral",
    lawfulEvil: "lawful-evil",
    neutralEvil: "neutral-evil",
    chaoticEvil: "chaotic-evil",
  };

  const getAlignmentImagePath = (alignment: User.UserAlignmentSystemType) => {
    return `/images/alignment-system/kung-fu-panda/${alignmentImages[alignment]}.png`;
  };

  const handleAlignmentSelect = async (selectedAlignment: User.UserAlignmentSystemType) => {
    // If clicking current alignment, clear it (set to null)
    const newAlignment = selectedAlignment === currentAlignment ? null : selectedAlignment;

    isSubmitting = true;
    error = "";

    try {
      await api.user.patch({
        id: userId,
        alignmentSystemType: newAlignment,
      });

      submitSuccess = true;

      // Close modal after a short delay
      setTimeout(() => {
        handleClose();
        onAlignmentUpdated();
      }, 1500);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isSubmitting = false;
    }
  };

  const handleClose = () => {
    error = "";
    submitSuccess = false;
    onHide();
  };
</script>

<Modal {show} onClose={handleClose} showFooter={false} size="lg">
  {#snippet title()}
    {t.title}
    <a href={t.alignmentSystemLink} target="_blank" aria-label={t.alignmentSystemLinkLabel}>
      <i class="bi bi-question-circle"></i>
    </a>
  {/snippet}

  {#if submitSuccess}
    <div class="alert alert-success mb-3">
      {t.updated}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger mb-3">
      {error}
    </div>
  {/if}

  <div class="row g-3">
    {#each alignmentOptions as alignment}
      <div class="col-md-4">
        <button
          class={`btn btn-outline-secondary  w-100 h-100 p-3 position-relative alignment-button ${
            currentAlignment === alignment ? "active border-primary" : ""
          }`}
          onclick={() => handleAlignmentSelect(alignment)}
          disabled={isSubmitting}
          style="min-height: 120px;"
        >
          <div class="d-flex flex-column align-items-center alignment-items-container">
            <img
              src={getAlignmentImagePath(alignment)}
              alt={t.alignmentTypes[alignment]}
              class="rounded"
              style="width: 170px; height: 110px;"
            />
            <small
              class={`text-center fw-medium ${currentAlignment === alignment ? "active" : ""}`}
            >
              {t.alignmentTypes[alignment]}
            </small>
          </div>
          {#if currentAlignment === alignment}
            <div class="position-absolute top-0 end-0 p-1">
              <i class="bi bi-check-circle-fill text-primary"></i>
            </div>
          {/if}
        </button>
      </div>
    {/each}
  </div>

  <div class="mt-4 d-flex justify-content-between">
    <button class="btn btn-secondary" onclick={handleClose} disabled={isSubmitting}>
      {t.cancel}
    </button>

    {#if currentAlignment}
      <button
        class="btn btn-outline-danger"
        onclick={() => handleAlignmentSelect(currentAlignment)}
        disabled={isSubmitting}
      >
        {isSubmitting ? t.updating : t.clearAlignment}
      </button>
    {/if}
  </div>
</Modal>

<style lang="scss">
  .alignment-button {
    &.active {
      background-color: var(--bs-secondary-bg);
    }
  }

  .alignment-items-container {
    small {
      color: #000 !important;

      // &.active {
      //   color: #fff !important;
      // }
    }
  }
</style>
