"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TagController = void 0;
const common_1 = require("@nestjs/common");
const acrpc_1 = require("../acrpc");
const tag_service_1 = require("./tag.service");
let TagController = class TagController {
    constructor(tagService) {
        this.tagService = tagService;
        const acrpcServer = (0, acrpc_1.getServer)();
        acrpcServer.register({
            tag: {
                list: {
                    get: (input, metadata) => this.tagService.getTags(input, metadata.user),
                },
                post: (input, metadata) => this.tagService.createTag(input, metadata.user),
                patch: (input, metadata) => this.tagService.updateTag(input, metadata.user),
                delete: (input, metadata) => this.tagService.deleteTag(input, metadata.user),
            },
        });
    }
};
exports.TagController = TagController;
exports.TagController = TagController = __decorate([
    (0, common_1.Controller)("tag"),
    __metadata("design:paramtypes", [tag_service_1.TagService])
], TagController);
//# sourceMappingURL=tag.controller.js.map