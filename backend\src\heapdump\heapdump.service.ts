import type { OnModuleInit } from "@nestjs/common";

import heapdump from "heapdump";
import { Cron } from "@nestjs/schedule";
import { Injectable } from "@nestjs/common";
import path from "path";

@Injectable()
export class HeapdumpService implements OnModuleInit {
    onModuleInit() {
        // this.saveHeapdump();
        this.logData();
    }

    // @Cron("0 * * * *")
    saveHeapdump() {
        console.log("Saving heapdump");
        heapdump.writeSnapshot(
            `./.heapdumps/${Date.now()}.heapsnapshot`,
            (err, filename) => {
                if (err) {
                    console.error(err);
                    return;
                }
                console.log(`Heapdump saved to ${filename}`);
            },
        );
    }

    @Cron("*/10 * * * *")
    logData() {
        const memoryUsage = process.memoryUsage();
        const ratio = 1024 * 1024;

        console.log(new Date().toISOString(), {
            rss: memoryUsage.rss / ratio,
            heapTotal: memoryUsage.heapTotal / ratio,
            heapUsed: memoryUsage.heapUsed / ratio,
            external: memoryUsage.external / ratio,
            arrayBuffers: memoryUsage.arrayBuffers / ratio,
        });
    }
}
