import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();

  // Load initial batch of invites
  const invites = await api.user.invite.list.get(
    {
      pagination: {
        page: 1,
        size: Consts.PAGE_SIZE
      }
    },
    { fetch, ctx: { url } }
  );

  return {
    invites,
    isHasMoreInvites: invites.length === Consts.PAGE_SIZE,
  };
};
