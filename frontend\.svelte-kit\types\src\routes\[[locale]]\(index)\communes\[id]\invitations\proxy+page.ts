// @ts-nocheck
import type { Commune, User } from "@commune/api";
import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export type InvitationWithDetails = Commune.GetCommuneInvitationsOutput[number] & {
  user: User.GetUsersOutput[number];
}

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const [
    user,
    [commune],
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
  ]);

  if (!commune) {
    throw error(404, "Commune not found");
  }

  // Check if user has permission to view invitations (admin or head member)
  const isAdmin = user?.role === "admin";
  const isHeadMember = user && commune.headMember.actorType === "user" && commune.headMember.actorId === user.id;
  
  if (!isAdmin && !isHeadMember) {
    throw new Error("Access denied: You must be an admin or commune head to view invitations");
  }

  const invitations = await api.commune.invitation.list.get(
    { communeId: params.id },
    { fetch, ctx: { url } },
  );

  const users = invitations.length
    ? await api.user.list.get(
      { ids: invitations.map(({ userId }) => userId) },
      { fetch, ctx: { url } },
    )
    : [];
  const userMap = new Map(users.map((user) => [user.id, user]));

  const invitationsWithUserDetails = invitations.map<InvitationWithDetails>((invitation) => ({
    ...invitation,
    user: userMap.get(invitation.userId)!,
  }));

  return {
    commune,
    invitations: invitationsWithUserDetails,
    isHasMoreInvitations: invitations.length === Consts.PAGE_SIZE,
    userPermissions: {
      isAdmin,
      isHeadMember,
      canManageInvitations: isAdmin || isHeadMember,
    },
  };
};
