import { Strategy } from "passport-local";
import { Request } from "express";
declare module "express-session" {
    interface SessionData {
        user?: {
            id: string;
            email: string;
            role: string;
        };
    }
}
declare const SessionStrategy_base: new (...args: [] | [options: import("passport-local").IStrategyOptionsWithRequest] | [options: import("passport-local").IStrategyOptions]) => Strategy & {
    validate(...args: any[]): unknown;
};
export declare class SessionStrategy extends SessionStrategy_base {
    constructor();
    validate(req: Request, email: string, otp: string): Promise<any>;
}
export {};
