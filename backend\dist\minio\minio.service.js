"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var MinioService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.MinioService = exports.minioImageEntities = void 0;
const node_path_1 = __importDefault(require("node:path"));
const minio_1 = require("minio");
const common_1 = require("@nestjs/common");
const config_service_1 = require("../config/config.service");
exports.minioImageEntities = [
    "commune",
    "user",
    "reactor-hub",
    "reactor-community",
];
let MinioService = MinioService_1 = class MinioService {
    constructor(configService) {
        this.configService = configService;
        this.logger = new common_1.Logger(MinioService_1.name);
        this.buckets = {
            ...exports.minioImageEntities.reduce((acc, entity) => {
                acc[entity] = entity;
                return acc;
            }, {}),
            "reactor-post": "reactor-post",
        };
    }
    onModuleInit() {
        this.client = new minio_1.Client({
            endPoint: this.configService.config.minio.endpoint,
            port: this.configService.config.minio.port,
            useSSL: this.configService.config.minio.useSSL,
            accessKey: this.configService.config.minio.accessKey,
            secretKey: this.configService.config.minio.secretKey,
        });
        this.initializeBuckets();
    }
    async initializeBuckets() {
        await Promise.all(Object.values(this.buckets).map(async (bucket) => {
            const exists = await this.client.bucketExists(bucket);
            if (!exists) {
                await this.client.makeBucket(bucket, "us-east-1");
                this.logger.log(`Bucket '${bucket}' created successfully`);
                const policy = {
                    Version: "2012-10-17",
                    Statement: [
                        {
                            Effect: "Allow",
                            Principal: { AWS: ["*"] },
                            Action: ["s3:GetObject"],
                            Resource: [`arn:aws:s3:::${bucket}/*`],
                        },
                    ],
                };
                await this.client.setBucketPolicy(bucket, JSON.stringify(policy));
                this.logger.log(`Public read policy set for bucket '${bucket}'`);
            }
        })).catch((error) => {
            this.logger.error("Failed to initialize MinIO buckets", error);
        });
    }
    toVersionedUrl(url) {
        return `${url}?v=${Date.now()}`;
    }
    getImageObjectName(entityId, index) {
        return `${entityId}${index !== undefined ? `.${index}` : ""}`;
    }
    async uploadImage(file, entity, entityId, index) {
        const bucket = this.buckets[entity];
        const objectName = this.getImageObjectName(entityId, index);
        if (!bucket) {
            throw new Error(`There's no bucket for entity: ${entity}`);
        }
        const url = await this.uploadFile(file, bucket, objectName).catch((error) => {
            this.logger.error(`Failed to upload post image ${objectName} to bucket ${bucket}`, error);
            throw error;
        });
        return this.toVersionedUrl(url);
    }
    async uploadPostImage(id, file) {
        const bucket = this.buckets["reactor-post"];
        const objectName = `${id}${node_path_1.default.extname(file.originalname)}`;
        const url = await this.uploadFile(file, bucket, objectName);
        return this.toVersionedUrl(url);
    }
    async uploadFile(file, bucket, objectName) {
        try {
            await this.client.putObject(bucket, objectName, file.buffer, undefined, {
                "Content-Type": file.mimetype,
            });
            return `${bucket}/${objectName}`;
        }
        catch (error) {
            this.logger.error(`Failed to upload file ${objectName} to bucket ${bucket}`, error);
            throw error;
        }
    }
    async deleteImage(entity, entityId, index) {
        const bucket = this.buckets[entity];
        const objectName = this.getImageObjectName(entityId, index);
        return await this.deleteFile(bucket, objectName);
    }
    async deleteFile(bucket, objectName) {
        try {
            await this.client.removeObject(bucket, objectName);
        }
        catch (error) {
            throw new Error(`Failed to delete file ${objectName} from bucket ${bucket}`, { cause: error });
        }
    }
};
exports.MinioService = MinioService;
exports.MinioService = MinioService = MinioService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_service_1.ConfigService])
], MinioService);
//# sourceMappingURL=minio.service.js.map