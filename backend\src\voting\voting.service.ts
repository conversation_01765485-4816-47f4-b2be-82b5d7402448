import { Injectable } from "@nestjs/common";
import { PrismaService } from "src/prisma/prisma.service";
import { Common } from "@commune/api";

type CreateDto = {
    votesRequired: number;
    endsAt: Date;
    title: Common.Localization[];
    description: Common.Localization[];
    options: {
        title: Common.Localization[];
    }[];
};

@Injectable()
export class VotingService {
    constructor(private readonly prisma: PrismaService) {}

    async create(data: CreateDto) {
        return await this.prisma.voting.create({
            data: {
                votesRequired: data.votesRequired,
                endsAt: data.endsAt,

                title: {
                    create: data.title.map((item) => ({
                        ...item,
                        key: "title",
                    })),
                },

                description: {
                    create: data.description.map((item) => ({
                        ...item,
                        key: "description",
                    })),
                },

                options: {
                    create: data.options.map((option) => ({
                        title: {
                            create: option.title,
                        },
                    })),
                },
            },

            include: {
                title: true,
                description: true,

                options: {
                    include: {
                        title: true,
                    },
                },
            },
        });
    }
}
