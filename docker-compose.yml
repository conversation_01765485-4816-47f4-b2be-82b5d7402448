version: '3.8'

name: commune

services:
  postgres:
    image: postgres:17.4-alpine3.21
    container_name: postgres
    environment:
      POSTGRES_DB: commune
      POSTGRES_USER: commune
      POSTGRES_PASSWORD: commune
    ports:
      - "5432:5432"
    volumes:
      # - ./.docker/postgres/data:/var/lib/postgresql/data
      - postgres-data:/var/lib/postgresql/data
    networks:
      - commune
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U commune -d commune"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: password
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
        - ./.docker/pgadmin/servers.json:/pgadmin4/servers.json
    networks:
      - commune
    restart: unless-stopped

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z-cpuv1
    container_name: minio
    restart: unless-stopped
    ports:
      - "9000:9000"  # API port
      - "9001:9001"  # Console port
    environment:
      MINIO_ROOT_USER: admin
      MINIO_ROOT_PASSWORD: password
    volumes:
      - ./.docker/minio/data:/usr/commune/minio/data
    command: server /data --console-address ":9001"
    networks:
      - commune
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

  # backend:
  #   build:
  #     context: .
  #     dockerfile: ./backend/Dockerfile
  #   container_name: backend
  #   ports:
  #     - "4000:4000"
  #   env_file:
  #     - ./backend/.env
  #     - ./backend/.env.docker
  #   depends_on:
  #     postgres:
  #       condition: service_healthy
  #     minio:
  #       condition: service_healthy
  #   volumes:
  #     - backend-sessions:/app/.sessions
  #   networks:
  #     - commune
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:4000"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s

  # frontend:
  #   build:
  #     context: .
  #     dockerfile: ./frontend/Dockerfile
  #   container_name: frontend
  #   ports:
  #     - "3000:3000"
  #   env_file:
  #     - ./frontend/.env
  #     - ./frontend/.env.docker
  #   depends_on:
  #     backend:
  #       condition: service_healthy
  #   networks:
  #     - commune
  #   restart: unless-stopped
  #   healthcheck:
  #     test: ["CMD", "curl", "-f", "http://localhost:3000"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 40s

volumes:
  postgres-data:

networks:
  commune:
    driver: bridge
