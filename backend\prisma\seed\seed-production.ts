import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

async function main() {
    await prisma.$transaction(async (trx) => {
        await trx.user.create({
            data: {
                email: "<EMAIL>",
                role: "admin",
                name: {
                    create: [
                        {
                            locale: "en",
                            value: "Admin",
                            key: "name",
                        },
                        {
                            locale: "ru",
                            value: "Админ",
                            key: "name",
                        },
                    ],
                },
            }
        })
    });
}

main()
    .then(async () => {
        await prisma.$disconnect();
    })
    .catch(async (e) => {
        console.error(e);
        await prisma.$disconnect();
        process.exit(1);
    });
