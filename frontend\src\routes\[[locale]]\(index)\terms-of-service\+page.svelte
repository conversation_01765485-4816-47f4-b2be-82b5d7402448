<script lang="ts">
  const i18n = {
    en: {
      _page: {
        title: "Terms of Service — Commune",
      },
      title: "Digital Society Commune — Terms of Service",
      lastUpdated: "Last updated: August 23, 2025",
      contact: "Contact for legal notices: <EMAIL>",

      summary: {
        title: "Plain-language summary (quick highlights)",
        points: [
          "Commune is a platform for people to communicate, cooperate, form groups, and create content.",
          "You must register with an email (OTP/passwordless). You may add a name/description. Bots / API clients are allowed; multiple personal accounts are not.",
          "You own your content — by posting you give Commune a non-exclusive right to host, show, copy and adapt it with attribution or a link to the source on Commune.",
          "We moderate manually and block/remove illegal, extremist, spammy or harmful content.",
          'Service is provided "as-is." Our liability is limited (see §10).',
          'Disputes: we\'ll first try to resolve them under Commune\'s own "The Law" and "Rules" (displayed on the site). If unresolved, Dutch law (Amsterdam courts) applies as a fallback.',
          "You accept these Terms by checking the box when you create an account or use the site.",
        ],
        disclaimer:
          "Not legal advice. This document is a practical, concise Terms of Service. For binding legal review in a given jurisdiction please consult a qualified attorney.",
      },

      whoWeAre: {
        title: "1. Who we are & what the service is",
        description:
          'Commune (a.k.a. "Digital Society Commune", "DS Commune", "Commune") operates the website and services that let people communicate, cooperate, publish posts (text, images), create comments, communes, hubs and communities, and use tools for conflict resolution using Commune\'s internal rules system (the "Service").',
      },

      agreement: {
        title: "2. Agreement / acceptance",
        description:
          'By creating an account, checking the "I accept" box, or using the Service you accept these Terms. If you don\'t agree, do not use the Service.',
      },

      lawAndRules: {
        title: "3. The Law and Rules; Governing law",
        primaryProcess:
          'Primary process. You agree that when a dispute arises you and Commune will first attempt to resolve it using Commune\'s internal system, "The Law" and "Rules", as published on the site (the "Internal Rules"). You agree to participate in good faith in any reasonable internal procedures Commune publishes.',
        fallback:
          "Fallback. If Internal Rules do not resolve the dispute, these Terms are governed by the laws of the Netherlands and courts in Amsterdam will have jurisdiction (unless mandatory local law requires otherwise).",
        enforceability:
          "If any part of this clause is unenforceable in a particular jurisdiction, the remaining parts remain in force.",
      },

      accounts: {
        title: "4. Accounts & access",
        points: [
          "Registration requires a valid email address. Commune uses passwordless sign-in (OTP) sent to that email. You may add a display name or free-form description.",
          "Multiple personal accounts and aliases are not permitted. Bot accounts or API clients are allowed per our published API rules.",
          "You are responsible for activity on your account. We may suspend or terminate access for breach of these Terms, fraud, or if required by law.",
        ],
      },

      userContent: {
        title: "5. User content & license",
        points: [
          "You retain ownership of content you post. By posting you grant Commune a non-exclusive, worldwide, royalty-free right to host, reproduce, display, distribute, cache, adapt and otherwise use the content to provide and promote the Service. When others copy content outside Commune they must attribute or link to the original source on Commune (credit requirement).",
          "You represent that you have the rights necessary to post the content and that it does not violate laws or third-party rights.",
          "We may remove or restrict access to content that violates these Terms, Internal Rules, or applicable laws.",
        ],
      },

      prohibited: {
        title: "6. Prohibited content & moderation",
        points: [
          "Prohibited: illegal material, extremist content, materials facilitating criminal activity, spam, flooding, threats, and content that reasonably creates an immediate risk of serious harm.",
          "Commune performs manual moderation. We do not publish moderation logs; Commune may retain minimal internal records where required for compliance or to defend legal claims.",
          "If you believe your content was removed in error, follow the site's appeal process (outlined in Internal Rules).",
        ],
      },

      payments: {
        title: "7. Payments, subscriptions & refunds",
        points: [
          "Right now Commune is free. In future we may introduce paid subscriptions, one-time charges, donations, cryptopayments, and an internal user balance system.",
          "Where payments occur, payment processor terms apply. You remain responsible for taxes where applicable.",
          "Refunds, trials and the exact billing rules will be governed by a separate Refund & Billing Policy published on the site. Generally, Commune aims to offer reasonable refund/trial handling; contact <NAME_EMAIL> for cases and disputes.",
        ],
      },

      privacy: {
        title: "8. Privacy & data deletion",
        points: [
          "Commune has a separate Privacy Policy (referenced on the site). Servers are located in the Netherlands; EU and other data protections will be respected. Commune will attempt to comply with EU, Russian and (where applicable) U.S. data protection obligations.",
          "You may request deletion of your personal data by contacting us; we will honor deletion requests except where we must retain data for legal compliance or legitimate business needs, as explained in the Privacy Policy.",
        ],
      },

      security: {
        title: "9. Security & availability",
        description:
          "Commune uses HTTPS for secure transport. We do not promise a specific uptime. Commune may suspend the Service for maintenance, emergencies, or legal reasons. Commune will use reasonable measures to protect your data, but you acknowledge no system is perfectly secure.",
      },

      disclaimers: {
        title: "10. Disclaimers & limitation of liability",
        points: [
          'The Service is provided "as is" and "as available". Commune disclaims all warranties to the fullest extent permitted by law.',
          "To the maximum extent permitted by law, Commune's total liability for damages arising from or related to these Terms will not exceed the amount you paid Commune in the twelve (12) months before the claim (or, if you paid nothing, a modest nominal amount). We are not liable for indirect, incidental, special, exemplary or consequential damages, including lost profits, lost data, or business interruption.",
          "Nothing in these Terms excludes liability for fraud, willful misconduct, or other liability that cannot be limited under applicable law.",
        ],
      },

      indemnity: {
        title: "11. Indemnity",
        description:
          "You agree to indemnify, defend, and hold Commune and its officers, employees and agents harmless from claims, damages, losses, liabilities and costs (including reasonable legal fees) arising from your use of the Service, your content, or your violation of these Terms.",
      },

      termination: {
        title: "12. Termination",
        description:
          "Either party may terminate access to the Service for breach of these Terms. On termination, Commune may delete or anonymize content and may withhold refunds where permitted. Commune will follow its published data-retention and deletion procedures.",
      },

      notices: {
        title: "13. Notices, changes to terms",
        points: [
          "We will send legal notices to the email you provide. You will be notified of material changes by email and/or prominent site notice. Changes take effect when posted unless otherwise stated. Continued use after notice means acceptance.",
          "Commune may suspend or terminate the Service without prior notice in emergencies or where required by law.",
        ],
      },

      thirdParty: {
        title: "14. Third-party services",
        description:
          "The Service may contain links to or integrate third-party services (payment processors, analytics, social login). Commune is not responsible for third-party terms or content. Use of third-party services is at your own risk.",
      },

      language: {
        title: "15. Language & translations",
        description:
          "These Terms are available in English and Russian. If there is a conflict, the English version will govern unless local law requires otherwise.",
      },

      acceptance: {
        title: "16. Acceptance method",
        description:
          "You accept these Terms by checking the acceptance box on registration or continuing to use the Service. This is a binding agreement.",
      },

      contactSection: {
        title: "17. Contact",
        description: "For questions, requests, or legal notices: <EMAIL>",
      },
    },

    ru: {
      _page: {
        title: "Условия использования — Коммуна",
      },
      title: "Цифровое сообщество Коммуна — Условия использования",
      lastUpdated: "Последнее обновление: 23 августа 2025 г.",
      contact: "Контакт для юридических уведомлений: <EMAIL>",

      summary: {
        title: "Краткое изложение простым языком (основные моменты)",
        points: [
          "Коммуна — это платформа для общения, сотрудничества, создания групп и контента.",
          "Вы должны зарегистрироваться с email (OTP/без пароля). Вы можете добавить имя/описание. Боты / API клиенты разрешены; множественные личные аккаунты не разрешены.",
          "Вы владеете своим контентом — публикуя, вы даете Коммуне неисключительное право размещать, показывать, копировать и адаптировать его с указанием авторства или ссылкой на источник в Коммуне.",
          "Мы модерируем вручную и блокируем/удаляем незаконный, экстремистский, спамовый или вредоносный контент.",
          'Сервис предоставляется "как есть". Наша ответственность ограничена (см. §10).',
          'Споры: мы сначала попытаемся разрешить их в соответствии с собственными "Законом" и "Правилами" Коммуны (отображаются на сайте). Если не разрешено, применяется голландское право (суды Амстердама) в качестве запасного варианта.',
          "Вы принимаете эти Условия, отмечая флажок при создании аккаунта или использовании сайта.",
        ],
        disclaimer:
          "Не является юридической консультацией. Этот документ представляет собой практические, краткие Условия использования. Для обязательного правового анализа в конкретной юрисдикции обратитесь к квалифицированному юристу.",
      },

      whoWeAre: {
        title: "1. Кто мы и что представляет собой сервис",
        description:
          'Коммуна (также "Цифровое сообщество Коммуна", "ЦС Коммуна", "Коммуна") управляет веб-сайтом и сервисами, которые позволяют людям общаться, сотрудничать, публиковать посты (текст, изображения), создавать комментарии, коммуны, хабы и сообщества, а также использовать инструменты для разрешения конфликтов с помощью внутренней системы правил Коммуны ("Сервис").',
      },

      agreement: {
        title: "2. Соглашение / принятие",
        description:
          'Создавая аккаунт, отмечая флажок "Я принимаю" или используя Сервис, вы принимаете эти Условия. Если вы не согласны, не используйте Сервис.',
      },

      lawAndRules: {
        title: "3. Закон и Правила; Применимое право",
        primaryProcess:
          'Основной процесс. Вы соглашаетесь, что при возникновении спора вы и Коммуна сначала попытаетесь разрешить его с помощью внутренней системы Коммуны, "Закона" и "Правил", опубликованных на сайте ("Внутренние правила"). Вы соглашаетесь добросовестно участвовать в любых разумных внутренних процедурах, которые публикует Коммуна.',
        fallback:
          "Запасной вариант. Если Внутренние правила не разрешают спор, эти Условия регулируются законами Нидерландов, и суды Амстердама будут иметь юрисдикцию (если обязательное местное право не требует иного).",
        enforceability:
          "Если какая-либо часть этого пункта не подлежит исполнению в конкретной юрисдикции, остальные части остаются в силе.",
      },

      accounts: {
        title: "4. Аккаунты и доступ",
        points: [
          "Регистрация требует действительного адреса электронной почты. Коммуна использует вход без пароля (OTP), отправляемый на этот email. Вы можете добавить отображаемое имя или описание в свободной форме.",
          "Множественные личные аккаунты и псевдонимы не разрешены. Боты или API клиенты разрешены согласно нашим опубликованным правилам API.",
          "Вы несете ответственность за активность в своем аккаунте. Мы можем приостановить или прекратить доступ за нарушение этих Условий, мошенничество или если это требуется по закону.",
        ],
      },

      userContent: {
        title: "5. Пользовательский контент и лицензия",
        points: [
          "Вы сохраняете право собственности на контент, который публикуете. Публикуя, вы предоставляете Коммуне неисключительное, всемирное, безвозмездное право размещать, воспроизводить, отображать, распространять, кэшировать, адаптировать и иным образом использовать контент для предоставления и продвижения Сервиса. Когда другие копируют контент за пределами Коммуны, они должны указывать авторство или ссылаться на оригинальный источник в Коммуне (требование указания авторства).",
          "Вы заявляете, что у вас есть необходимые права для публикации контента и что он не нарушает законы или права третьих лиц.",
          "Мы можем удалить или ограничить доступ к контенту, который нарушает эти Условия, Внутренние правила или применимые законы.",
        ],
      },

      prohibited: {
        title: "6. Запрещенный контент и модерация",
        points: [
          "Запрещено: незаконные материалы, экстремистский контент, материалы, способствующие преступной деятельности, спам, флуд, угрозы и контент, который разумно создает непосредственный риск серьезного вреда.",
          "Коммуна выполняет ручную модерацию. Мы не публикуем журналы модерации; Коммуна может вести минимальные внутренние записи, где это требуется для соблюдения требований или защиты правовых претензий.",
          "Если вы считаете, что ваш контент был удален по ошибке, следуйте процедуре апелляции сайта (изложенной во Внутренних правилах).",
        ],
      },

      payments: {
        title: "7. Платежи, подписки и возвраты",
        points: [
          "Сейчас Коммуна бесплатна. В будущем мы можем ввести платные подписки, разовые платежи, пожертвования, криптоплатежи и внутреннюю систему баланса пользователей.",
          "При осуществлении платежей применяются условия платежного процессора. Вы остаетесь ответственными за налоги, где это применимо.",
          "Возвраты, пробные периоды и точные правила биллинга будут регулироваться отдельной Политикой возвратов и биллинга, опубликованной на сайте. В целом, Коммуна стремится предложить разумное обращение с возвратами/пробными периодами; обращайтесь в поддержку по адресу <EMAIL> для случаев и споров.",
        ],
      },

      privacy: {
        title: "8. Конфиденциальность и удаление данных",
        points: [
          "У Коммуны есть отдельная Политика конфиденциальности (указанная на сайте). Серверы расположены в Нидерландах; будут соблюдаться защита данных ЕС и другие. Коммуна будет пытаться соблюдать обязательства по защите данных ЕС, России и (где применимо) США.",
          "Вы можете запросить удаление ваших персональных данных, связавшись с нами; мы выполним запросы на удаление, за исключением случаев, когда мы должны сохранить данные для соблюдения законодательства или законных деловых потребностей, как объяснено в Политике конфиденциальности.",
        ],
      },

      security: {
        title: "9. Безопасность и доступность",
        description:
          "Коммуна использует HTTPS для безопасной передачи данных. Мы не обещаем конкретное время работы. Коммуна может приостановить Сервис для обслуживания, чрезвычайных ситуаций или по правовым причинам. Коммуна будет использовать разумные меры для защиты ваших данных, но вы признаете, что ни одна система не является абсолютно безопасной.",
      },

      disclaimers: {
        title: "10. Отказ от ответственности и ограничение ответственности",
        points: [
          'Сервис предоставляется "как есть" и "как доступно". Коммуна отказывается от всех гарантий в максимальной степени, разрешенной законом.',
          "В максимальной степени, разрешенной законом, общая ответственность Коммуны за ущерб, возникающий из или связанный с этими Условиями, не будет превышать сумму, которую вы заплатили Коммуне в течение двенадцати (12) месяцев до претензии (или, если вы ничего не платили, скромную номинальную сумму). Мы не несем ответственности за косвенный, случайный, особый, примерный или последующий ущерб, включая упущенную прибыль, потерянные данные или прерывание бизнеса.",
          "Ничто в этих Условиях не исключает ответственность за мошенничество, умышленные неправомерные действия или другую ответственность, которая не может быть ограничена в соответствии с применимым законом.",
        ],
      },

      indemnity: {
        title: "11. Возмещение ущерба",
        description:
          "Вы соглашаетесь возместить, защитить и оградить Коммуну и ее должностных лиц, сотрудников и агентов от претензий, ущерба, потерь, обязательств и расходов (включая разумные юридические сборы), возникающих из вашего использования Сервиса, вашего контента или вашего нарушения этих Условий.",
      },

      termination: {
        title: "12. Прекращение",
        description:
          "Любая сторона может прекратить доступ к Сервису за нарушение этих Условий. При прекращении Коммуна может удалить или анонимизировать контент и может удержать возвраты, где это разрешено. Коммуна будет следовать своим опубликованным процедурам хранения и удаления данных.",
      },

      notices: {
        title: "13. Уведомления, изменения в условиях",
        points: [
          "Мы будем отправлять юридические уведомления на предоставленный вами email. Вы будете уведомлены о существенных изменениях по email и/или заметным уведомлением на сайте. Изменения вступают в силу при публикации, если не указано иное. Продолжение использования после уведомления означает принятие.",
          "Коммуна может приостановить или прекратить Сервис без предварительного уведомления в чрезвычайных ситуациях или когда это требуется по закону.",
        ],
      },

      thirdParty: {
        title: "14. Сторонние сервисы",
        description:
          "Сервис может содержать ссылки на сторонние сервисы или интегрироваться с ними (платежные процессоры, аналитика, социальный вход). Коммуна не несет ответственности за условия или контент третьих сторон. Использование сторонних сервисов осуществляется на ваш собственный риск.",
      },

      language: {
        title: "15. Язык и переводы",
        description:
          "Эти Условия доступны на английском и русском языках. В случае конфликта английская версия будет иметь приоритет, если местное право не требует иного.",
      },

      acceptance: {
        title: "16. Способ принятия",
        description:
          "Вы принимаете эти Условия, отмечая флажок принятия при регистрации или продолжая использовать Сервис. Это обязывающее соглашение.",
      },

      contactSection: {
        title: "17. Контакты",
        description: "По вопросам, запросам или юридическим уведомлениям: <EMAIL>",
      },
    },
  };

  const { data } = $props();
  const { locale } = $derived(data);

  const t = $derived(i18n[locale]);
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-5">
  <div class="responsive-container">
    <h1 class="mb-4">{t.title}</h1>
    <p class="text-muted mb-2"><strong>{t.lastUpdated}</strong></p>
    <p class="text-muted mb-4">{t.contact}</p>

    <!-- Summary Section -->
    <section class="mb-5">
      <h2>{t.summary.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.summary.points as point}
              <li>{point}</li>
            {/each}
          </ul>
          <blockquote class="blockquote mt-3">
            <p class="mb-0 text-muted"><small>{t.summary.disclaimer}</small></p>
          </blockquote>
        </div>
      </div>
    </section>

    <!-- Who We Are Section -->
    <section class="mb-5">
      <h2>{t.whoWeAre.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.whoWeAre.description}</p>
        </div>
      </div>
    </section>

    <!-- Agreement Section -->
    <section class="mb-5">
      <h2>{t.agreement.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.agreement.description}</p>
        </div>
      </div>
    </section>

    <!-- Law and Rules Section -->
    <section class="mb-5">
      <h2>{t.lawAndRules.title}</h2>
      <div class="card">
        <div class="card-body">
          <ol>
            <li><strong>Primary process.</strong> {t.lawAndRules.primaryProcess}</li>
            <li><strong>Fallback.</strong> {t.lawAndRules.fallback}</li>
            <li>{t.lawAndRules.enforceability}</li>
          </ol>
        </div>
      </div>
    </section>

    <!-- Accounts Section -->
    <section class="mb-5">
      <h2>{t.accounts.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.accounts.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- User Content Section -->
    <section class="mb-5">
      <h2>{t.userContent.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.userContent.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Prohibited Content Section -->
    <section class="mb-5">
      <h2>{t.prohibited.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.prohibited.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Payments Section -->
    <section class="mb-5">
      <h2>{t.payments.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.payments.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Privacy Section -->
    <section class="mb-5">
      <h2>{t.privacy.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.privacy.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Security Section -->
    <section class="mb-5">
      <h2>{t.security.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.security.description}</p>
        </div>
      </div>
    </section>

    <!-- Disclaimers Section -->
    <section class="mb-5">
      <h2>{t.disclaimers.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.disclaimers.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Indemnity Section -->
    <section class="mb-5">
      <h2>{t.indemnity.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.indemnity.description}</p>
        </div>
      </div>
    </section>

    <!-- Termination Section -->
    <section class="mb-5">
      <h2>{t.termination.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.termination.description}</p>
        </div>
      </div>
    </section>

    <!-- Notices Section -->
    <section class="mb-5">
      <h2>{t.notices.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.notices.points as point}
              <li>{point}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Third Party Section -->
    <section class="mb-5">
      <h2>{t.thirdParty.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.thirdParty.description}</p>
        </div>
      </div>
    </section>

    <!-- Language Section -->
    <section class="mb-5">
      <h2>{t.language.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.language.description}</p>
        </div>
      </div>
    </section>

    <!-- Acceptance Section -->
    <section class="mb-5">
      <h2>{t.acceptance.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.acceptance.description}</p>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="mb-5">
      <h2>{t.contactSection.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.contactSection.description}</p>
        </div>
      </div>
    </section>
  </div>
</div>
