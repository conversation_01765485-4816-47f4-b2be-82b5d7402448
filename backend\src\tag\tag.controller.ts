import { Controller } from "@nestjs/common";
import { getServer } from "src/acrpc";
import { TagService } from "./tag.service";

@Controller("tag")
export class TagController {
    constructor(private readonly tagService: TagService) {
        const acrpcServer = getServer();

        acrpcServer.register({
            tag: {
                list: {
                    get: (input, metadata) =>
                        this.tagService.getTags(input, metadata.user),
                },
                post: (input, metadata) =>
                    this.tagService.createTag(input, metadata.user),
                patch: (input, metadata) =>
                    this.tagService.updateTag(input, metadata.user),
                delete: (input, metadata) =>
                    this.tagService.deleteTag(input, metadata.user),
            },
        });
    }
}
