import type { Infer } from "./types";

import { z } from "zod";
import {
    id,
    searchIds,
    searchQuery,
    deletedAt,
    LocalizationsSchema,
    PaginationSchema,
} from "./common";

export const tagName = LocalizationsSchema.min(1);

export type GetTagsInput = Infer<typeof GetTagsInputSchema>;
export const GetTagsInputSchema = z
    .object({
        pagination: PaginationSchema,
        ids: searchIds,
        query: searchQuery,
    })
    .partial();

export type GetTagsOutput = Infer<typeof GetTagsOutputSchema>;
export const GetTagsOutputSchema = z.array(
    z.object({
        id,
        name: tagName,
        deletedAt: deletedAt.optional(),
    }),
);

export type CreateTagInput = Infer<typeof CreateTagInputSchema>;
export const CreateTagInputSchema = z.object({
    name: tagName,
});

export type UpdateTagInput = Infer<typeof UpdateTagInputSchema>;
export const UpdateTagInputSchema = z.object({
    id,
    name: tagName,
});
