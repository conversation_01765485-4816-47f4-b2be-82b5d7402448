import { Injectable } from "@nestjs/common";
import { VoteActorType } from "@prisma/client";
import { PrismaService } from "src/prisma/prisma.service";

type CreateDto = {
    userId: string;
    votingId: string;
    optionId: string;
};

@Injectable()
export class VoteService {
    constructor(private readonly prisma: PrismaService) {}

    async create(data: CreateDto) {
        await this.prisma.$transaction(async (trx) => {
            await trx.vote.deleteMany({
                where: {
                    actorType: VoteActorType.user,
                    actorId: data.userId,
                    votingId: data.votingId,
                },
            });

            await trx.vote.create({
                data: {
                    actorType: VoteActorType.user,
                    actorId: data.userId,
                    votingId: data.votingId,
                    optionId: data.optionId,
                },
            });
        });
    }
}
