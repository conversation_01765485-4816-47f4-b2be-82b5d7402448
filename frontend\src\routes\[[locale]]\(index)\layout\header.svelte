<script lang="ts">
  import type { Common } from "@commune/api";
  import type { LocalStorageUser } from "$lib";

  import { onMount } from "svelte";
  import { LocaleSwitcher } from "$lib/components";

  interface Props {
    user: LocalStorageUser | null;
    locale: Common.WebsiteLocale;
    routeLocale: Common.WebsiteLocale | null;
    toLocaleHref: (href: string) => string;
  }

  const i18n = {
    ru: {
      navGap: "mx-auto",
      theLaw: "Право",
      rules: "Правила",
      newEnglish: "Новый английский",
      newCalendar: "Новый календарь",
      reactor: "Реактор",
      users: "Люди",
      communes: "Коммуны",
      profile: "Профиль",
    },

    en: {
      navGap: "mx-auto",
      theLaw: "The Law",
      rules: "Rules",
      newEnglish: "New English",
      newCalendar: "New Calendar",
      reactor: "Reactor",
      users: "People",
      communes: "Communes",
      profile: "Profile",
    },
  };

  const { user, locale, routeLocale, toLocaleHref }: Props = $props();

  const t = $derived(i18n[locale]);

  // Function to close the navbar collapse
  function closeNavbar() {
    const navbarCollapse = document.getElementById("navbarNav");
    const navbarToggler = document.querySelector(".navbar-toggler") as HTMLElement;

    if (navbarCollapse && navbarToggler) {
      // Check if the navbar is currently expanded
      const isExpanded = navbarCollapse.classList.contains("show");

      if (isExpanded) {
        // Use Bootstrap's collapse method to hide the navbar
        const bootstrap = (window as any).bootstrap;
        if (bootstrap && bootstrap.Collapse) {
          const bsCollapse =
            bootstrap.Collapse.getInstance(navbarCollapse) ||
            new bootstrap.Collapse(navbarCollapse, { toggle: false });
          bsCollapse.hide();
        }
      }
    }
  }

  // Setup event listeners for nav links
  onMount(() => {
    const navLinks = document.querySelectorAll(".compact-navbar .nav-link, .compact-navbar .btn");

    navLinks.forEach((link) => {
      link.addEventListener("click", closeNavbar);
    });

    // Cleanup event listeners
    return () => {
      navLinks.forEach((link) => {
        link.removeEventListener("click", closeNavbar);
      });
    };
  });
</script>

<nav class="navbar navbar-expand-lg sticky-top compact-navbar">
  <div class="container">
    <a href={toLocaleHref("/")} class="navbar-brand py-0 ps-5">
      <img
        src="/images/full-v3-transparent.svg"
        alt="Site Logo"
        height={60}
        width={60}
        style:width="auto"
      />
    </a>
    <button
      class="navbar-toggler"
      type="button"
      data-bs-toggle="collapse"
      data-bs-target="#navbarNav"
      aria-controls="navbarNav"
      aria-expanded="false"
      aria-label="Toggle navigation"
    >
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav mx-auto">
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/the-law")} class="nav-link">{t.theLaw}</a>
        </li>
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/rules")} class="nav-link">{t.rules}</a>
        </li>
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/new-english")} class="nav-link">{t.newEnglish}</a>
        </li>
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/new-calendar")} class="nav-link">{t.newCalendar}</a>
        </li>
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/people")} class="nav-link">{t.users}</a>
        </li>
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/communes")} class="nav-link">{t.communes}</a>
        </li>
        <li class={`nav-item ${t.navGap} text-nowrap`}>
          <a href={toLocaleHref("/reactor")} class="nav-link">{t.reactor}</a>
        </li>
      </ul>
      <ul class="navbar-nav d-flex align-items-center" style:position="relative">
        <li class="nav-item me-2">
          <a href={toLocaleHref("/profile")} class="btn btn-primary btn-sm">
            {t.profile}
          </a>
        </li>
        <li class="nav-item">
          <LocaleSwitcher currentLocale={routeLocale} />
        </li>
        {#if user?.role === "admin"}
          <li class="nav-item" style:position="absolute" style:right="-105px">
            <a href={toLocaleHref("/admin")} class="btn btn-primary btn-sm">Админка</a>
          </li>
        {/if}
      </ul>
    </div>
  </div>
</nav>

<style>
  .navbar {
    padding: 0;
  }

  .compact-navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    z-index: 1030;
    backdrop-filter: blur(5px);
    background-color: rgba(248, 249, 250, 0.95) !important;
  }

  .compact-navbar :global(.nav-link) {
    font-size: 1.05rem; /* Increased font size */
    padding: 0.7rem 1rem !important; /* Increased padding for larger clickable area */
    transition: color 0.2s ease;
    color: #333333; /* Darker color for better contrast */
    font-weight: 500; /* Slightly bolder for better visibility */
    display: block; /* Ensures the entire area is clickable */
  }

  .compact-navbar :global(.nav-link:hover) {
    color: #0d6efd;
  }

  .compact-navbar :global(.navbar-brand) {
    display: flex;
    align-items: center;
  }

  .compact-navbar :global(.navbar-collapse) {
    justify-content: space-between;
  }

  .compact-navbar :global(.navbar-nav.mx-auto) {
    display: flex;
    justify-content: center;
  }

  .compact-navbar :global(.nav-item) {
    display: flex;
    align-items: center;
  }

  .compact-navbar :global(.btn-sm) {
    font-size: 0.95rem;
    padding: 0.5rem 1.1rem;
    font-weight: 500;
    display: inline-block;
  }

  @media (max-width: 991.98px) {
    .compact-navbar :global(.navbar-collapse) {
      padding: 0.5rem 0;
      flex-direction: column;
    }

    .compact-navbar :global(.navbar-nav) {
      text-align: center;
      margin-bottom: 0.5rem;
    }

    .compact-navbar :global(.nav-item) {
      margin: 0.2rem 0 !important;
    }

    .compact-navbar :global(.navbar-nav.mx-auto) {
      margin-left: 0 !important;
      margin-right: 0 !important;
    }

    /* Align profile and language selector on mobile */
    .compact-navbar :global(.navbar-nav:last-child) {
      flex-direction: row !important;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;
    }

    .compact-navbar :global(.navbar-nav:last-child .nav-item) {
      margin: 0 !important;
    }
  }
</style>
