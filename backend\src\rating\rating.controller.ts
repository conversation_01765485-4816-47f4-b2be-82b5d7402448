import { Controller } from "@nestjs/common";
import { getServer } from "src/acrpc";
import { RatingService } from "./rating.service";
import { RatingKarmaService } from "./rating-karma.service";

@Controller("rating")
export class RatingController {
    constructor(
        private readonly ratingService: RatingService,
        private readonly ratingKarmaService: RatingKarmaService,
    ) {
        const acrpcServer = getServer();

        acrpcServer.register({
            rating: {
                karma: {
                    list: {
                        get: async (input) => {
                            const points =
                                await this.ratingKarmaService.getKarmaPoints(
                                    input,
                                );

                            return points.map((p) => ({
                                id: p.id,
                                author: {
                                    ...p.sourceUser,
                                    image: p.sourceUser.image?.url ?? null,
                                },
                                quantity: p.quantity,
                                comment: p.comment,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.ratingKarmaService.spendKarmaPoint(
                            input,
                            metadata.user,
                        ),
                },
                feedback: {
                    list: {
                        get: async (input) => {
                            const feedbacks =
                                await this.ratingService.getUserFeedbacks(
                                    input,
                                );

                            return feedbacks.map((f) => ({
                                id: f.id,
                                author: f.isAnonymous
                                    ? null
                                    : {
                                          ...f.sourceUser,
                                          image:
                                              f.sourceUser.image?.url ?? null,
                                      },
                                isAnonymous: f.isAnonymous,
                                value: f.value,
                                text: f.text,
                            }));
                        },
                    },
                    post: (input, metadata) =>
                        this.ratingService.createUserFeedback(
                            input,
                            metadata.user,
                        ),
                },
                summary: {
                    get: (input) => this.ratingService.getUserSummary(input),
                },
            },
        });
    }
}
