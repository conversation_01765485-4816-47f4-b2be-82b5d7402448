import {
    Controller,
    Get,
    InternalServerErrorException,
    Logger,
    Query,
} from "@nestjs/common";
import { SitemapService } from "./sitemap.service";
import { z, ZodPipe } from "src/zod";
import { Sitemap } from "@commune/api";

@Controller("sitemap")
export class SitemapController {
    private readonly logger = new Logger(SitemapController.name);

    constructor(private readonly sitemapService: SitemapService) {}

    @Get("generation-data")
    async getSitemapGenerationData(
        @Query("key", new ZodPipe(z.string().nonempty())) key: string,
    ) {
        const data = await this.sitemapService.getSitemapGenerationData(key);

        const parsedData =
            Sitemap.GetSitemapGenerationDataOutputSchema.safeParse(data);

        if (!parsedData.success) {
            this.logger.error(parsedData.error.issues);

            throw new InternalServerErrorException();
        }

        return parsedData.data;
    }
}
