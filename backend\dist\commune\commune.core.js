"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneCore = void 0;
const api_1 = require("@commune/api");
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
let CommuneCore = class CommuneCore {
    constructor(prisma) {
        this.prisma = prisma;
    }
    isHeadMember(commune, userId) {
        return commune.members.some((member) => member.deletedAt === null &&
            member.isHead &&
            member.actorId === userId &&
            member.actorType === client_1.CommuneMemberType.user);
    }
    isMember(commune, userId) {
        return commune.members.some((member) => member.deletedAt === null &&
            member.actorId === userId &&
            member.actorType === client_1.CommuneMemberType.user);
    }
    async getCountOfCommunesWhereUserIsHead(userId, trx = this.prisma) {
        return await trx.communeMember.count({
            where: {
                actorType: client_1.CommuneMemberType.user,
                actorId: userId,
                isHead: true,
                deletedAt: null,
            },
        });
    }
    async getCountOfCommunesWhereUserIsMember(userId, trx = this.prisma) {
        return await trx.communeMember.count({
            where: {
                actorType: client_1.CommuneMemberType.user,
                actorId: userId,
                deletedAt: null,
            },
        });
    }
    async checkIsUserReachedMaxCommuneHeadsLimit(userId, trx = this.prisma) {
        const countOfCommunesWhereUserIsHead = await this.getCountOfCommunesWhereUserIsHead(userId, trx);
        if (countOfCommunesWhereUserIsHead >= api_1.Consts.MAX_COMMUNES_TO_BE_HEAD) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_reached_max_commune_heads_limit"));
        }
    }
    async checkIsUserReachedMaxCommunesLimit(userId, trx = this.prisma) {
        const countOfCommunesWhereUserIsMember = await this.getCountOfCommunesWhereUserIsMember(userId, trx);
        if (countOfCommunesWhereUserIsMember >= api_1.Consts.MAX_COMMUNES_TO_BE_MEMBER) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_reached_max_communes_limit"));
        }
    }
};
exports.CommuneCore = CommuneCore;
exports.CommuneCore = CommuneCore = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CommuneCore);
//# sourceMappingURL=commune.core.js.map