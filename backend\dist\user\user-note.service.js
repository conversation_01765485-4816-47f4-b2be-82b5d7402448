"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserNoteService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
let UserNoteService = class UserNoteService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getUserNote(input, currentUser) {
        return await this.prisma.userNote.findUnique({
            where: {
                sourceUserId_targetUserId: {
                    sourceUserId: currentUser.id,
                    targetUserId: input.userId,
                },
            },
        });
    }
    async updateUserNote(input, currentUser) {
        if (input.text !== null) {
            await this.prisma.userNote.upsert({
                where: {
                    sourceUserId_targetUserId: {
                        sourceUserId: currentUser.id,
                        targetUserId: input.userId,
                    },
                },
                create: {
                    sourceUserId: currentUser.id,
                    targetUserId: input.userId,
                    text: input.text,
                },
                update: {
                    text: input.text,
                },
            });
        }
        else {
            await this.prisma.userNote.delete({
                where: {
                    sourceUserId_targetUserId: {
                        sourceUserId: currentUser.id,
                        targetUserId: input.userId,
                    },
                },
            });
        }
        return true;
    }
};
exports.UserNoteService = UserNoteService;
exports.UserNoteService = UserNoteService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserNoteService);
//# sourceMappingURL=user-note.service.js.map