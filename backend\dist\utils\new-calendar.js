"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NewCalendarDate = void 0;
exports.pad2zeros = pad2zeros;
function pad2zeros(value) {
    return value.toString().padStart(2, "0");
}
class NewCalendarDate extends Date {
    getIsLeapYear(year) {
        return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);
    }
    getDayOfYear(year = this.getFullYear()) {
        const msFromStartOfYear = this.getTime() - new Date(year, 0, 0).getTime();
        return Math.ceil(msFromStartOfYear / NewCalendarDate.MS_PER_DAY);
    }
    getDayPosition(dayOfYear) {
        if (dayOfYear === 366) {
            return {
                month: 14,
                day: 2,
            };
        }
        if (dayOfYear === 365) {
            return {
                month: 14,
                day: 1,
            };
        }
        return {
            month: Math.ceil(dayOfYear / NewCalendarDate.DAYS_PER_MONTH),
            day: dayOfYear % NewCalendarDate.DAYS_PER_MONTH,
        };
    }
    getParsed() {
        const year = this.getFullYear();
        const dayOfYear = this.getDayOfYear(year);
        const { month, day } = this.getDayPosition(dayOfYear);
        return {
            year,
            month,
            day,
        };
    }
    toString() {
        const originalIsoString = super.toISOString();
        const timeFragment = originalIsoString.split("T")[1].slice(0, 8);
        const { year, month, day } = this.getParsed();
        return `${pad2zeros(day)}.${pad2zeros(month)}.${year} ${timeFragment}`;
    }
    toISOString() {
        const originalIsoString = super.toISOString();
        const timeFragment = originalIsoString.split("T")[1];
        const { year, month, day } = this.getParsed();
        console.log({
            originalIsoString,
            timeFragment,
            year,
            month,
            day,
        });
        return `${year}-${pad2zeros(month)}-${pad2zeros(day)}T${timeFragment}`;
    }
}
exports.NewCalendarDate = NewCalendarDate;
NewCalendarDate.MS_PER_DAY = 1000 * 60 * 60 * 24;
NewCalendarDate.DAYS_PER_MONTH = 28;
//# sourceMappingURL=new-calendar.js.map