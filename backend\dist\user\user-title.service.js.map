{"version": 3, "file": "user-title.service.js", "sourceRoot": "", "sources": ["../../src/user/user-title.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,2CAAgE;AAEhE,6CAA6C;AAC7C,6DAA0D;AAC1D,oCAAkD;AAG3C,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IACzB,YAA6B,MAAqB;QAArB,WAAM,GAAN,MAAM,CAAe;IAAG,CAAC;IAEtD,KAAK,CAAC,aAAa,CACf,KAA8B,EAC9B,WAAwB;QAExB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAC;QAExC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;YACxC,KAAK,EAAE;gBACH,MAAM;gBACN,EAAE,EAAE,GAAG,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE;gBACtB,QAAQ,EAAE,QAAQ,IAAI,SAAS;aAClC;YACD,MAAM,EAAE;gBACJ,EAAE,EAAE,IAAI;gBACR,MAAM,EAAE,IAAI;gBACZ,KAAK,EAAE,IAAI;gBACX,QAAQ,EAAE,IAAI;gBACd,IAAI,EAAE;oBACF,MAAM,EAAE;wBACJ,MAAM,EAAE,IAAI;wBACZ,KAAK,EAAE,IAAI;qBACd;iBACJ;gBACD,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,IAAI;gBACf,SAAS,EAAE,WAAW,CAAC,OAAO;aACjC;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,eAAe,CACjB,KAAgC,EAChC,WAAwB;QAExB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACvB,MAAM,IAAI,2BAAkB,CAAC,GAAG,IAAA,iBAAQ,EAAC,eAAe,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,IAAI,EAAE;gBACF,MAAM,EAAE,KAAK,CAAC,MAAM;gBACpB,IAAI,EAAE;oBACF,MAAM,EAAE,IAAA,6BAAqB,EAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC;iBACpD;gBACD,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,KAAK,EAAE,KAAK,CAAC,KAAK;aACrB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,eAAe,CACjB,KAAgC,EAChC,WAAwB;QAExB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,iBAAiB,CAAC;YAC5D,KAAK,EAAE;gBACH,EAAE,EAAE,KAAK,CAAC,EAAE;gBACZ,SAAS,EAAE,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;aACpD;SACJ,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACvB,IAAI,SAAS,CAAC,MAAM,KAAK,WAAW,CAAC,EAAE,EAAE,CAAC;gBACtC,MAAM,IAAI,2BAAkB,CACxB,GAAG,IAAA,iBAAQ,EAAC,wBAAwB,CAAC,CACxC,CAAC;YACN,CAAC;QACL,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE;gBACH,EAAE,EAAE,KAAK,CAAC,EAAE;aACf;YACD,IAAI,EAAE;gBACF,IAAI,EAAE,KAAK,CAAC,IAAI,IAAI;oBAChB,UAAU,EAAE,EAAE;oBACd,MAAM,EAAE,IAAA,6BAAqB,EAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC;iBACpD;gBACD,QAAQ,EAAE,KAAK,CAAC,QAAQ;gBACxB,KAAK,EAAE,KAAK,CAAC,KAAK;aACrB;SACJ,CAAC,CAAC;IACP,CAAC;IAED,KAAK,CAAC,eAAe,CACjB,KAA0B,EAC1B,WAAwB;QAExB,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YACvB,MAAM,IAAI,2BAAkB,CAAC,GAAG,IAAA,iBAAQ,EAAC,eAAe,CAAC,CAAC,CAAC;QAC/D,CAAC;QAED,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,KAAK,EAAE;gBACH,EAAE,EAAE,KAAK,CAAC,EAAE;aACf;YACD,IAAI,EAAE;gBACF,SAAS,EAAE,IAAI,IAAI,EAAE;aACxB;SACJ,CAAC,CAAC;IACP,CAAC;CACJ,CAAA;AAxGY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;qCAE4B,8BAAa;GADzC,gBAAgB,CAwG5B"}