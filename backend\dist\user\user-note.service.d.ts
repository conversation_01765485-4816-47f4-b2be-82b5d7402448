import { User } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
export declare class UserNoteService {
    private readonly prisma;
    constructor(prisma: PrismaService);
    getUserNote(input: User.GetUserNoteInput, currentUser: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        sourceUserId: string;
        targetUserId: string;
        text: string;
    } | null>;
    updateUserNote(input: User.UpdateUserNoteInput, currentUser: CurrentUser): Promise<boolean>;
}
