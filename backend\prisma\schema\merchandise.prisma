// merchandise-status
enum MerchandiseStatus {
    @@map("merchandise_status")

    hidden
    shown
}

// merchandise
model Merchandise {
    @@map("merchandises")

    id String @id @default(nanoid())

    category String
    @@index([category])

    price Int @map("price")

    images Image[] @relation("merchandise_images")
    tags   Tag[]   @relation("merchandise_tags")

    status MerchandiseStatus @default(hidden)

    createdAt DateTime  @map("created_at") @db.Timestamptz(3) @default(now())
    updatedAt DateTime  @map("updated_at") @db.Timestamptz(3) @updatedAt
    deletedAt DateTime? @map("deleted_at") @db.Timestamptz(3)
}
