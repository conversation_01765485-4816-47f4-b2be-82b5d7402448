!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.ModelManager");const t=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(o=n=e,(r=String).prototype.isPrototypeOf(o)||(null===(s=n.constructor)||void 0===s?void 0:s.name)===r.name)?"string":t;var o,n,r,s})(t)===e,o=e=>t=>typeof t===e,n=e=>t=>e===t,r=t("string"),s=t("object"),l=t("array"),a=n(null),c=o("boolean"),i=n(void 0),m=e=>!(e=>null==e)(e),d=o("function"),u=o("number"),f=()=>{},g=e=>()=>e,h=e=>e,p=(e,t)=>e===t;function b(e,...t){return(...o)=>{const n=t.concat(o);return e.apply(null,n)}}const w=e=>t=>!e(t),v=e=>e(),y=g(!1),x=g(!0);class C{constructor(e,t){this.tag=e,this.value=t}static some(e){return new C(!0,e)}static none(){return C.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?C.some(e(this.value)):C.none()}bind(e){return this.tag?e(this.value):C.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:C.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return m(e)?C.some(e):C.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}C.singletonNone=new C(!1);const S=Array.prototype.slice,T=Array.prototype.indexOf,R=Array.prototype.push,D=(e,t)=>{return o=e,n=t,T.call(o,n)>-1;var o,n},O=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return!0;return!1},k=(e,t)=>{const o=[];for(let n=0;n<e;n++)o.push(t(n));return o},E=(e,t)=>{const o=e.length,n=new Array(o);for(let r=0;r<o;r++){const o=e[r];n[r]=t(o,r)}return n},N=(e,t)=>{for(let o=0,n=e.length;o<n;o++)t(e[o],o)},B=(e,t)=>{const o=[],n=[];for(let r=0,s=e.length;r<s;r++){const s=e[r];(t(s,r)?o:n).push(s)}return{pass:o,fail:n}},_=(e,t)=>{const o=[];for(let n=0,r=e.length;n<r;n++){const r=e[n];t(r,n)&&o.push(r)}return o},z=(e,t,o)=>(((e,t)=>{for(let o=e.length-1;o>=0;o--)t(e[o],o)})(e,((e,n)=>{o=t(o,e,n)})),o),A=(e,t,o)=>(N(e,((e,n)=>{o=t(o,e,n)})),o),W=(e,t)=>((e,t,o)=>{for(let n=0,r=e.length;n<r;n++){const r=e[n];if(t(r,n))return C.some(r);if(o(r,n))break}return C.none()})(e,t,y),L=(e,t)=>{for(let o=0,n=e.length;o<n;o++)if(t(e[o],o))return C.some(o);return C.none()},M=e=>{const t=[];for(let o=0,n=e.length;o<n;++o){if(!l(e[o]))throw new Error("Arr.flatten item "+o+" was not an array, input: "+e);R.apply(t,e[o])}return t},j=(e,t)=>M(E(e,t)),P=(e,t)=>{for(let o=0,n=e.length;o<n;++o)if(!0!==t(e[o],o))return!1;return!0},I=(e,t)=>{const o={};for(let n=0,r=e.length;n<r;n++){const r=e[n];o[String(r)]=t(r,n)}return o},F=(e,t)=>t>=0&&t<e.length?C.some(e[t]):C.none(),H=e=>F(e,0),$=e=>F(e,e.length-1),V=(e,t)=>{for(let o=0;o<e.length;o++){const n=t(e[o],o);if(n.isSome())return n}return C.none()},q=Object.keys,U=Object.hasOwnProperty,G=(e,t)=>{const o=q(e);for(let n=0,r=o.length;n<r;n++){const r=o[n];t(e[r],r)}},K=(e,t)=>Y(e,((e,o)=>({k:o,v:t(e,o)}))),Y=(e,t)=>{const o={};return G(e,((e,n)=>{const r=t(e,n);o[r.k]=r.v})),o},J=(e,t)=>{const o=[];return G(e,((e,n)=>{o.push(t(e,n))})),o},Q=e=>J(e,h),X=(e,t)=>U.call(e,t),Z=e=>{if(!l(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],o={};return N(e,((n,r)=>{const s=q(n);if(1!==s.length)throw new Error("one and only one name per case");const a=s[0],c=n[a];if(void 0!==o[a])throw new Error("duplicate key detected:"+a);if("cata"===a)throw new Error("cannot have a case named cata (sorry)");if(!l(c))throw new Error("case arguments must be an array");t.push(a),o[a]=(...o)=>{const n=o.length;if(n!==c.length)throw new Error("Wrong number of arguments to case "+a+". Expected "+c.length+" ("+c+"), got "+n);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[r].apply(null,o)},match:e=>{const n=q(e);if(t.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+n.join(","));if(!P(t,(e=>D(n,e))))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+t.join(", "));return e[a].apply(null,o)},log:e=>{console.log(e,{constructors:t,constructor:a,params:o})}}}})),o},ee=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},te=e=>e.slice(0).sort(),oe=(e,t)=>{const o=_(t,(t=>!D(e,t)));o.length>0&&(e=>{throw new Error("Unsupported keys for object: "+te(e).join(", "))})(o)},ne=e=>((e,t)=>((e,t,o)=>{if(0===t.length)throw new Error("You must specify at least one required field.");return((e,t)=>{if(!l(t))throw new Error("The "+e+" fields must be an array. Was: "+t+".");N(t,(t=>{if(!r(t))throw new Error("The value "+t+" in the "+e+" fields was not a string.")}))})("required",t),(e=>{const t=te(e);W(t,((e,o)=>o<t.length-1&&e===t[o+1])).each((e=>{throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}))})(t),n=>{const r=q(n);P(t,(e=>D(r,e)))||((e,t)=>{throw new Error("All required keys ("+te(e).join(", ")+") were not specified. Specified keys were: "+te(t).join(", ")+".")})(t,r),e(t,r);const s=_(t,(e=>!o.validate(n[e],e)));return s.length>0&&((e,t)=>{throw new Error("All values need to be of type: "+t+". Keys ("+te(e).join(", ")+") were not.")})(s,o.label),n}})(e,t,{validate:d,label:"function"}))(oe,e),re=e=>{const t=t=>t(e),o=g(e),n=()=>r,r={tag:!0,inner:e,fold:(t,o)=>o(e),isValue:x,isError:y,map:t=>le.value(t(e)),mapError:n,bind:t,exists:t,forall:t,getOr:o,or:n,getOrThunk:o,orThunk:n,getOrDie:o,each:t=>{t(e)},toOptional:()=>C.some(e)};return r},se=e=>{const t=()=>o,o={tag:!1,inner:e,fold:(t,o)=>t(e),isValue:y,isError:x,map:t,mapError:t=>le.error(t(e)),bind:t,exists:y,forall:x,getOr:h,or:h,getOrThunk:v,orThunk:v,getOrDie:(n=String(e),()=>{throw new Error(n)}),each:f,toOptional:C.none};var n;return o},le={value:re,error:se,fromOption:(e,t)=>e.fold((()=>se(t)),re)},ae="undefined"!=typeof window?window:Function("return this;")(),ce=e=>{const t=e.replace(/\./g,"-");return{resolve:e=>t+"-"+e}},ie=(e,t,o=p)=>e.exists((e=>o(e,t))),me=e=>{const t=[],o=e=>{t.push(e)};for(let t=0;t<e.length;t++)e[t].each(o);return t},de=(e,t)=>e?C.some(t):C.none(),ue=(e,t)=>((e,t)=>{let o=null!=t?t:ae;for(let t=0;t<e.length&&null!=o;++t)o=o[e[t]];return o})(e.split("."),t),fe=()=>{const e=(e=>{const t=ee(C.none()),o=()=>t.get().each(e);return{clear:()=>{o(),t.set(C.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{o(),t.set(C.some(e))}}})(f);return{...e,on:t=>e.get().each(t)}},ge=(e,t,o)=>""===t||e.length>=t.length&&e.substr(o,o+t.length)===t,he=(e,t,o=0,n)=>{const r=e.indexOf(t,o);return-1!==r&&(!!i(n)||r+t.length<=n)},pe=(e,t)=>ge(e,t,0),be=(e,t)=>ge(e,t,e.length-t.length),we=(e=>t=>t.replace(e,""))(/^\s+|\s+$/g),ve=e=>e.length>0,ye=e=>{let t,o=!1;return(...n)=>(o||(o=!0,t=e.apply(null,n)),t)},xe=["tfoot","thead","tbody","colgroup"],Ce=(e,t,o)=>({element:e,rowspan:t,colspan:o}),Se=(e,t,o)=>({element:e,cells:t,section:o}),Te=(e,t,o)=>({element:e,isNew:t,isLocked:o}),Re=(e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}),De=(e,t,o)=>{const n=e.cells,r=n.slice(0,t),s=n.slice(t),l=r.concat(o).concat(s);return Ee(e,l)},Oe=(e,t,o)=>De(e,t,[o]),ke=(e,t,o)=>{e.cells[t]=o},Ee=(e,t)=>Re(e.element,t,e.section,e.isNew),Ne=(e,t)=>e.cells[t],Be=(e,t)=>Ne(e,t).element,_e=e=>e.cells.length,ze=e=>{const t=B(e,(e=>"colgroup"===e.section));return{rows:t.fail,cols:t.pass}},Ae=(e,t,o)=>{const n=E(e.cells,o);return Re(t(e.element),n,e.section,!0)},We=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},Le={fromHtml:(e,t)=>{const o=(t||document).createElement("div");if(o.innerHTML=e,!o.hasChildNodes()||o.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return We(o.childNodes[0])},fromTag:(e,t)=>{const o=(t||document).createElement(e);return We(o)},fromText:(e,t)=>{const o=(t||document).createTextNode(e);return We(o)},fromDom:We,fromPoint:(e,t,o)=>C.from(e.dom.elementFromPoint(t,o)).map(We)},Me=(e,t)=>{const o=e.document.createRange();return o.selectNode(t.dom),o},je=(e,t)=>{const o=e.document.createRange();return Pe(o,t),o},Pe=(e,t)=>e.selectNodeContents(t.dom),Ie=(e,t,o)=>{const n=e.document.createRange();var r;return r=n,t.fold((e=>{r.setStartBefore(e.dom)}),((e,t)=>{r.setStart(e.dom,t)}),(e=>{r.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,o)=>{e.setEnd(t.dom,o)}),(t=>{e.setEndAfter(t.dom)}))})(n,o),n},Fe=(e,t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},He=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom,width:e.width,height:e.height}),$e=Z([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Ve=(e,t,o)=>t(Le.fromDom(o.startContainer),o.startOffset,Le.fromDom(o.endContainer),o.endOffset),qe=(e,t)=>{const o=((e,t)=>t.match({domRange:e=>({ltr:g(e),rtl:C.none}),relative:(t,o)=>({ltr:ye((()=>Ie(e,t,o))),rtl:ye((()=>C.some(Ie(e,o,t))))}),exact:(t,o,n,r)=>({ltr:ye((()=>Fe(e,t,o,n,r))),rtl:ye((()=>C.some(Fe(e,n,r,t,o))))})}))(e,t);return((e,t)=>{const o=t.ltr();return o.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>$e.rtl(Le.fromDom(e.endContainer),e.endOffset,Le.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>Ve(0,$e.ltr,o))):Ve(0,$e.ltr,o)})(0,o)},Ue=(e,t)=>qe(e,t).match({ltr:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(t.dom,o),s.setEnd(n.dom,r),s},rtl:(t,o,n,r)=>{const s=e.document.createRange();return s.setStart(n.dom,r),s.setEnd(t.dom,o),s}});$e.ltr,$e.rtl;const Ge=(e,t)=>{const o=e.dom;if(1!==o.nodeType)return!1;{const e=o;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},Ke=e=>1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType||0===e.childElementCount,Ye=(e,t)=>{const o=void 0===t?document:t.dom;return Ke(o)?C.none():C.from(o.querySelector(e)).map(Le.fromDom)},Je=(e,t)=>e.dom===t.dom,Qe=(e,t)=>{const o=e.dom,n=t.dom;return o!==n&&o.contains(n)},Xe=Ge,Ze=()=>et(0,0),et=(e,t)=>({major:e,minor:t}),tt={nu:et,detect:(e,t)=>{const o=String(t).toLowerCase();return 0===e.length?Ze():((e,t)=>{const o=((e,t)=>{for(let o=0;o<e.length;o++){const n=e[o];if(n.test(t))return n}})(e,t);if(!o)return{major:0,minor:0};const n=e=>Number(t.replace(o,"$"+e));return et(n(1),n(2))})(e,o)},unknown:Ze},ot=(e,t)=>{const o=String(t).toLowerCase();return W(e,(e=>e.search(o)))},nt=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,rt=e=>t=>he(t,e),st=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>he(e,"edge/")&&he(e,"chrome")&&he(e,"safari")&&he(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,nt],search:e=>he(e,"chrome")&&!he(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>he(e,"msie")||he(e,"trident")},{name:"Opera",versionRegexes:[nt,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:rt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:rt("firefox")},{name:"Safari",versionRegexes:[nt,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(he(e,"safari")||he(e,"mobile/"))&&he(e,"applewebkit")}],lt=[{name:"Windows",search:rt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>he(e,"iphone")||he(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:rt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:rt("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:rt("linux"),versionRegexes:[]},{name:"Solaris",search:rt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:rt("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:rt("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],at={browsers:g(st),oses:g(lt)},ct="Edge",it="Chromium",mt="Opera",dt="Firefox",ut="Safari",ft=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isEdge:n(ct),isChromium:n(it),isIE:n("IE"),isOpera:n(mt),isFirefox:n(dt),isSafari:n(ut)}},gt=()=>ft({current:void 0,version:tt.unknown()}),ht=ft,pt=(g(ct),g(it),g("IE"),g(mt),g(dt),g(ut),"Windows"),bt="Android",wt="Linux",vt="macOS",yt="Solaris",xt="FreeBSD",Ct="ChromeOS",St=e=>{const t=e.current,o=e.version,n=e=>()=>t===e;return{current:t,version:o,isWindows:n(pt),isiOS:n("iOS"),isAndroid:n(bt),isMacOS:n(vt),isLinux:n(wt),isSolaris:n(yt),isFreeBSD:n(xt),isChromeOS:n(Ct)}},Tt=()=>St({current:void 0,version:tt.unknown()}),Rt=St,Dt=(g(pt),g("iOS"),g(bt),g(wt),g(vt),g(yt),g(xt),g(Ct),e=>window.matchMedia(e).matches);let Ot=ye((()=>((e,t,o)=>{const n=at.browsers(),r=at.oses(),s=t.bind((e=>((e,t)=>V(t.brands,(t=>{const o=t.brand.toLowerCase();return W(e,(e=>{var t;return o===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:tt.nu(parseInt(t.version,10),0)})))})))(n,e))).orThunk((()=>((e,t)=>ot(e,t).map((e=>{const o=tt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(n,e))).fold(gt,ht),l=((e,t)=>ot(e,t).map((e=>{const o=tt.detect(e.versionRegexes,t);return{current:e.name,version:o}})))(r,e).fold(Tt,Rt),a=((e,t,o,n)=>{const r=e.isiOS()&&!0===/ipad/i.test(o),s=e.isiOS()&&!r,l=e.isiOS()||e.isAndroid(),a=l||n("(pointer:coarse)"),c=r||!s&&l&&n("(min-device-width:768px)"),i=s||l&&!c,m=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(o),d=!i&&!c&&!m;return{isiPad:g(r),isiPhone:g(s),isTablet:g(c),isPhone:g(i),isTouch:g(a),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:g(m),isDesktop:g(d)}})(l,s,e,o);return{browser:s,os:l,deviceType:a}})(window.navigator.userAgent,C.from(window.navigator.userAgentData),Dt)));const kt=()=>Ot(),Et=Object.getPrototypeOf,Nt=e=>{const t=ue("ownerDocument.defaultView",e);return s(e)&&((e=>((e,t)=>{const o=((e,t)=>ue(e,t))(e,t);if(null==o)throw new Error(e+" not available on this browser");return o})("HTMLElement",e))(t).prototype.isPrototypeOf(e)||/^HTML\w*Element$/.test(Et(e).constructor.name))},Bt=e=>e.dom.nodeName.toLowerCase(),_t=e=>e.dom.nodeType,zt=e=>t=>_t(t)===e,At=e=>8===_t(e)||"#comment"===Bt(e),Wt=e=>Lt(e)&&Nt(e.dom),Lt=zt(1),Mt=zt(3),jt=zt(9),Pt=zt(11),It=e=>t=>Lt(t)&&Bt(t)===e,Ft=e=>Le.fromDom(e.dom.ownerDocument),Ht=e=>jt(e)?e:Ft(e),$t=e=>C.from(e.dom.parentNode).map(Le.fromDom),Vt=e=>C.from(e.dom.parentElement).map(Le.fromDom),qt=(e,t)=>{const o=d(t)?t:y;let n=e.dom;const r=[];for(;null!==n.parentNode&&void 0!==n.parentNode;){const e=n.parentNode,t=Le.fromDom(e);if(r.push(t),!0===o(t))break;n=e}return r},Ut=e=>C.from(e.dom.previousSibling).map(Le.fromDom),Gt=e=>C.from(e.dom.nextSibling).map(Le.fromDom),Kt=e=>E(e.dom.childNodes,Le.fromDom),Yt=(e,t)=>{const o=e.dom.childNodes;return C.from(o[t]).map(Le.fromDom)},Jt=e=>Pt(e)&&m(e.dom.host),Qt=e=>Le.fromDom(e.dom.getRootNode()),Xt=e=>Le.fromDom(e.dom.host),Zt=e=>{const t=Le.fromDom((e=>{if(m(e.target)){const t=Le.fromDom(e.target);if(Lt(t)&&m(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return H(t)}}return C.from(e.target)})(e).getOr(e.target)),o=()=>e.stopPropagation(),n=()=>e.preventDefault(),r=(s=n,l=o,(...e)=>s(l.apply(null,e)));var s,l;return((e,t,o,n,r,s,l)=>({target:e,x:t,y:o,stop:n,prevent:r,kill:s,raw:l}))(t,e.clientX,e.clientY,o,n,r,e)},eo=(e,t,o,n)=>{e.dom.removeEventListener(t,o,n)},to=x,oo=(e,t,o)=>((e,t,o,n)=>((e,t,o,n,r)=>{const s=((e,t)=>o=>{e(o)&&t(Zt(o))})(o,n);return e.dom.addEventListener(t,s,r),{unbind:b(eo,e,t,s,r)}})(e,t,o,n,!1))(e,t,to,o),no=Zt,ro=(e,t)=>{$t(e).each((o=>{o.dom.insertBefore(t.dom,e.dom)}))},so=(e,t)=>{Gt(e).fold((()=>{$t(e).each((e=>{ao(e,t)}))}),(e=>{ro(e,t)}))},lo=(e,t)=>{const o=(e=>Yt(e,0))(e);o.fold((()=>{ao(e,t)}),(o=>{e.dom.insertBefore(t.dom,o.dom)}))},ao=(e,t)=>{e.dom.appendChild(t.dom)},co=(e,t)=>{ro(e,t),ao(t,e)},io=(e,t)=>{N(t,((o,n)=>{const r=0===n?e:t[n-1];so(r,o)}))},mo=(e,t)=>{N(t,(t=>{ao(e,t)}))},uo=(e,t,o)=>{if(!(r(o)||c(o)||u(o)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",o,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,o+"")},fo=(e,t,o)=>{uo(e.dom,t,o)},go=(e,t)=>{const o=e.dom;G(t,((e,t)=>{uo(o,t,e)}))},ho=(e,t)=>{const o=e.dom.getAttribute(t);return null===o?void 0:o},po=(e,t)=>C.from(ho(e,t)),bo=(e,t)=>{e.dom.removeAttribute(t)},wo=e=>A(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{}),vo=e=>{e.dom.textContent="",N(Kt(e),(e=>{yo(e)}))},yo=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},xo=e=>{const t=Kt(e);t.length>0&&io(e,t),yo(e)},Co=(e,t)=>Le.fromDom(e.dom.cloneNode(t)),So=e=>Co(e,!1),To=e=>Co(e,!0),Ro=(e,t)=>{const o=Le.fromTag(t),n=wo(e);return go(o,n),o},Do=e=>void 0!==e.style&&d(e.style.getPropertyValue),Oo=e=>{const t=Mt(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const o=t.ownerDocument;return(e=>{const t=Qt(e);return Jt(t)?C.some(t):C.none()})(Le.fromDom(t)).fold((()=>o.body.contains(t)),(n=Oo,r=Xt,e=>n(r(e))));var n,r},ko=(e,t,o)=>{if(!r(o))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",o,":: Element ",e),new Error("CSS value must be a string: "+o);Do(e)&&e.style.setProperty(t,o)},Eo=(e,t,o)=>{const n=e.dom;ko(n,t,o)},No=(e,t)=>{const o=e.dom;G(t,((e,t)=>{ko(o,t,e)}))},Bo=(e,t)=>{const o=e.dom,n=window.getComputedStyle(o).getPropertyValue(t);return""!==n||Oo(e)?n:_o(o,t)},_o=(e,t)=>Do(e)?e.style.getPropertyValue(t):"",zo=(e,t)=>{const o=e.dom,n=_o(o,t);return C.from(n).filter((e=>e.length>0))},Ao=(e,t)=>{((e,t)=>{Do(e)&&e.style.removeProperty(t)})(e.dom,t),ie(po(e,"style").map(we),"")&&bo(e,"style")},Wo=(e,t)=>{const o=o=>{const n=t(o);if(n<=0||null===n){const t=Bo(o,e);return parseFloat(t)||0}return n},n=(e,t)=>A(t,((t,o)=>{const n=Bo(e,o),r=void 0===n?0:parseInt(n,10);return isNaN(r)?t:t+r}),0);return{set:(t,o)=>{if(!u(o)&&!o.match(/^[0-9]+$/))throw new Error(e+".set accepts only positive integer values. Value was "+o);const n=t.dom;Do(n)&&(n.style[e]=o+"px")},get:o,getOuter:o,aggregate:n,max:(e,t,o)=>{const r=n(e,o);return t>r?t-r:0}}},Lo=(e,t,o)=>((e,t)=>(e=>{const t=parseFloat(e);return isNaN(t)?C.none():C.some(t)})(e).getOr(t))(Bo(e,t),o),Mo=Wo("height",(e=>{const t=e.dom;return Oo(e)?t.getBoundingClientRect().height:t.offsetHeight})),jo=e=>Mo.get(e),Po=e=>Mo.getOuter(e),Io=Wo("width",(e=>e.dom.offsetWidth));Wo("width",(e=>{const t=e.dom;return Oo(e)?t.getBoundingClientRect().width:t.offsetWidth}));const Fo=e=>Io.get(e),Ho=e=>Io.getOuter(e),$o=e=>((e,t)=>{const o=e.dom,n=o.getBoundingClientRect().width||o.offsetWidth;return"border-box"===t?n:((e,t,o,n)=>t-Lo(e,`padding-${o}`,0)-Lo(e,`padding-${n}`,0)-Lo(e,`border-${o}-width`,0)-Lo(e,`border-${n}-width`,0))(e,n,"left","right")})(e,"content-box"),Vo=(e,t)=>({left:e,top:t,translate:(o,n)=>Vo(e+o,t+n)}),qo=Vo,Uo=(e,t)=>void 0!==e?e:void 0!==t?t:0,Go=e=>{const t=e.dom.ownerDocument,o=t.body,n=t.defaultView,r=t.documentElement;if(o===e.dom)return qo(o.offsetLeft,o.offsetTop);const s=Uo(null==n?void 0:n.pageYOffset,r.scrollTop),l=Uo(null==n?void 0:n.pageXOffset,r.scrollLeft),a=Uo(r.clientTop,o.clientTop),c=Uo(r.clientLeft,o.clientLeft);return Ko(e).translate(l-c,s-a)},Ko=e=>{const t=e.dom,o=t.ownerDocument.body;return o===t?qo(o.offsetLeft,o.offsetTop):Oo(e)?(e=>{const t=e.getBoundingClientRect();return qo(t.left,t.top)})(t):qo(0,0)},Yo=(e=>{const t=t=>e(t)?C.from(t.dom.nodeValue):C.none();return{get:o=>{if(!e(o))throw new Error("Can only get text value of a text node");return t(o).getOr("")},getOption:t,set:(t,o)=>{if(!e(t))throw new Error("Can only set raw text value of a text node");t.dom.nodeValue=o}}})(Mt),Jo=e=>Yo.get(e),Qo=e=>Yo.getOption(e),Xo=(e,t)=>Yo.set(e,t),Zo=(e,t)=>o=>"rtl"===en(o)?t:e,en=e=>"rtl"===Bo(e,"direction")?"rtl":"ltr";var tn=(e,t,o,n,r)=>e(o,n)?C.some(o):d(r)&&r(o)?C.none():t(o,n,r);const on=(e,t,o)=>{let n=e.dom;const r=d(o)?o:y;for(;n.parentNode;){n=n.parentNode;const e=Le.fromDom(n);if(t(e))return C.some(e);if(r(e))break}return C.none()},nn=(e,t,o)=>tn(((e,t)=>t(e)),on,e,t,o),rn=(e,t,o)=>on(e,(e=>Ge(e,t)),o),sn=(e,t)=>(e=>W(e.dom.childNodes,(e=>{return o=Le.fromDom(e),Ge(o,t);var o})).map(Le.fromDom))(e),ln=(e,t)=>Ye(t,e),an=(e,t,o)=>tn(((e,t)=>Ge(e,t)),rn,e,t,o),cn=e=>void 0!==e.dom.classList,mn=(e,t)=>((e,t,o)=>{const n=((e,t)=>{const o=ho(e,t);return void 0===o||""===o?[]:o.split(" ")})(e,t).concat([o]);return fo(e,t,n.join(" ")),!0})(e,"class",t),dn=(e,t)=>{cn(e)?e.dom.classList.add(t):mn(e,t)},un=(e,t)=>cn(e)&&e.dom.classList.contains(t),fn=e=>an(e,"[contenteditable]"),gn=(e,t=!1)=>Oo(e)?e.dom.isContentEditable:fn(e).fold(g(t),(e=>"true"===hn(e))),hn=e=>e.dom.contentEditable,pn=(e,t)=>{let o=[];return N(Kt(e),(e=>{t(e)&&(o=o.concat([e])),o=o.concat(pn(e,t))})),o},bn=(e,t,o)=>((e,o,n)=>_(qt(e,n),(e=>Ge(e,t))))(e,0,o),wn=(e,t)=>(e=>_(Kt(e),(e=>Ge(e,t))))(e),vn=(e,t)=>((e,t)=>{const o=void 0===t?document:t.dom;return Ke(o)?[]:E(o.querySelectorAll(e),Le.fromDom)})(t,e),yn=e=>"img"===Bt(e)?1:Qo(e).fold((()=>Kt(e).length),(e=>e.length)),xn=["img","br"],Cn=e=>Qo(e).filter((e=>0!==e.trim().length||e.indexOf("\xa0")>-1)).isSome()||D(xn,Bt(e))||(e=>Wt(e)&&"false"===ho(e,"contenteditable"))(e),Sn=e=>((e,t)=>{const o=e=>{for(let n=0;n<e.childNodes.length;n++){const r=Le.fromDom(e.childNodes[n]);if(t(r))return C.some(r);const s=o(e.childNodes[n]);if(s.isSome())return s}return C.none()};return o(e.dom)})(e,Cn),Tn=e=>Rn(e,Cn),Rn=(e,t)=>{const o=e=>{const n=Kt(e);for(let e=n.length-1;e>=0;e--){const r=n[e];if(t(r))return C.some(r);const s=o(r);if(s.isSome())return s}return C.none()};return o(e)},Dn=(e,t,o,n)=>({start:e,soffset:t,finish:o,foffset:n}),On=Z([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),kn={before:On.before,on:On.on,after:On.after,cata:(e,t,o,n)=>e.fold(t,o,n),getStart:e=>e.fold(h,h,h)},En=Z([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Nn={domRange:En.domRange,relative:En.relative,exact:En.exact,exactFromRange:e=>En.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>Le.fromDom(e.startContainer),relative:(e,t)=>kn.getStart(e),exact:(e,t,o,n)=>e}))(e);return Le.fromDom(Ht(t).dom.defaultView)},range:Dn},Bn=(e,t)=>{const o=Bt(e);return"input"===o?kn.after(e):D(["br","img"],o)?0===t?kn.before(e):kn.after(e):kn.on(e,t)},_n=e=>C.from(e.getSelection()),zn=(e,t)=>{_n(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))},An=(e,t,o,n,r)=>{const s=Fe(e,t,o,n,r);zn(e,s)},Wn=(e,t)=>qe(e,t).match({ltr:(t,o,n,r)=>{An(e,t,o,n,r)},rtl:(t,o,n,r)=>{_n(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,o,n.dom,r);else if(s.extend)try{((e,t,o,n,r,s)=>{t.collapse(o.dom,n),t.extend(r.dom,s)})(0,s,t,o,n,r)}catch(s){An(e,n,r,t,o)}else An(e,n,r,t,o)}))}}),Ln=(e,t,o,n,r)=>{const s=((e,t,o,n)=>{const r=Bn(e,t),s=Bn(o,n);return Nn.relative(r,s)})(t,o,n,r);Wn(e,s)},Mn=(e,t,o)=>{const n=((e,t)=>{const o=e.fold(kn.before,Bn,kn.after),n=t.fold(kn.before,Bn,kn.after);return Nn.relative(o,n)})(t,o);Wn(e,n)},jn=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),o=e.getRangeAt(e.rangeCount-1);return C.some(Dn(Le.fromDom(t.startContainer),t.startOffset,Le.fromDom(o.endContainer),o.endOffset))}return C.none()},Pn=e=>{if(null===e.anchorNode||null===e.focusNode)return jn(e);{const t=Le.fromDom(e.anchorNode),o=Le.fromDom(e.focusNode);return((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=Ft(e).dom.createRange();return r.setStart(e.dom,t),r.setEnd(o.dom,n),r})(e,t,o,n),s=Je(e,o)&&t===n;return r.collapsed&&!s})(t,e.anchorOffset,o,e.focusOffset)?C.some(Dn(t,e.anchorOffset,o,e.focusOffset)):jn(e)}},In=(e,t,o=!0)=>{const n=(o?je:Me)(e,t);zn(e,n)},Fn=e=>(e=>_n(e).filter((e=>e.rangeCount>0)).bind(Pn))(e).map((e=>Nn.exact(e.start,e.soffset,e.finish,e.foffset))),Hn=(e,t,o)=>((e,t,o)=>((e,t,o)=>e.caretPositionFromPoint?((e,t,o)=>{var n;return C.from(null===(n=e.caretPositionFromPoint)||void 0===n?void 0:n.call(e,t,o)).bind((t=>{if(null===t.offsetNode)return C.none();const o=e.createRange();return o.setStart(t.offsetNode,t.offset),o.collapse(),C.some(o)}))})(e,t,o):e.caretRangeFromPoint?((e,t,o)=>{var n;return C.from(null===(n=e.caretRangeFromPoint)||void 0===n?void 0:n.call(e,t,o))})(e,t,o):C.none())(e.document,t,o).map((e=>Dn(Le.fromDom(e.startContainer),e.startOffset,Le.fromDom(e.endContainer),e.endOffset))))(e,t,o),$n={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},Vn=(()=>{const e="[0-9]+",t="[eE][+-]?"+e,o=e=>`(?:${e})?`,n=["Infinity",e+"\\."+o(e)+o(t),"\\."+e+o(t),e+o(t)].join("|");return new RegExp(`^([+-]?(?:${n}))(.*)$`)})(),qn=e=>E(e,g(0)),Un=(e,t,o,n,r)=>r(e.slice(0,t)).concat(n).concat(r(e.slice(o))),Gn=e=>(t,o,n,r)=>{if(e(n)){const e=Math.max(r,t[o]-Math.abs(n)),s=Math.abs(e-t[o]);return n>=0?s:-s}return n},Kn=Gn((e=>e<0)),Yn=Gn(x),Jn=()=>{const e=(e,t,o,n)=>{const r=(100+o)/100,s=Math.max(n,(e[t]+o)/r);return E(e,((e,o)=>(o===t?s:e/r)-e))},t=(t,o,n,r,s,l)=>l?e(t,o,r,s):((e,t,o,n,r)=>{const s=Kn(e,t,n,r);return Un(e,t,o+1,[s,0],qn)})(t,o,n,r,s);return{resizeTable:(e,t)=>e(t),clampTableDelta:Kn,calcLeftEdgeDeltas:t,calcMiddleDeltas:(e,o,n,r,s,l,a)=>t(e,n,r,s,l,a),calcRightEdgeDeltas:(t,o,n,r,s,l)=>{if(l)return e(t,n,r,s);{const e=Kn(t,n,r,s);return qn(t.slice(0,n)).concat([e])}},calcRedestributedWidths:(e,t,o,n)=>{if(n){const n=(t+o)/t,r=E(e,(e=>e/n));return{delta:100*n-100,newSizes:r}}return{delta:o,newSizes:e}}}},Qn=()=>{const e=(e,t,o,n,r)=>{const s=Yn(e,n>=0?o:t,n,r);return Un(e,t,o+1,[s,-s],qn)};return{resizeTable:(e,t,o)=>{o&&e(t)},clampTableDelta:(e,t,o,n,r)=>{if(r){if(o>=0)return o;{const t=A(e,((e,t)=>e+t-n),0);return Math.max(-t,o)}}return Kn(e,t,o,n)},calcLeftEdgeDeltas:e,calcMiddleDeltas:(t,o,n,r,s,l)=>e(t,n,r,s,l),calcRightEdgeDeltas:(e,t,o,n,r,s)=>{if(s)return qn(e);{const t=n/e.length;return E(e,g(t))}},calcRedestributedWidths:(e,t,o,n)=>({delta:0,newSizes:e})}},Xn=(e,t,o=0)=>po(e,t).map((e=>parseInt(e,10))).getOr(o),Zn=(e,t)=>Xn(e,t,1),er=e=>It("col")(e)?Xn(e,"span",1)>1:Zn(e,"colspan")>1,tr=(e,t)=>parseInt(Bo(e,t),10),or=g(10),nr=g(10),rr=(e,t)=>sr(e,t,x),sr=(e,t,o)=>j(Kt(e),(e=>Ge(e,t)?o(e)?[e]:[]:sr(e,t,o))),lr=(e,t)=>((e,t,o=y)=>o(t)?C.none():D(e,Bt(t))?C.some(t):rn(t,e.join(","),(e=>Ge(e,"table")||o(e))))(["td","th"],e,t),ar=e=>rr(e,"th,td"),cr=e=>Ge(e,"colgroup")?wn(e,"col"):j(dr(e),(e=>wn(e,"col"))),ir=(e,t)=>an(e,"table",t),mr=e=>rr(e,"tr"),dr=e=>ir(e).fold(g([]),(e=>wn(e,"colgroup"))),ur=It("th"),fr=e=>P(e,(e=>ur(e.element))),gr=(e,t)=>e&&t?"sectionCells":e?"section":"cells",hr=e=>{const t="thead"===e.section,o=ie(pr(e.cells),"th");return"tfoot"===e.section?{type:"footer"}:t||o?{type:"header",subType:gr(t,o)}:{type:"body"}},pr=e=>{const t=_(e,(e=>ur(e.element)));return 0===t.length?C.some("td"):t.length===e.length?C.some("th"):C.none()},br=(e,t)=>E(e,(e=>{if("colgroup"===Bt(e)){const t=E(cr(e),(e=>{const t=Xn(e,"span",1);return Ce(e,1,t)}));return Se(e,t,"colgroup")}{const o=E(ar(e),(e=>{const t=Xn(e,"rowspan",1),o=Xn(e,"colspan",1);return Ce(e,t,o)}));return Se(e,o,t(e))}})),wr=e=>$t(e).map((e=>{const t=Bt(e);return(e=>D(xe,e))(t)?t:"tbody"})).getOr("tbody"),vr=e=>{const t=mr(e),o=[...dr(e),...t];return br(o,wr)},yr="data-snooker-locked-cols",xr=e=>po(e,yr).bind((e=>C.from(e.match(/\d+/g)))).map((e=>I(e,x))),Cr=e=>{const t=A(ze(e).rows,((e,t)=>(N(t.cells,((t,o)=>{t.isLocked&&(e[o]=!0)})),e)),{}),o=J(t,((e,t)=>parseInt(t,10)));return(e=>{const t=S.call(e,0);return t.sort(void 0),t})(o)},Sr=(e,t)=>e+","+t,Tr=(e,t)=>{const o=j(e.all,(e=>e.cells));return _(o,t)},Rr=e=>{const t={},o=[],n=H(e).map((e=>e.element)).bind(ir).bind(xr).getOr({});let r=0,s=0,l=0;const{pass:a,fail:c}=B(e,(e=>"colgroup"===e.section));N(c,(e=>{const a=[];N(e.cells,(e=>{let o=0;for(;void 0!==t[Sr(l,o)];)o++;const r=((e,t)=>X(e,t)&&void 0!==e[t]&&null!==e[t])(n,o.toString()),c=((e,t,o,n,r,s)=>({element:e,rowspan:t,colspan:o,row:n,column:r,isLocked:s}))(e.element,e.rowspan,e.colspan,l,o,r);for(let n=0;n<e.colspan;n++)for(let r=0;r<e.rowspan;r++){const e=o+n,a=Sr(l+r,e);t[a]=c,s=Math.max(s,e+1)}a.push(c)})),r++,o.push(Se(e.element,a,e.section)),l++}));const{columns:i,colgroups:m}=$(a).map((e=>{const t=(e=>{const t={};let o=0;return N(e.cells,(e=>{const n=e.colspan;k(n,(r=>{const s=o+r;t[s]=((e,t,o)=>({element:e,colspan:t,column:o}))(e.element,n,s)})),o+=n})),t})(e),o=((e,t)=>({element:e,columns:t}))(e.element,Q(t));return{colgroups:[o],columns:t}})).getOrThunk((()=>({colgroups:[],columns:{}}))),d=((e,t)=>({rows:e,columns:t}))(r,s);return{grid:d,access:t,all:o,columns:i,colgroups:m}},Dr=e=>{const t=vr(e);return Rr(t)},Or=Rr,kr=(e,t,o)=>C.from(e.access[Sr(t,o)]),Er=(e,t,o)=>{const n=Tr(e,(e=>o(t,e.element)));return n.length>0?C.some(n[0]):C.none()},Nr=Tr,Br=e=>j(e.all,(e=>e.cells)),_r=e=>Q(e.columns),zr=e=>q(e.columns).length>0,Ar=(e,t)=>C.from(e.columns[t]),Wr=(e,t,o)=>Te(o(e.element,t),!0,e.isLocked),Lr=(e,t)=>e.section!==t?Re(e.element,e.cells,t,e.isNew):e,Mr=()=>({transformRow:Lr,transformCell:(e,t,o)=>{const n=o(e.element,t),r="td"!==Bt(n)?(e=>{const t=Ro(e,"td");so(e,t);const o=Kt(e);return mo(t,o),yo(e),t})(n):n;return Te(r,e.isNew,e.isLocked)}}),jr=()=>({transformRow:Lr,transformCell:Wr}),Pr=()=>({transformRow:(e,t)=>Lr(e,"thead"===t?"tbody":t),transformCell:Wr}),Ir=Mr,Fr=jr,Hr=Pr,$r=()=>({transformRow:h,transformCell:Wr}),Vr=(e,t=x)=>{const o=e.grid,n=k(o.columns,h),r=k(o.rows,h);return E(n,(o=>qr((()=>j(r,(t=>kr(e,t,o).filter((e=>e.column===o)).toArray()))),(e=>1===e.colspan&&t(e.element)),(()=>kr(e,0,o)))))},qr=(e,t,o)=>{const n=e();return W(n,t).orThunk((()=>C.from(n[0]).orThunk(o))).map((e=>e.element))},Ur=e=>{const t=e.grid,o=k(t.rows,h),n=k(t.columns,h);return E(o,(t=>qr((()=>j(n,(o=>kr(e,t,o).filter((e=>e.row===t)).fold(g([]),(e=>[e]))))),(e=>1===e.rowspan),(()=>kr(e,t,0)))))},Gr=(e,t)=>({row:e,y:t}),Kr=(e,t)=>({col:e,x:t}),Yr=e=>Go(e).left+Ho(e),Jr=e=>Go(e).left,Qr=(e,t)=>Kr(e,Jr(t)),Xr=(e,t)=>Kr(e,Yr(t)),Zr=e=>Go(e).top,es=(e,t)=>Gr(e,Zr(t)),ts=(e,t)=>Gr(e,Zr(t)+Po(t)),os=(e,t,o)=>{if(0===o.length)return[];const n=E(o.slice(1),((t,o)=>t.map((t=>e(o,t))))),r=o[o.length-1].map((e=>t(o.length-1,e)));return n.concat([r])},ns={delta:h,positions:e=>os(es,ts,e),edge:Zr},rs=Zo({delta:h,edge:Jr,positions:e=>os(Qr,Xr,e)},{delta:e=>-e,edge:Yr,positions:e=>os(Xr,Qr,e)}),ss={delta:(e,t)=>rs(t).delta(e,t),positions:(e,t)=>rs(t).positions(e,t),edge:e=>rs(e).edge(e)},ls=/(\d+(\.\d+)?)%/,as=/(\d+(\.\d+)?)px|em/,cs=It("col"),is=It("tr"),ms=(e,t,o)=>{const n=Vt(e).getOrThunk((()=>(e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return Le.fromDom(t)})(Ft(e))));return t(e)/o(n)*100},ds=(e,t)=>{Eo(e,"width",t+"px")},us=(e,t)=>{Eo(e,"width",t+"%")},fs=(e,t)=>{Eo(e,"height",t+"px")},gs=e=>{const t=(e=>{return Lo(t=e,"height",t.dom.offsetHeight)+"px";var t})(e);return t?((e,t,o,n)=>{const r=parseFloat(e);return be(e,"%")&&"table"!==Bt(t)?((e,t,o,n)=>{const r=ir(e).map((e=>{const n=o(e);return Math.floor(t/100*n)})).getOr(t);return n(e,r),r})(t,r,o,n):r})(t,e,jo,fs):jo(e)},hs=(e,t)=>zo(e,t).orThunk((()=>po(e,t).map((e=>e+"px")))),ps=e=>hs(e,"width"),bs=e=>ms(e,Fo,$o),ws=e=>{return cs(e)?Fo(e):Lo(t=e,"width",t.dom.offsetWidth);var t},vs=e=>is(e)?jo(e):((e,t,o)=>o(e)/Zn(e,"rowspan"))(e,0,gs),ys=(e,t,o)=>{Eo(e,"width",t+o)},xs=e=>ms(e,Fo,$o)+"%",Cs=g(ls),Ss=It("col"),Ts=e=>ps(e).getOrThunk((()=>ws(e)+"px")),Rs=e=>{return(t=e,hs(t,"height")).getOrThunk((()=>vs(e)+"px"));var t},Ds=(e,t,o,n,r,s)=>e.filter(n).fold((()=>s(((e,t)=>{if(t<0||t>=e.length-1)return C.none();const o=e[t].fold((()=>{const o=(e=>{const t=S.call(e,0);return t.reverse(),t})(e.slice(0,t));return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:0}))),n=e[t+1].fold((()=>{const o=e.slice(t+1);return V(o,((e,t)=>e.map((e=>({value:e,delta:t+1})))))}),(e=>C.some({value:e,delta:1})));return o.bind((e=>n.map((t=>{const o=t.delta+e.delta;return Math.abs(t.value-e.value)/o}))))})(o,t))),(e=>r(e))),Os=(e,t,o,n)=>{const r=Vr(e),s=zr(e)?(e=>E(_r(e),(e=>C.from(e.element))))(e):r,l=[C.some(ss.edge(t))].concat(E(ss.positions(r,t),(e=>e.map((e=>e.x))))),a=w(er);return E(s,((e,t)=>Ds(e,t,l,a,(e=>{if((e=>{const t=kt().browser,o=t.isChromium()||t.isFirefox();return!Ss(e)||o})(e))return o(e);{const e=null!=(s=r[t])?h(s):C.none();return Ds(e,t,l,a,(e=>n(C.some(Fo(e)))),n)}var s}),n)))},ks=e=>e.map((e=>e+"px")).getOr(""),Es=(e,t,o)=>Os(e,t,ws,(e=>e.getOrThunk(o.minCellWidth))),Ns=(e,t,o,n)=>{const r=Ur(e),s=E(e.all,(e=>C.some(e.element))),l=[C.some(ns.edge(t))].concat(E(ns.positions(r,t),(e=>e.map((e=>e.y)))));return E(s,((e,t)=>Ds(e,t,l,x,o,n)))},Bs=(e,t)=>()=>Oo(e)?t(e):parseFloat(zo(e,"width").getOr("0")),_s=e=>{const t=Bs(e,(e=>parseFloat(xs(e)))),o=Bs(e,Fo);return{width:t,pixelWidth:o,getWidths:(t,o)=>((e,t,o)=>Os(e,t,bs,(e=>e.fold((()=>o.minCellWidth()),(e=>e/o.pixelWidth()*100)))))(t,e,o),getCellDelta:e=>e/o()*100,singleColumnWidth:(e,t)=>[100-e],minCellWidth:()=>or()/o()*100,setElementWidth:us,adjustTableWidth:o=>{const n=t();us(e,n+o/100*n)},isRelative:!0,label:"percent"}},zs=e=>{const t=Bs(e,Fo);return{width:t,pixelWidth:t,getWidths:(t,o)=>Es(t,e,o),getCellDelta:h,singleColumnWidth:(e,t)=>[Math.max(or(),e+t)-e],minCellWidth:or,setElementWidth:ds,adjustTableWidth:o=>{const n=t()+o;ds(e,n)},isRelative:!1,label:"pixel"}},As=e=>ps(e).fold((()=>(e=>{const t=Bs(e,Fo),o=g(0);return{width:t,pixelWidth:t,getWidths:(t,o)=>Es(t,e,o),getCellDelta:o,singleColumnWidth:g([0]),minCellWidth:o,setElementWidth:f,adjustTableWidth:f,isRelative:!0,label:"none"}})(e)),(t=>((e,t)=>null!==Cs().exec(t)?_s(e):zs(e))(e,t))),Ws=zs,Ls=_s,Ms=(e,t,o,n)=>{o===n?bo(e,t):fo(e,t,o)},js=(e,t,o)=>{$(wn(e,t)).fold((()=>lo(e,o)),(e=>so(e,o)))},Ps=(e,t)=>{const o=[],n=[],r=e=>E(e,(e=>{e.isNew&&o.push(e.element);const t=e.element;return vo(t),N(e.cells,(e=>{e.isNew&&n.push(e.element),Ms(e.element,"colspan",e.colspan,1),Ms(e.element,"rowspan",e.rowspan,1),ao(t,e.element)})),t})),s=e=>j(e,(e=>E(e.cells,(e=>(Ms(e.element,"span",e.colspan,1),e.element))))),l=(t,o)=>{const n=((e,t)=>{const o=sn(e,t).getOrThunk((()=>{const o=Le.fromTag(t,Ft(e).dom);return"thead"===t?js(e,"caption,colgroup",o):"colgroup"===t?js(e,"caption",o):ao(e,o),o}));return vo(o),o})(e,o),l=("colgroup"===o?s:r)(t);mo(n,l)},a=(t,o)=>{t.length>0?l(t,o):(t=>{sn(e,t).each(yo)})(o)},c=[],i=[],m=[],d=[];return N(t,(e=>{switch(e.section){case"thead":c.push(e);break;case"tbody":i.push(e);break;case"tfoot":m.push(e);break;case"colgroup":d.push(e)}})),a(d,"colgroup"),a(c,"thead"),a(i,"tbody"),a(m,"tfoot"),{newRows:o,newCells:n}},Is=(e,t)=>{if(0===e.length)return 0;const o=e[0];return L(e,(e=>!t(o.element,e.element))).getOr(e.length)},Fs=(e,t)=>{const o=E(e,(e=>E(e.cells,y)));return E(e,((n,r)=>{const s=j(n.cells,((n,s)=>{if(!1===o[r][s]){const m=((e,t,o,n)=>{const r=((e,t)=>e[t])(e,t),s="colgroup"===r.section,l=Is(r.cells.slice(o),n),a=s?1:Is(((e,t)=>E(e,(e=>Ne(e,t))))(e.slice(t),o),n);return{colspan:l,rowspan:a}})(e,r,s,t);return((e,t,n,r)=>{for(let s=e;s<e+n;s++)for(let e=t;e<t+r;e++)o[s][e]=!0})(r,s,m.rowspan,m.colspan),[(l=n.element,a=m.rowspan,c=m.colspan,i=n.isNew,{element:l,rowspan:a,colspan:c,isNew:i})]}return[];var l,a,c,i}));return((e,t,o,n)=>({element:e,cells:t,section:o,isNew:n}))(n.element,s,n.section,n.isNew)}))},Hs=(e,t,o)=>{const n=[];N(e.colgroups,(r=>{const s=[];for(let n=0;n<e.grid.columns;n++){const r=Ar(e,n).map((e=>Te(e.element,o,!1))).getOrThunk((()=>Te(t.colGap(),!0,!1)));s.push(r)}n.push(Re(r.element,s,"colgroup",o))}));for(let r=0;r<e.grid.rows;r++){const s=[];for(let n=0;n<e.grid.columns;n++){const l=kr(e,r,n).map((e=>Te(e.element,o,e.isLocked))).getOrThunk((()=>Te(t.gap(),!0,!1)));s.push(l)}const l=e.all[r],a=Re(l.element,s,l.section,o);n.push(a)}return n},$s=e=>Fs(e,Je),Vs=(e,t)=>V(e.all,(e=>W(e.cells,(e=>Je(t,e.element))))),qs=(e,t,o)=>{const n=E(t.selection,(t=>lr(t).bind((t=>Vs(e,t))).filter(o))),r=me(n);return de(r.length>0,r)},Us=(e,t,o,n,r,s,l,a,c)=>{const i=Dr(s),m=C.from(null==c?void 0:c.section).getOrThunk($r);return t(i,l).map((t=>{const o=((e,t)=>Hs(e,t,!1))(i,a),n=e(o,t,Je,r(a),m),s=Cr(n.grid);return{info:t,grid:$s(n.grid),cursor:n.cursor,lockedColumns:s}})).bind((e=>{const t=Ps(s,e.grid),r=C.from(null==c?void 0:c.sizing).getOrThunk((()=>As(s))),l=C.from(null==c?void 0:c.resize).getOrThunk(Qn);return o(s,e.grid,e.info,{sizing:r,resize:l,section:m}),n(s),bo(s,yr),e.lockedColumns.length>0&&fo(s,yr,e.lockedColumns.join(",")),C.some({cursor:e.cursor,newRows:t.newRows,newCells:t.newCells})}))},Gs=(e,t)=>lr(t.element).bind((o=>Vs(e,o).map((e=>({...e,generators:t.generators,clipboard:t.clipboard}))))),Ks=(e,t)=>qs(e,t,x).map((e=>({cells:e,generators:t.generators,clipboard:t.clipboard}))),Ys=(e,t)=>qs(e,t,x),Js=(e,t)=>qs(e,t,(e=>!e.isLocked)),Qs=(e,t)=>P(t,(t=>((e,t)=>Vs(e,t).exists((e=>!e.isLocked)))(e,t))),Xs=(e,t)=>((e,t)=>t.mergable)(0,t).filter((t=>Qs(e,t.cells))),Zs=(e,t)=>((e,t)=>t.unmergable)(0,t).filter((t=>Qs(e,t))),el={...Z([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}])},tl=(e,t,o)=>{const n=((e,t)=>zr(e)?((e,t)=>{const o=_r(e);return E(o,((e,o)=>({element:e.element,width:t[o],colspan:e.colspan})))})(e,t):((e,t)=>{const o=Br(e);return E(o,(e=>{const o=((e,t,o)=>{let n=0;for(let r=e;r<t;r++)n+=void 0!==o[r]?o[r]:0;return n})(e.column,e.column+e.colspan,t);return{element:e.element,width:o,colspan:e.colspan}}))})(e,t))(e,t);N(n,(e=>{o.setElementWidth(e.element,e.width)}))},ol=(e,t,o,n,r)=>{const s=Dr(e),l=r.getCellDelta(t),a=r.getWidths(s,r),c=o===s.grid.columns-1,i=n.clampTableDelta(a,o,l,r.minCellWidth(),c),m=((e,t,o,n,r)=>{const s=e.slice(0),l=((e,t)=>0===e.length?el.none():1===e.length?el.only(0):0===t?el.left(0,1):t===e.length-1?el.right(t-1,t):t>0&&t<e.length-1?el.middle(t-1,t,t+1):el.none())(e,t),a=g(E(s,g(0)));return l.fold(a,(e=>n.singleColumnWidth(s[e],o)),((e,t)=>r.calcLeftEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)),((e,t,l)=>r.calcMiddleDeltas(s,e,t,l,o,n.minCellWidth(),n.isRelative)),((e,t)=>r.calcRightEdgeDeltas(s,e,t,o,n.minCellWidth(),n.isRelative)))})(a,o,i,r,n),d=E(m,((e,t)=>e+a[t]));tl(s,d,r),n.resizeTable(r.adjustTableWidth,i,c)},nl=(e,t,o)=>{const n=Dr(e),r=((e,t)=>Ns(e,t,vs,(e=>e.getOrThunk(nr))))(n,e),s=E(r,((e,n)=>o===n?Math.max(t+e,nr()):e)),l=((e,t)=>E(e.all,((e,o)=>({element:e.element,height:t[o]}))))(n,s);N(l,(e=>{fs(e.element,e.height)})),N(Br(n),(e=>{(e=>{Ao(e,"height")})(e.element)}));const a=z(s,((e,t)=>e+t),0);fs(e,a)},rl=(e,t)=>{if(!er(e)){const o=(e=>ps(e).bind((e=>{return t=e,o=["fixed","relative","empty"],C.from(Vn.exec(t)).bind((e=>{const t=Number(e[1]),n=e[2];return((e,t)=>O(t,(t=>O($n[t],(t=>e===t)))))(n,o)?C.some({value:t,unit:n}):C.none()}));var t,o})))(e);o.each((o=>{const n=o.value/2;ys(e,n,o.unit),ys(t,n,o.unit)}))}},sl=(e,t,o)=>{const n=Xn(e,t,1);1===o||n<=1?bo(e,t):fo(e,t,Math.min(o,n))},ll=(e,t)=>o=>{const n=o.column+o.colspan-1,r=o.column;return n>=e&&r<t},al=(e,t,o)=>{const n=e[o].element,r=Le.fromTag("td");ao(r,Le.fromTag("br")),(t?ao:lo)(n,r)},cl=It("col"),il=It("colgroup"),ml=e=>"tr"===Bt(e)||il(e),dl=e=>({element:e,colspan:Xn(e,"colspan",1),rowspan:Xn(e,"rowspan",1)}),ul=e=>po(e,"scope").map((e=>e.substr(0,3))),fl=(e,t=dl)=>{const o=o=>{if(ml(o))return il((r={element:o}).element)?e.colgroup(r):e.row(r);{const r=o,s=(t=>cl(t.element)?e.col(t):e.cell(t))(t(r));return n=C.some({item:r,replacement:s}),s}var r};let n=C.none();return{getOrInit:(e,t)=>n.fold((()=>o(e)),(n=>t(e,n.item)?n.replacement:o(e)))}},gl=e=>t=>{const o=[],n=n=>{const r="td"===e?{scope:null}:{},s=t.replace(n,e,r);return o.push({item:n,sub:s}),s};return{replaceOrInit:(e,t)=>{if(ml(e)||cl(e))return e;{const r=e;return((e,t)=>W(o,(o=>t(o.item,e))))(r,t).fold((()=>n(r)),(o=>t(e,o.item)?o.sub:n(r)))}}}},hl=e=>({unmerge:t=>{const o=ul(t);return o.each((e=>fo(t,"scope",e))),()=>{const n=e.cell({element:t,colspan:1,rowspan:1});return Ao(n,"width"),Ao(t,"width"),o.each((e=>fo(n,"scope",e))),n}},merge:e=>(Ao(e[0],"width"),(()=>{const t=me(E(e,ul));if(0===t.length)return C.none();{const e=t[0],o=["row","col"];return O(t,(t=>t!==e&&D(o,t)))?C.none():C.from(e)}})().fold((()=>bo(e[0],"scope")),(t=>fo(e[0],"scope",t+"group"))),g(e[0]))}),pl=Z([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),bl=(e,t,o)=>{const n=o.substring(0,o.length-e.length),r=parseFloat(n);return n===r.toString()?t(r):pl.invalid(o)},wl={...pl,from:e=>be(e,"%")?bl("%",pl.percent,e):be(e,"px")?bl("px",pl.pixels,e):pl.invalid(e)},vl=(e,t,o)=>{const n=wl.from(o),r=P(e,(e=>"0px"===e))?((e,t)=>{const o=e.fold((()=>g("")),(e=>g(e/t+"px")),(()=>g(100/t+"%")));return k(t,o)})(n,e.length):((e,t,o)=>e.fold((()=>t),(e=>((e,t,o)=>{const n=o/t;return E(e,(e=>wl.from(e).fold((()=>e),(e=>e*n+"px"),(e=>e/100*o+"px"))))})(t,o,e)),(e=>((e,t)=>E(e,(e=>wl.from(e).fold((()=>e),(e=>e/t*100+"%"),(e=>e+"%")))))(t,o))))(n,e,t);return Cl(r)},yl=(e,t)=>0===e.length?t:z(e,((e,t)=>wl.from(t).fold(g(0),h,h)+e),0),xl=(e,t)=>wl.from(e).fold(g(e),(e=>e+t+"px"),(e=>e+t+"%")),Cl=e=>{if(0===e.length)return e;const t=z(e,((e,t)=>{const o=wl.from(t).fold((()=>({value:t,remainder:0})),(e=>(e=>{const t=Math.floor(e);return{value:t+"px",remainder:e-t}})(e)),(e=>({value:e+"%",remainder:0})));return{output:[o.value].concat(e.output),remainder:e.remainder+o.remainder}}),{output:[],remainder:0}),o=t.output;return o.slice(0,o.length-1).concat([xl(o[o.length-1],Math.round(t.remainder))])},Sl=wl.from,Tl=(e,t,o)=>{const n=Dr(e),r=n.all,s=Br(n),l=_r(n);t.each((t=>{const o=Sl(t).fold(g("px"),g("px"),g("%")),r=Fo(e),a=((e,t)=>Os(e,t,Ts,ks))(n,e),c=vl(a,r,t);zr(n)?((e,t,o)=>{N(t,((t,n)=>{const r=yl([e[n]],or());Eo(t.element,"width",r+o)}))})(c,l,o):((e,t,o)=>{N(t,(t=>{const n=e.slice(t.column,t.colspan+t.column),r=yl(n,or());Eo(t.element,"width",r+o)}))})(c,s,o),Eo(e,"width",t)})),o.each((t=>{const o=jo(e),l=((e,t)=>Ns(e,t,Rs,ks))(n,e);((e,t,o)=>{N(o,(e=>{Ao(e.element,"height")})),N(t,((t,o)=>{Eo(t.element,"height",e[o])}))})(vl(l,o,t),r,s),Eo(e,"height",t)}))},Rl=e=>ps(e).exists((e=>ls.test(e))),Dl=e=>ps(e).exists((e=>as.test(e))),Ol=e=>ps(e).isNone();var kl=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],El=()=>({up:g({selector:rn,closest:an,predicate:on,all:qt}),down:g({selector:vn,predicate:pn}),styles:g({get:Bo,getRaw:zo,set:Eo,remove:Ao}),attrs:g({get:ho,set:fo,remove:bo,copyTo:(e,t)=>{const o=wo(e);go(t,o)}}),insert:g({before:ro,after:so,afterAll:io,append:ao,appendAll:mo,prepend:lo,wrap:co}),remove:g({unwrap:xo,remove:yo}),create:g({nu:Le.fromTag,clone:e=>Le.fromDom(e.dom.cloneNode(!1)),text:Le.fromText}),query:g({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:Ut,nextSibling:Gt}),property:g({children:Kt,name:Bt,parent:$t,document:e=>Ht(e).dom,isText:Mt,isComment:At,isElement:Lt,isSpecial:e=>{const t=Bt(e);return D(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>Lt(e)?po(e,"lang"):C.none(),getText:Jo,setText:Xo,isBoundary:e=>!!Lt(e)&&("body"===Bt(e)||D(kl,Bt(e))),isEmptyTag:e=>!!Lt(e)&&D(["br","img","hr","input"],Bt(e)),isNonEditable:e=>Lt(e)&&"false"===ho(e,"contenteditable")}),eq:Je,is:Xe});const Nl=(e,t)=>({item:e,mode:t}),Bl=(e,t,o,n=_l)=>e.property().parent(t).map((e=>Nl(e,n))),_l=(e,t,o,n=zl)=>o.sibling(e,t).map((e=>Nl(e,n))),zl=(e,t,o,n=zl)=>{const r=e.property().children(t);return o.first(r).map((e=>Nl(e,n)))},Al=[{current:Bl,next:_l,fallback:C.none()},{current:_l,next:zl,fallback:C.some(Bl)},{current:zl,next:zl,fallback:C.some(_l)}],Wl=(e,t,o,n,r=Al)=>W(r,(e=>e.current===o)).bind((o=>o.current(e,t,n,o.next).orThunk((()=>o.fallback.bind((o=>Wl(e,t,o,n))))))),Ll=(e,t,o,n,r,s)=>Wl(e,t,n,r).bind((t=>s(t.item)?C.none():o(t.item)?C.some(t.item):Ll(e,t.item,o,t.mode,r,s))),Ml=(e,t)=>({element:e,offset:t}),jl=(e,t,o)=>e.property().isText(t)&&0===e.property().getText(t).trim().length||e.property().isComment(t)?o(t).bind((t=>jl(e,t,o).orThunk((()=>C.some(t))))):C.none(),Pl=(e,t)=>e.property().isText(t)?e.property().getText(t).length:e.property().children(t).length,Il=(e,t)=>{const o=jl(e,t,e.query().prevSibling).getOr(t);if(e.property().isText(o))return Ml(o,Pl(e,o));const n=e.property().children(o);return n.length>0?Il(e,n[n.length-1]):Ml(o,Pl(e,o))},Fl=Il,Hl=El(),$l=e=>t=>0===e.property().children(t).length,Vl=(e,t,o,n)=>Ll(e,t,o,_l,{sibling:(e,t)=>e.query().prevSibling(t),first:e=>e.length>0?C.some(e[e.length-1]):C.none()},n),ql=(e,t,o,n)=>Ll(e,t,o,_l,{sibling:(e,t)=>e.query().nextSibling(t),first:e=>e.length>0?C.some(e[0]):C.none()},n),Ul=El(),Gl=(e,t)=>((e,t,o)=>Vl(e,t,$l(e),o))(Ul,e,t),Kl=(e,t)=>((e,t,o)=>ql(e,t,$l(e),o))(Ul,e,t),Yl=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],Jl=(e,t,o,n)=>{const r=t(e,o);return z(n,((o,n)=>{const r=t(e,n);return Ql(e,o,r)}),r)},Ql=(e,t,o)=>t.bind((t=>o.filter(b(e.eq,t)))),Xl=El(),Zl=(e,t)=>((e,t,o)=>o.length>0?((e,t,o,n)=>n(e,t,o[0],o.slice(1)))(e,t,o,Jl):C.none())(Xl,((t,o)=>e(o)),t),ea=El(),ta=e=>((e,t)=>{const o=e.property().name(t);return D(Yl,o)})(ea,e),oa=e=>((e,t)=>{const o=e.property().name(t);return D(["ol","ul"],o)})(ea,e),na=e=>{const t=It("br"),o=e=>Tn(e).bind((o=>{const n=Gt(o).map((e=>!!ta(e)||!!((e,t)=>D(["br","img","hr","input"],e.property().name(t)))(ea,e)&&"img"!==Bt(e))).getOr(!1);return $t(o).map((r=>{return!0===n||("li"===Bt(s=r)||on(s,oa).isSome())||t(o)||ta(r)&&!Je(e,r)?[]:[Le.fromTag("br")];var s}))})).getOr([]),n=(()=>{const n=j(e,(e=>{const n=Kt(e);return(e=>P(e,(e=>t(e)||Mt(e)&&0===Jo(e).trim().length)))(n)?[]:n.concat(o(e))}));return 0===n.length?[Le.fromTag("br")]:n})();vo(e[0]),mo(e[0],n)},ra=e=>{bo(e,"width"),bo(e,"height")},sa=e=>{const t=xs(e);Tl(e,C.some(t),C.none()),ra(e)},la=e=>{const t=(e=>Fo(e)+"px")(e);Tl(e,C.some(t),C.none()),ra(e)},aa=e=>{Ao(e,"width");const t=cr(e),o=t.length>0?t:ar(e);N(o,(e=>{Ao(e,"width"),ra(e)})),ra(e)},ca={scope:["row","col"]},ia=e=>()=>{const t=Le.fromTag("td",e.dom);return ao(t,Le.fromTag("br",e.dom)),t},ma=e=>()=>Le.fromTag("col",e.dom),da=e=>()=>Le.fromTag("colgroup",e.dom),ua=e=>()=>Le.fromTag("tr",e.dom),fa=(e,t,o)=>{const n=((e,t)=>{const o=Ro(e,t),n=Kt(To(e));return mo(o,n),o})(e,t);return G(o,((e,t)=>{null===e?bo(n,t):fo(n,t,e)})),n},ga=e=>e,ha=(e,t,o)=>{const n=(e,t)=>{((e,t)=>{const o=e.dom,n=t.dom;Do(o)&&Do(n)&&(n.style.cssText=o.style.cssText)})(e.element,t),Ao(t,"height"),1!==e.colspan&&Ao(t,"width")};return{col:o=>{const r=Le.fromTag(Bt(o.element),t.dom);return n(o,r),e(o.element,r),r},colgroup:da(t),row:ua(t),cell:r=>{const s=Le.fromTag(Bt(r.element),t.dom),l=o.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),a=l.length>0?((e,t,o)=>Sn(e).map((n=>{const r=o.join(","),s=bn(n,r,(t=>Je(t,e)));return z(s,((e,t)=>{const o=So(t);return ao(e,o),o}),t)})).getOr(t))(r.element,s,l):s;return ao(a,Le.fromTag("br")),n(r,s),((e,t)=>{G(ca,((o,n)=>po(e,n).filter((e=>D(o,e))).each((e=>fo(t,n,e)))))})(r.element,s),e(r.element,s),s},replace:fa,colGap:ma(t),gap:ia(t)}},pa=e=>({col:ma(e),colgroup:da(e),row:ua(e),cell:ia(e),replace:ga,colGap:ma(e),gap:ia(e)}),ba=e=>Dr(e).grid,wa=(e,t,o,n)=>{const r=ze(e).rows;let s=!0;for(let e=0;e<r.length;e++)for(let l=0;l<_e(r[0]);l++){const a=r[e],c=Ne(a,l),i=o(c.element,t);i&&!s?ke(a,l,Te(n(),!0,c.isLocked)):i&&(s=!1)}return e},va=(e,t)=>({rowDelta:0,colDelta:_e(e[0])-_e(t[0])}),ya=(e,t)=>({rowDelta:e.length-t.length,colDelta:0}),xa=(e,t,o,n)=>{const r="colgroup"===t.section?o.col:o.cell;return k(e,(e=>Te(r(),!0,n(e))))},Ca=(e,t,o,n)=>{const r=e[e.length-1];return e.concat(k(t,(()=>{const e="colgroup"===r.section?o.colgroup:o.row,t=Ae(r,e,h),s=xa(t.cells.length,t,o,(e=>X(n,e.toString())));return Ee(t,s)})))},Sa=(e,t,o,n)=>E(e,(e=>{const r=xa(t,e,o,y);return De(e,n,r)})),Ta=(e,t,o)=>{const n=t.colDelta<0?Sa:h,r=t.rowDelta<0?Ca:h,s=Cr(e),l=_e(e[0]),a=O(s,(e=>e===l-1)),c=n(e,Math.abs(t.colDelta),o,a?l-1:l),i=Cr(c);return r(c,Math.abs(t.rowDelta),o,I(i,x))},Ra=(e,t,o,n)=>{const r=b(n,Ne(e[t],o).element),s=e[t];return e.length>1&&_e(s)>1&&(o>0&&r(Be(s,o-1))||o<s.cells.length-1&&r(Be(s,o+1))||t>0&&r(Be(e[t-1],o))||t<e.length-1&&r(Be(e[t+1],o)))},Da=(e,t,o)=>_(o,(o=>o>=e.column&&o<=_e(t[0])+e.column)),Oa=(e,t,o,n,r)=>{((e,t,o,n)=>{t>0&&t<e[0].cells.length&&N(e,(e=>{const r=e.cells[t-1];let s=0;const l=n();for(;e.cells.length>t+s&&o(r.element,e.cells[t+s].element);)ke(e,t+s,Te(l,!0,e.cells[t+s].isLocked)),s++}))})(t,e,r,n.cell);const s=ya(o,t),l=Ta(o,s,n),a=ya(t,l),c=Ta(t,a,n);return E(c,((t,o)=>De(t,e,l[o].cells)))},ka=(e,t,o,n,r)=>{((e,t,o,n)=>{const r=ze(e).rows;if(t>0&&t<r.length){const e=((e,t)=>A(e,((e,o)=>O(e,(e=>t(e.element,o.element)))?e:e.concat([o])),[]))(r[t-1].cells,o);N(e,(e=>{let s=C.none();for(let l=t;l<r.length;l++)for(let t=0;t<_e(r[0]);t++){const a=r[l],c=Ne(a,t);o(c.element,e.element)&&(s.isNone()&&(s=C.some(n())),s.each((e=>{ke(a,t,Te(e,!0,c.isLocked))})))}}))}})(t,e,r,n.cell);const s=Cr(t),l=va(t,o),a={...l,colDelta:l.colDelta-s.length},c=Ta(t,a,n),{cols:i,rows:m}=ze(c),d=Cr(c),u=va(o,t),f={...u,colDelta:u.colDelta+d.length},g=(p=n,b=d,E(o,(e=>A(b,((t,o)=>{const n=xa(1,e,p,x)[0];return Oe(t,o,n)}),e)))),h=Ta(g,f,n);var p,b;return[...i,...m.slice(0,e),...h,...m.slice(e,m.length)]},Ea=(e,t,o,n,r)=>{const{rows:s,cols:l}=ze(e),a=s.slice(0,t),c=s.slice(t);return[...l,...a,((e,t,o,n)=>Ae(e,(e=>n(e,o)),t))(s[o],((e,o)=>t>0&&t<s.length&&n(Be(s[t-1],o),Be(s[t],o))?Ne(s[t],o):Te(r(e.element,n),!0,e.isLocked)),n,r),...c]},Na=(e,t,o,n,r)=>E(e,(e=>{const s=t>0&&t<_e(e)&&n(Be(e,t-1),Be(e,t)),l=((e,t,o,n,r,s,l)=>{if("colgroup"!==o&&n)return Ne(e,t);{const t=Ne(e,r);return Te(l(t.element,s),!0,!1)}})(e,t,e.section,s,o,n,r);return Oe(e,t,l)})),Ba=(e,t,o,n)=>((e,t,o,n)=>void 0!==Be(e[t],o)&&t>0&&n(Be(e[t-1],o),Be(e[t],o)))(e,t,o,n)||((e,t,o)=>t>0&&o(Be(e,t-1),Be(e,t)))(e[t],o,n),_a=(e,t,o,n)=>{const r=e=>(e=>"row"===e?(e=>Zn(e,"rowspan")>1)(t):er(t))(e)?`${e}group`:e;return e?ur(t)?r(o):null:n&&ur(t)?r("row"===o?"col":"row"):null},za=(e,t,o)=>Te(o(e.element,t),!0,e.isLocked),Aa=(e,t,o,n,r,s,l)=>E(e,((e,a)=>(e=>{const c=e.cells,i=E(c,((e,c)=>{if((e=>O(t,(t=>o(e.element,t.element))))(e)){const t=l(e,a,c)?r(e,o,n):e;return s(t,a,c).each((e=>{var o,n;o=t.element,n={scope:C.from(e)},G(n,((e,t)=>{e.fold((()=>{bo(o,t)}),(e=>{uo(o.dom,t,e)}))}))})),t}return e}));return Re(e.element,i,e.section,e.isNew)})(e))),Wa=(e,t,o)=>j(e,((n,r)=>Ba(e,r,t,o)?[]:[Ne(n,t)])),La=(e,t,o,n,r)=>{const s=ze(e).rows,l=j(t,(e=>Wa(s,e,n))),a=E(s,(e=>fr(e.cells))),c=((e,t)=>P(t,h)&&fr(e)?x:(e,o,n)=>!("th"===Bt(e.element)&&t[o]))(l,a),i=((e,t)=>(o,n)=>C.some(_a(e,o.element,"row",t[n])))(o,a);return Aa(e,l,n,r,za,i,c)},Ma=(e,t,o,n)=>{const r=ze(e).rows,s=E(t,(e=>Ne(r[e.row],e.column)));return Aa(e,s,o,n,za,C.none,x)},ja=e=>A(e,((e,t)=>O(e,(e=>e.column===t.column))?e:e.concat([t])),[]).sort(((e,t)=>e.column-t.column)),Pa=e=>gn(e,!0),Ia=e=>{0===ar(e).length&&yo(e)},Fa=(e,t)=>({grid:e,cursor:t}),Ha=(e,t,o)=>{const n=((e,t,o)=>{var n,r;const s=ze(e).rows;return C.from(null===(r=null===(n=s[t])||void 0===n?void 0:n.cells[o])||void 0===r?void 0:r.element).filter(Pa).orThunk((()=>(e=>V(e,(e=>V(e.cells,(e=>{const t=e.element;return de(Pa(t),t)})))))(s)))})(e,t,o);return Fa(e,n)},$a=e=>A(e,((e,t)=>O(e,(e=>e.row===t.row))?e:e.concat([t])),[]).sort(((e,t)=>e.row-t.row)),Va=(e,t,o,n)=>{const r=t[0].row,s=$a(t),l=z(s,((e,t)=>({grid:Ea(e.grid,r,t.row+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Ha(l,r,t[0].column)},qa=(e,t,o,n)=>{const r=$a(t),s=r[r.length-1],l=s.row+s.rowspan,a=z(r,((e,t)=>Ea(e,l,t.row,o,n.getOrInit)),e);return Ha(a,l,t[0].column)},Ua=(e,t,o,n)=>{const r=t.details,s=ja(r),l=s[0].column,a=z(s,((e,t)=>({grid:Na(e.grid,l,t.column+e.delta,o,n.getOrInit),delta:e.delta+1})),{grid:e,delta:0}).grid;return Ha(a,r[0].row,l)},Ga=(e,t,o,n)=>{const r=t.details,s=r[r.length-1],l=s.column+s.colspan,a=ja(r),c=z(a,((e,t)=>Na(e,l,t.column,o,n.getOrInit)),e);return Ha(c,r[0].row,l)},Ka=(e,t,o,n)=>{const r=ja(t),s=E(r,(e=>e.column)),l=La(e,s,!0,o,n.replaceOrInit);return Ha(l,t[0].row,t[0].column)},Ya=(e,t,o,n)=>{const r=Ma(e,t,o,n.replaceOrInit);return Ha(r,t[0].row,t[0].column)},Ja=(e,t,o,n)=>{const r=ja(t),s=E(r,(e=>e.column)),l=La(e,s,!1,o,n.replaceOrInit);return Ha(l,t[0].row,t[0].column)},Qa=(e,t,o,n)=>{const r=Ma(e,t,o,n.replaceOrInit);return Ha(r,t[0].row,t[0].column)},Xa=(e,t)=>(o,n,r,s,l)=>{const a=$a(n),c=E(a,(e=>e.row)),i=((e,t,o,n,r,s,l)=>{const{cols:a,rows:c}=ze(e),i=c[t[0]],m=j(t,(e=>((e,t,o)=>{const n=e[t];return j(n.cells,((n,r)=>Ba(e,t,r,o)?[]:[n]))})(c,e,r))),d=E(i.cells,((e,t)=>fr(Wa(c,t,r)))),u=[...c];N(t,(e=>{u[e]=l.transformRow(c[e],o)}));const f=[...a,...u],g=((e,t)=>P(t,h)&&fr(e.cells)?x:(e,o,n)=>!("th"===Bt(e.element)&&t[n]))(i,d),p=((e,t)=>(o,n,r)=>C.some(_a(e,o.element,"col",t[r])))(n,d);return Aa(f,m,r,s,l.transformCell,p,g)})(o,c,e,t,r,s.replaceOrInit,l);return Ha(i,n[0].row,n[0].column)},Za=Xa("thead",!0),ec=Xa("tbody",!1),tc=Xa("tfoot",!1),oc=(e,t,o,n)=>{const r=ja(t.details),s=((e,t)=>j(e,(e=>{const o=e.cells,n=z(t,((e,t)=>t>=0&&t<e.length?e.slice(0,t).concat(e.slice(t+1)):e),o);return n.length>0?[Re(e.element,n,e.section,e.isNew)]:[]})))(e,E(r,(e=>e.column))),l=s.length>0?s[0].cells.length-1:0;return Ha(s,r[0].row,Math.min(r[0].column,l))},nc=(e,t,o,n)=>{const r=$a(t),s=((e,t,o)=>{const{rows:n,cols:r}=ze(e);return[...r,...n.slice(0,t),...n.slice(o+1)]})(e,r[0].row,r[r.length-1].row),l=Math.max(ze(s).rows.length-1,0);return Ha(s,Math.min(t[0].row,l),t[0].column)},rc=(e,t,o,n)=>{const r=t.cells;na(r);const s=((e,t,o,n)=>{const r=ze(e).rows;if(0===r.length)return e;for(let e=t.startRow;e<=t.finishRow;e++)for(let o=t.startCol;o<=t.finishCol;o++){const t=r[e],s=Ne(t,o).isLocked;ke(t,o,Te(n(),!1,s))}return e})(e,t.bounds,0,n.merge(r));return Fa(s,C.from(r[0]))},sc=(e,t,o,n)=>{const r=z(t,((e,t)=>wa(e,t,o,n.unmerge(t))),e);return Fa(r,C.from(t[0]))},lc=(e,t,o,n)=>{const r=((e,t)=>{const o=Dr(e);return Hs(o,t,!0)})(t.clipboard,t.generators);var s,l;return((e,t,o,n,r)=>{const s=Cr(t),l=((e,t,o)=>{const n=_e(t[0]),r=ze(t).cols.length+e.row,s=k(n-e.column,(t=>t+e.column));return{row:r,column:W(s,(e=>P(o,(t=>t!==e)))).getOr(n-1)}})(e,t,s),a=ze(o).rows,c=Da(l,a,s),i=((e,t,o)=>{if(e.row>=t.length||e.column>_e(t[0]))return le.error("invalid start address out of table bounds, row: "+e.row+", column: "+e.column);const n=t.slice(e.row),r=n[0].cells.slice(e.column),s=_e(o[0]),l=o.length;return le.value({rowDelta:n.length-l,colDelta:r.length-s})})(l,t,a);return i.map((e=>{const o={...e,colDelta:e.colDelta-c.length},s=Ta(t,o,n),i=Cr(s),m=Da(l,a,i);return((e,t,o,n,r,s)=>{const l=e.row,a=e.column,c=l+o.length,i=a+_e(o[0])+s.length,m=I(s,x);for(let e=l;e<c;e++){let s=0;for(let c=a;c<i;c++){if(m[c]){s++;continue}Ra(t,e,c,r)&&wa(t,Be(t[e],c),r,n.cell);const i=c-a-s,d=Ne(o[e-l],i),u=d.element,f=n.replace(u);ke(t[e],c,Te(f,!0,d.isLocked))}}return t})(l,s,a,n,r,m)}))})((s=t.row,l=t.column,{row:s,column:l}),e,r,t.generators,o).fold((()=>Fa(e,C.some(t.element))),(e=>Ha(e,t.row,t.column)))},ac=(e,t,o)=>{const n=((e,t)=>br(e,(()=>t)))(e,o.section),r=Or(n);return Hs(r,t,!0)},cc=(e,t,o,n)=>{const r=ze(e).rows,s=t.cells[0].column,l=r[t.cells[0].row],a=ac(t.clipboard,t.generators,l),c=Oa(s,e,a,t.generators,o);return Ha(c,t.cells[0].row,t.cells[0].column)},ic=(e,t,o,n)=>{const r=ze(e).rows,s=t.cells[t.cells.length-1].column+t.cells[t.cells.length-1].colspan,l=r[t.cells[0].row],a=ac(t.clipboard,t.generators,l),c=Oa(s,e,a,t.generators,o);return Ha(c,t.cells[0].row,s)},mc=(e,t,o,n)=>{const r=ze(e).rows,s=t.cells[0].row,l=r[s],a=ac(t.clipboard,t.generators,l),c=ka(s,e,a,t.generators,o);return Ha(c,t.cells[0].row,t.cells[0].column)},dc=(e,t,o,n)=>{const r=ze(e).rows,s=t.cells[t.cells.length-1].row+t.cells[t.cells.length-1].rowspan,l=r[t.cells[0].row],a=ac(t.clipboard,t.generators,l),c=ka(s,e,a,t.generators,o);return Ha(c,s,t.cells[0].column)},uc=(e,t,o,n)=>((e,t,o,n)=>{const r=Or(t),s=n.getWidths(r,n);tl(r,s,n)})(0,t,0,n.sizing),fc=(e,t,o,n)=>((e,t,o,n,r)=>{const s=Or(t),l=n.getWidths(s,n),a=n.pixelWidth(),{newSizes:c,delta:i}=r.calcRedestributedWidths(l,a,o.pixelDelta,n.isRelative);tl(s,c,n),n.adjustTableWidth(i)})(0,t,o,n.sizing,n.resize),gc=(e,t)=>O(t,(e=>0===e.column&&e.isLocked)),hc=(e,t)=>O(t,(t=>t.column+t.colspan>=e.grid.columns&&t.isLocked)),pc=(e,t)=>{const o=Vr(e),n=ja(t);return A(n,((e,t)=>e+o[t.column].map(Ho).getOr(0)),0)},bc=e=>(t,o)=>Ys(t,o).filter((o=>!(e?gc:hc)(t,o))).map((e=>({details:e,pixelDelta:pc(t,e)}))),wc=(e,t)=>Js(e,t).map((t=>({details:t,pixelDelta:-pc(e,t)}))),vc=e=>(t,o)=>Ks(t,o).filter((o=>!(e?gc:hc)(t,o.cells))),yc=gl("th"),xc=gl("td"),Cc=(e,t,o,n)=>Us(Va,Ys,f,f,fl,e,t,o,n),Sc=(e,t,o,n)=>Us(qa,Ys,f,f,fl,e,t,o,n),Tc=(e,t,o,n)=>Us(Ua,bc(!0),fc,f,fl,e,t,o,n),Rc=(e,t,o,n)=>Us(Ga,bc(!1),fc,f,fl,e,t,o,n),Dc=(e,t,o,n)=>Us(oc,wc,fc,Ia,fl,e,t,o,n),Oc=(e,t,o,n)=>Us(nc,Ys,f,Ia,fl,e,t,o,n),kc=(e,t,o,n)=>Us(Ka,Js,f,f,yc,e,t,o,n),Ec=(e,t,o,n)=>Us(Ja,Js,f,f,xc,e,t,o,n),Nc=(e,t,o,n)=>Us(Za,Ys,f,f,yc,e,t,o,n),Bc=(e,t,o,n)=>Us(ec,Ys,f,f,xc,e,t,o,n),_c=(e,t,o,n)=>Us(tc,Ys,f,f,xc,e,t,o,n),zc=(e,t,o,n)=>Us(Ya,Js,f,f,yc,e,t,o,n),Ac=(e,t,o,n)=>Us(Qa,Js,f,f,xc,e,t,o,n),Wc=(e,t,o,n)=>Us(rc,Xs,uc,f,hl,e,t,o,n),Lc=(e,t,o,n)=>Us(sc,Zs,uc,f,hl,e,t,o,n),Mc=(e,t,o,n)=>Us(lc,Gs,uc,f,fl,e,t,o,n),jc=(e,t,o,n)=>Us(cc,vc(!0),f,f,fl,e,t,o,n),Pc=(e,t,o,n)=>Us(ic,vc(!1),f,f,fl,e,t,o,n),Ic=(e,t,o,n)=>Us(mc,Ks,f,f,fl,e,t,o,n),Fc=(e,t,o,n)=>Us(dc,Ks,f,f,fl,e,t,o,n),Hc=(e,t)=>{const o=Dr(e);return Ys(o,t).bind((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=M(E(o.all,(e=>_(e.cells,(e=>e.column>=n&&e.column<r)))));return pr(s)})).getOr("")},$c=(e,t)=>{const o=Dr(e);return Ys(o,t).bind(pr).getOr("")},Vc=(e,t)=>{const o=Dr(e);return Ys(o,t).bind((e=>{const t=e[e.length-1],n=e[0].row,r=t.row+t.rowspan;return(e=>{const t=E(e,(e=>hr(e).type)),o=D(t,"header"),n=D(t,"footer");if(o||n){const e=D(t,"body");return!o||e||n?o||e||!n?C.none():C.some("footer"):C.some("header")}return C.some("body")})(o.all.slice(n,r))})).getOr("")},qc=(e,t)=>{const o=t.column,n=t.column+t.colspan-1,r=t.row,s=t.row+t.rowspan-1;return o<=e.finishCol&&n>=e.startCol&&r<=e.finishRow&&s>=e.startRow},Uc=(e,t)=>t.column>=e.startCol&&t.column+t.colspan-1<=e.finishCol&&t.row>=e.startRow&&t.row+t.rowspan-1<=e.finishRow,Gc=(e,t,o)=>{const n=Er(e,t,Je),r=Er(e,o,Je);return n.bind((e=>r.map((t=>{return o=e,n=t,{startRow:Math.min(o.row,n.row),startCol:Math.min(o.column,n.column),finishRow:Math.max(o.row+o.rowspan-1,n.row+n.rowspan-1),finishCol:Math.max(o.column+o.colspan-1,n.column+n.colspan-1)};var o,n}))))},Kc=(e,t,o)=>Gc(e,t,o).map((t=>{const o=Nr(e,b(qc,t));return E(o,(e=>e.element))})),Yc=(e,t)=>Er(e,t,((e,t)=>Qe(t,e))).map((e=>e.element)),Jc=(e,t,o)=>{const n=Xc(e);return Kc(n,t,o)},Qc=(e,t,o,n,r)=>{const s=Xc(e),l=Je(e,o)?C.some(t):Yc(s,t),a=Je(e,r)?C.some(n):Yc(s,n);return l.bind((e=>a.bind((t=>Kc(s,e,t)))))},Xc=Dr,Zc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},ei=(e,t,o,n)=>k(e,(e=>((e,t,o,n)=>{const r=Le.fromTag("tr");for(let s=0;s<e;s++){const e=Le.fromTag(n<t||s<o?"th":"td");s<o&&fo(e,"scope","row"),n<t&&fo(e,"scope","col"),ao(e,Le.fromTag("br")),ao(r,e)}return r})(t,o,n,e))),ti=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=_(t,(t=>t!==e))},trigger:(...o)=>{const n={};N(e,((e,t)=>{n[e]=o[t]})),N(t,(e=>{e(n)}))}}},oi=e=>({registry:K(e,(e=>({bind:e.bind,unbind:e.unbind}))),trigger:K(e,(e=>e.trigger))}),ni=ne(["compare","extract","mutate","sink"]),ri=ne(["element","start","stop","destroy"]),si=ne(["forceDrop","drop","move","delayDrop"]),li=()=>{const e=(()=>{const e=oi({move:ti(["info"])});return{onEvent:f,reset:f,events:e.registry}})(),t=(()=>{let e=C.none();const t=oi({move:ti(["info"])});return{onEvent:(o,n)=>{n.extract(o).each((o=>{const r=((t,o)=>{const n=e.map((e=>t.compare(e,o)));return e=C.some(o),n})(n,o);r.each((e=>{t.trigger.move(e)}))}))},reset:()=>{e=C.none()},events:t.registry}})();let o=e;return{on:()=>{o.reset(),o=t},off:()=>{o.reset(),o=e},isOn:()=>o===t,onEvent:(e,t)=>{o.onEvent(e,t)},events:t.events}},ai=ce("ephox-dragster").resolve;var ci=ni({compare:(e,t)=>qo(t.left-e.left,t.top-e.top),extract:e=>C.some(qo(e.x,e.y)),sink:(e,t)=>{const o=(e=>{const t={layerClass:ai("blocker"),...e},o=Le.fromTag("div");return fo(o,"role","presentation"),No(o,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),dn(o,ai("blocker")),dn(o,t.layerClass),{element:g(o),destroy:()=>{yo(o)}}})(t),n=oo(o.element(),"mousedown",e.forceDrop),r=oo(o.element(),"mouseup",e.drop),s=oo(o.element(),"mousemove",e.move),l=oo(o.element(),"mouseout",e.delayDrop);return ri({element:o.element,start:e=>{ao(e,o.element())},stop:()=>{yo(o.element())},destroy:()=>{o.destroy(),r.unbind(),s.unbind(),l.unbind(),n.unbind()}})},mutate:(e,t)=>{e.mutate(t.left,t.top)}});const ii=ce("ephox-snooker").resolve,mi=ii("resizer-bar"),di=ii("resizer-rows"),ui=ii("resizer-cols"),fi=e=>{const t=vn(e.parent(),"."+mi);N(t,yo)},gi=(e,t,o)=>{const n=e.origin();N(t,(t=>{t.each((t=>{const r=o(n,t);dn(r,mi),ao(e.parent(),r)}))}))},hi=(e,t,o,n,r)=>{const s=Go(o),l=t.isResizable,a=n.length>0?ns.positions(n,o):[],c=a.length>0?((e,t)=>j(e.all,((e,o)=>t(e.element)?[o]:[])))(e,l):[];((e,t,o,n)=>{gi(e,t,((e,t)=>{const r=((e,t,o,n)=>{const r=Le.fromTag("div");return No(r,{position:"absolute",left:t+"px",top:o-3.5+"px",height:"7px",width:n+"px"}),go(r,{"data-mce-bogus":"all","data-row":e,role:"presentation"}),r})(t.row,o.left-e.left,t.y-e.top,n);return dn(r,di),r}))})(t,_(a,((e,t)=>O(c,(e=>t===e)))),s,Ho(o));const i=r.length>0?ss.positions(r,o):[],m=i.length>0?((e,t)=>{const o=[];return k(e.grid.columns,(n=>{Ar(e,n).map((e=>e.element)).forall(t)&&o.push(n)})),_(o,(o=>{const n=Nr(e,(e=>e.column===o));return P(n,(e=>t(e.element)))}))})(e,l):[];((e,t,o,n)=>{gi(e,t,((e,t)=>{const r=((e,t,o,n,r)=>{const s=Le.fromTag("div");return No(s,{position:"absolute",left:t-3.5+"px",top:o+"px",height:r+"px",width:"7px"}),go(s,{"data-mce-bogus":"all","data-column":e,role:"presentation"}),s})(t.col,t.x-e.left,o.top-e.top,0,n);return dn(r,ui),r}))})(t,_(i,((e,t)=>O(m,(e=>t===e)))),s,Po(o))},pi=(e,t)=>{if(fi(e),e.isResizable(t)){const o=Dr(t),n=Ur(o),r=Vr(o);hi(o,e,t,n,r)}},bi=(e,t)=>{const o=vn(e.parent(),"."+mi);N(o,t)},wi=e=>{bi(e,(e=>{Eo(e,"display","none")}))},vi=e=>{bi(e,(e=>{Eo(e,"display","block")}))},yi=ii("resizer-bar-dragging"),xi=e=>{const t=(()=>{const e=oi({drag:ti(["xDelta","yDelta","target"])});let t=C.none();const o=(()=>{const e=oi({drag:ti(["xDelta","yDelta"])});return{mutate:(t,o)=>{e.trigger.drag(t,o)},events:e.registry}})();return o.events.drag.bind((o=>{t.each((t=>{e.trigger.drag(o.xDelta,o.yDelta,t)}))})),{assign:e=>{t=C.some(e)},get:()=>t,mutate:o.mutate,events:e.registry}})(),o=((e,t={})=>{var o;return((e,t,o)=>{let n=!1;const r=oi({start:ti([]),stop:ti([])}),s=li(),l=()=>{m.stop(),s.isOn()&&(s.off(),r.trigger.stop())},c=(e=>{let t=null;const o=()=>{a(t)||(clearTimeout(t),t=null)};return{cancel:o,throttle:(...n)=>{o(),t=setTimeout((()=>{t=null,e.apply(null,n)}),200)}}})(l);s.events.move.bind((o=>{t.mutate(e,o.info)}));const i=e=>(...t)=>{n&&e.apply(null,t)},m=t.sink(si({forceDrop:l,drop:i(l),move:i((e=>{c.cancel(),s.onEvent(e,t)})),delayDrop:i(c.throttle)}),o);return{element:m.element,go:e=>{m.start(e),s.on(),r.trigger.start()},on:()=>{n=!0},off:()=>{n=!1},isActive:()=>n,destroy:()=>{m.destroy()},events:r.registry}})(e,null!==(o=t.mode)&&void 0!==o?o:ci,t)})(t,{});let n=C.none();const r=(e,t)=>C.from(ho(e,t));t.events.drag.bind((e=>{r(e.target,"data-row").each((t=>{const o=tr(e.target,"top");Eo(e.target,"top",o+e.yDelta+"px")})),r(e.target,"data-column").each((t=>{const o=tr(e.target,"left");Eo(e.target,"left",o+e.xDelta+"px")}))}));const s=(e,t)=>tr(e,t)-Xn(e,"data-initial-"+t,0);o.events.stop.bind((()=>{t.get().each((t=>{n.each((o=>{r(t,"data-row").each((e=>{const n=s(t,"top");bo(t,"data-initial-top"),d.trigger.adjustHeight(o,n,parseInt(e,10))})),r(t,"data-column").each((e=>{const n=s(t,"left");bo(t,"data-initial-left"),d.trigger.adjustWidth(o,n,parseInt(e,10))})),pi(e,o)}))}))}));const l=(n,r)=>{d.trigger.startAdjust(),t.assign(n),fo(n,"data-initial-"+r,tr(n,r)),dn(n,yi),Eo(n,"opacity","0.2"),o.go(e.dragContainer())},c=oo(e.parent(),"mousedown",(e=>{var t;t=e.target,un(t,di)&&l(e.target,"top"),(e=>un(e,ui))(e.target)&&l(e.target,"left")})),i=t=>Je(t,e.view()),m=oo(e.view(),"mouseover",(t=>{var r;(r=t.target,an(r,"table",i).filter(gn)).fold((()=>{Oo(t.target)&&!(e=>un(e,"ephox-snooker-resizer-bar")||un(e,"ephox-dragster-blocker"))(t.target)&&fi(e)}),(t=>{o.isActive()&&(n=C.some(t),pi(e,t))}))})),d=oi({adjustHeight:ti(["table","delta","row"]),adjustWidth:ti(["table","delta","column"]),startAdjust:ti([])});return{destroy:()=>{c.unbind(),m.unbind(),o.destroy(),fi(e)},refresh:t=>{pi(e,t)},on:o.on,off:o.off,hideBars:b(wi,e),showBars:b(vi,e),events:d.registry}},Ci=e=>t=>t.options.get(e),Si="100%",Ti=e=>{var t;const o=e.dom,n=null!==(t=o.getParent(e.selection.getStart(),o.isBlock))&&void 0!==t?t:e.getBody();return $o(Le.fromDom(n))+"px"},Ri=e=>C.from(e.options.get("table_clone_elements")),Di=Ci("table_header_type"),Oi=Ci("table_column_resizing"),ki=e=>"preservetable"===Oi(e),Ei=e=>"resizetable"===Oi(e),Ni=Ci("table_sizing_mode"),Bi=e=>"relative"===Ni(e),_i=e=>"fixed"===Ni(e),zi=e=>"responsive"===Ni(e),Ai=Ci("table_resize_bars"),Wi=Ci("table_style_by_css"),Li=Ci("table_merge_content_on_paste"),Mi=e=>{const t=e.options,o=t.get("table_default_attributes");return t.isSet("table_default_attributes")?o:((e,t)=>zi(e)||Wi(e)?t:_i(e)?{...t,width:Ti(e)}:{...t,width:Si})(e,o)},ji=Ci("table_use_colgroups"),Pi=e=>Le.fromDom(e.getBody()),Ii=e=>t=>Je(t,Pi(e)),Fi=e=>{bo(e,"data-mce-style");const t=e=>bo(e,"data-mce-style");N(ar(e),t),N(cr(e),t),N(mr(e),t)},Hi=e=>Le.fromDom(e.selection.getStart()),$i=e=>e.getBoundingClientRect().width,Vi=e=>e.getBoundingClientRect().height,qi=e=>(t,o)=>{const n=t.dom.getStyle(o,e)||t.dom.getAttrib(o,e);return C.from(n).filter(ve)},Ui=qi("width"),Gi=qi("height"),Ki=e=>nn(e,It("table")).exists(gn),Yi=e=>rn(e,"table"),Ji=(e,t,o)=>{const n=e=>t=>void 0!==o&&o(t)||Je(t,e);return Je(e,t)?C.some({boxes:C.some([e]),start:e,finish:t}):Yi(e).bind((r=>Yi(t).bind((s=>{if(Je(r,s))return C.some({boxes:Jc(r,e,t),start:e,finish:t});if(Qe(r,s)){const o=bn(t,"td,th",n(r)),l=o.length>0?o[o.length-1]:t;return C.some({boxes:Qc(r,e,r,t,s),start:e,finish:l})}if(Qe(s,r)){const o=bn(e,"td,th",n(s)),l=o.length>0?o[o.length-1]:e;return C.some({boxes:Qc(s,e,r,t,s),start:e,finish:l})}return((e,t)=>((e,t,o,n=y)=>{const r=[t].concat(e.up().all(t)),s=[o].concat(e.up().all(o)),l=e=>L(e,n).fold((()=>e),(t=>e.slice(0,t+1))),a=l(r),c=l(s),i=W(a,(t=>O(c,((e,t)=>b(e.eq,t))(e,t))));return{firstpath:a,secondpath:c,shared:i}})(Xl,e,t,void 0))(e,t).shared.bind((l=>an(l,"table",o).bind((o=>{const l=bn(t,"td,th",n(o)),a=l.length>0?l[l.length-1]:t,c=bn(e,"td,th",n(o)),i=c.length>0?c[c.length-1]:e;return C.some({boxes:Qc(o,e,r,t,s),start:i,finish:a})}))))}))))},Qi=(e,t)=>{const o=vn(e,t);return o.length>0?C.some(o):C.none()},Xi=(e,t,o)=>ln(e,t).bind((t=>ln(e,o).bind((e=>Zl(Yi,[t,e]).map((o=>({first:t,last:e,table:o}))))))),Zi=(e,t,o,n,r)=>((e,t)=>W(e,(e=>Ge(e,t))))(e,r).bind((e=>((e,t,o)=>ir(e).bind((n=>((e,t,o,n)=>Er(e,t,Je).bind((t=>{const r=o>0?t.row+t.rowspan-1:t.row,s=n>0?t.column+t.colspan-1:t.column;return kr(e,r+o,s+n).map((e=>e.element))})))(Xc(n),e,t,o))))(e,t,o).bind((e=>((e,t)=>rn(e,"table").bind((o=>ln(o,t).bind((t=>Ji(t,e).bind((e=>e.boxes.map((t=>({boxes:t,start:e.start,finish:e.finish}))))))))))(e,n))))),em=(e,t)=>Qi(e,t),tm=(e,t,o)=>Xi(e,t,o).bind((t=>{const o=t=>Je(e,t),n="thead,tfoot,tbody,table",r=rn(t.first,n,o),s=rn(t.last,n,o);return r.bind((e=>s.bind((o=>Je(e,o)?((e,t,o)=>((e,t,o)=>Gc(e,t,o).bind((t=>((e,t)=>{let o=!0;const n=b(Uc,t);for(let r=t.startRow;r<=t.finishRow;r++)for(let s=t.startCol;s<=t.finishCol;s++)o=o&&kr(e,r,s).exists(n);return o?C.some(t):C.none()})(e,t))))(Xc(e),t,o))(t.table,t.first,t.last):C.none()))))})),om=h,nm=e=>{const t=(e,t)=>po(e,t).exists((e=>parseInt(e,10)>1));return e.length>0&&P(e,(e=>t(e,"rowspan")||t(e,"colspan")))?C.some(e):C.none()},rm=(e,t,o)=>t.length<=1?C.none():tm(e,o.firstSelectedSelector,o.lastSelectedSelector).map((e=>({bounds:e,cells:t}))),sm=(e,t)=>({selection:e,kill:t}),lm=()=>({tag:"none"}),am=e=>({tag:"multiple",elements:e}),cm=e=>({tag:"single",element:e}),im=(e,t,o,n)=>({start:kn.on(e,t),finish:kn.on(o,n)}),mm=(e,t)=>{const o=Ue(e,t);return Dn(Le.fromDom(o.startContainer),o.startOffset,Le.fromDom(o.endContainer),o.endOffset)},dm=im,um=(e,t,o,n,r)=>Je(o,n)?C.none():Ji(o,n,t).bind((t=>{const n=t.boxes.getOr([]);return n.length>1?(r(e,n,t.start,t.finish),C.some(sm(C.some(dm(o,0,o,yn(o))),!0))):C.none()})),fm=Z([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),gm=e=>an(e,"tr"),hm={...fm,verify:(e,t,o,n,r,s,l)=>an(n,"td,th",l).bind((o=>an(t,"td,th",l).map((t=>Je(o,t)?Je(n,o)&&yn(o)===r?s(t):fm.none("in same cell"):Zl(gm,[o,t]).fold((()=>((e,t,o)=>{const n=e.getRect(t),r=e.getRect(o);return r.right>n.left&&r.left<n.right})(e,t,o)?fm.success():s(t)),(e=>s(t))))))).getOr(fm.none("default")),cata:(e,t,o,n,r)=>e.fold(t,o,n,r)},pm=It("br"),bm=(e,t,o)=>t(e,o).bind((e=>Mt(e)&&0===Jo(e).trim().length?bm(e,t,o):C.some(e))),wm=(e,t,o,n)=>((e,t)=>Yt(e,t).filter(pm).orThunk((()=>Yt(e,t-1).filter(pm))))(t,o).bind((t=>n.traverse(t).fold((()=>bm(t,n.gather,e).map(n.relative)),(e=>(e=>$t(e).bind((t=>{const o=Kt(t);return((e,t)=>L(e,b(Je,t)))(o,e).map((n=>((e,t,o,n)=>({parent:e,children:t,element:o,index:n}))(t,o,e,n)))})))(e).map((e=>kn.on(e.parent,e.index))))))),vm=(e,t)=>({left:e.left,top:e.top+t,right:e.right,bottom:e.bottom+t}),ym=(e,t)=>({left:e.left,top:e.top-t,right:e.right,bottom:e.bottom-t}),xm=(e,t,o)=>({left:e.left+t,top:e.top+o,right:e.right+t,bottom:e.bottom+o}),Cm=e=>({left:e.left,top:e.top,right:e.right,bottom:e.bottom}),Sm=(e,t)=>C.some(e.getRect(t)),Tm=(e,t,o)=>Lt(t)?Sm(e,t).map(Cm):Mt(t)?((e,t,o)=>o>=0&&o<yn(t)?e.getRangedRect(t,o,t,o+1):o>0?e.getRangedRect(t,o-1,t,o):C.none())(e,t,o).map(Cm):C.none(),Rm=(e,t)=>Lt(t)?Sm(e,t).map(Cm):Mt(t)?e.getRangedRect(t,0,t,yn(t)).map(Cm):C.none(),Dm=Z([{none:[]},{retry:["caret"]}]),Om=(e,t,o)=>nn(t,ta).fold(y,(t=>Rm(e,t).exists((e=>((e,t)=>e.left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right)(o,e))))),km={point:e=>e.bottom,adjuster:(e,t,o,n,r)=>{const s=vm(r,5);return Math.abs(o.bottom-n.bottom)<1||o.top>r.bottom?Dm.retry(s):o.top===r.bottom?Dm.retry(vm(r,1)):Om(e,t,r)?Dm.retry(xm(s,5,0)):Dm.none()},move:vm,gather:Kl},Em=(e,t,o,n,r)=>0===r?C.some(n):((e,t,o)=>e.elementFromPoint(t,o).filter((e=>"table"===Bt(e))).isSome())(e,n.left,t.point(n))?((e,t,o,n,r)=>Em(e,t,o,t.move(n,5),r))(e,t,o,n,r-1):e.situsFromPoint(n.left,t.point(n)).bind((s=>s.start.fold(C.none,(s=>Rm(e,s).bind((l=>t.adjuster(e,s,l,o,n).fold(C.none,(n=>Em(e,t,o,n,r-1))))).orThunk((()=>C.some(n)))),C.none))),Nm=(e,t,o)=>{const n=e.move(o,5),r=Em(t,e,o,n,100).getOr(n);return((e,t,o)=>e.point(t)>o.getInnerHeight()?C.some(e.point(t)-o.getInnerHeight()):e.point(t)<0?C.some(-e.point(t)):C.none())(e,r,t).fold((()=>t.situsFromPoint(r.left,e.point(r))),(o=>(t.scrollBy(0,o),t.situsFromPoint(r.left,e.point(r)-o))))},Bm={tryUp:b(Nm,{point:e=>e.top,adjuster:(e,t,o,n,r)=>{const s=ym(r,5);return Math.abs(o.top-n.top)<1||o.bottom<r.top?Dm.retry(s):o.bottom===r.top?Dm.retry(ym(r,1)):Om(e,t,r)?Dm.retry(xm(s,5,0)):Dm.none()},move:ym,gather:Gl}),tryDown:b(Nm,km),getJumpSize:g(5)},_m=(e,t,o)=>e.getSelection().bind((n=>((e,t,o,n)=>{const r=pm(t)?((e,t,o)=>o.traverse(t).orThunk((()=>bm(t,o.gather,e))).map(o.relative))(e,t,n):wm(e,t,o,n);return r.map((e=>({start:e,finish:e})))})(t,n.finish,n.foffset,o).fold((()=>C.some(Ml(n.finish,n.foffset))),(r=>{const s=e.fromSitus(r);return l=hm.verify(e,n.finish,n.foffset,s.finish,s.foffset,o.failure,t),hm.cata(l,(e=>C.none()),(()=>C.none()),(e=>C.some(Ml(e,0))),(e=>C.some(Ml(e,yn(e)))));var l})))),zm=(e,t,o,n,r,s)=>0===s?C.none():Lm(e,t,o,n,r).bind((l=>{const a=e.fromSitus(l),c=hm.verify(e,o,n,a.finish,a.foffset,r.failure,t);return hm.cata(c,(()=>C.none()),(()=>C.some(l)),(l=>Je(o,l)&&0===n?Am(e,o,n,ym,r):zm(e,t,l,0,r,s-1)),(l=>Je(o,l)&&n===yn(l)?Am(e,o,n,vm,r):zm(e,t,l,yn(l),r,s-1)))})),Am=(e,t,o,n,r)=>Tm(e,t,o).bind((t=>Wm(e,r,n(t,Bm.getJumpSize())))),Wm=(e,t,o)=>{const n=kt().browser;return n.isChromium()||n.isSafari()||n.isFirefox()?t.retry(e,o):C.none()},Lm=(e,t,o,n,r)=>Tm(e,o,n).bind((t=>Wm(e,r,t))),Mm=(e,t,o,n,r)=>an(n,"td,th",t).bind((n=>an(n,"table",t).bind((s=>((e,t)=>on(e,(e=>$t(e).exists((e=>Je(e,t)))),void 0).isSome())(r,s)?((e,t,o)=>_m(e,t,o).bind((n=>zm(e,t,n.element,n.offset,o,20).map(e.fromSitus))))(e,t,o).bind((e=>an(e.finish,"td,th",t).map((t=>({start:n,finish:t,range:e}))))):C.none())))),jm=(e,t,o,n,r,s)=>s(n,t).orThunk((()=>Mm(e,t,o,n,r).map((e=>{const t=e.range;return sm(C.some(dm(t.start,t.soffset,t.finish,t.foffset)),!0)})))),Pm=(e,t)=>an(e,"tr",t).bind((e=>an(e,"table",t).bind((o=>{const n=vn(o,"tr");return Je(e,n[0])?((e,t,o)=>Vl(Ul,e,(e=>Tn(e).isSome()),o))(o,0,t).map((e=>{const t=yn(e);return sm(C.some(dm(e,t,e,t)),!0)})):C.none()})))),Im=(e,t)=>an(e,"tr",t).bind((e=>an(e,"table",t).bind((o=>{const n=vn(o,"tr");return Je(e,n[n.length-1])?((e,t,o)=>ql(Ul,e,(e=>Sn(e).isSome()),o))(o,0,t).map((e=>sm(C.some(dm(e,0,e,0)),!0))):C.none()})))),Fm=(e,t,o,n,r,s,l)=>Mm(e,o,n,r,s).bind((e=>um(t,o,e.start,e.finish,l))),Hm=(e,t)=>an(e,"td,th",t),$m=e=>Vt(e).exists(gn),Vm={traverse:Gt,gather:Kl,relative:kn.before,retry:Bm.tryDown,failure:hm.failedDown},qm={traverse:Ut,gather:Gl,relative:kn.before,retry:Bm.tryUp,failure:hm.failedUp},Um=e=>t=>t===e,Gm=Um(38),Km=Um(40),Ym=e=>e>=37&&e<=40,Jm={isBackward:Um(37),isForward:Um(39)},Qm={isBackward:Um(39),isForward:Um(37)},Xm=e=>({elementFromPoint:(t,o)=>Le.fromPoint(Le.fromDom(e.document),t,o),getRect:e=>e.dom.getBoundingClientRect(),getRangedRect:(t,o,n,r)=>{const s=Nn.exact(t,o,n,r);return((e,t)=>(e=>{const t=e.getClientRects(),o=t.length>0?t[0]:e.getBoundingClientRect();return o.width>0||o.height>0?C.some(o).map(He):C.none()})(Ue(e,t)))(e,s)},getSelection:()=>Fn(e).map((t=>mm(e,t))),fromSitus:t=>{const o=Nn.relative(t.start,t.finish);return mm(e,o)},situsFromPoint:(t,o)=>Hn(e,t,o).map((e=>im(e.start,e.soffset,e.finish,e.foffset))),clearSelection:()=>{(e=>{_n(e).each((e=>e.removeAllRanges()))})(e)},collapseSelection:(t=!1)=>{Fn(e).each((o=>o.fold((e=>e.collapse(t)),((o,n)=>{const r=t?o:n;Mn(e,r,r)}),((o,n,r,s)=>{const l=t?o:r,a=t?n:s;Ln(e,l,a,l,a)}))))},setSelection:t=>{Ln(e,t.start,t.soffset,t.finish,t.foffset)},setRelativeSelection:(t,o)=>{Mn(e,t,o)},selectNode:t=>{In(e,t,!1)},selectContents:t=>{In(e,t)},getInnerHeight:()=>e.innerHeight,getScrollY:()=>(e=>{const t=void 0!==e?e.dom:document,o=t.body.scrollLeft||t.documentElement.scrollLeft,n=t.body.scrollTop||t.documentElement.scrollTop;return qo(o,n)})(Le.fromDom(e.document)).top,scrollBy:(t,o)=>{((e,t,o)=>{const n=(void 0!==o?o.dom:document).defaultView;n&&n.scrollBy(e,t)})(t,o,Le.fromDom(e.document))}}),Zm=(e,t)=>({rows:e,cols:t}),ed=e=>nn(e,Wt).exists(gn),td=(e,t)=>ed(e)||ed(t),od="data-mce-selected",nd="data-mce-first-selected",rd="data-mce-last-selected",sd="["+od+"]",ld={selected:od,selectedSelector:"td["+od+"],th["+od+"]",firstSelected:nd,firstSelectedSelector:"td["+nd+"],th["+nd+"]",lastSelected:rd,lastSelectedSelector:"td["+rd+"],th["+rd+"]"},ad=(e,t,o)=>({element:o,mergable:rm(t,e,ld),unmergable:nm(e),selection:om(e)}),cd=e=>(t,o)=>{const n=Bt(t),r="col"===n||"colgroup"===n?ir(s=t).bind((e=>em(e,ld.firstSelectedSelector))).fold(g(s),(e=>e[0])):t;var s;return an(r,e,o)},id=cd("th,td,caption"),md=cd("th,td"),dd=e=>{return t=e.model.table.getSelectedCells(),E(t,Le.fromDom);var t},ud=(e,t)=>{e.on("BeforeGetContent",(t=>{const o=o=>{t.preventDefault(),(e=>ir(e[0]).map((e=>{const t=((e,t)=>{const o=e=>Ge(e.element,t),n=To(e),r=vr(n),s=As(e),l=Or(r),a=((e,t)=>{const o=e.grid.columns;let n=e.grid.rows,r=o,s=0,l=0;const a=[],c=[];return G(e.access,(e=>{if(a.push(e),t(e)){c.push(e);const t=e.row,o=t+e.rowspan-1,a=e.column,i=a+e.colspan-1;t<n?n=t:o>s&&(s=o),a<r?r=a:i>l&&(l=i)}})),((e,t,o,n,r,s)=>({minRow:e,minCol:t,maxRow:o,maxCol:n,allCells:r,selectedCells:s}))(n,r,s,l,a,c)})(l,o),c="th:not("+t+"),td:not("+t+")",i=sr(n,"th,td",(e=>Ge(e,c)));N(i,yo),((e,t,o,n)=>{const r=_(e,(e=>"colgroup"!==e.section)),s=t.grid.columns,l=t.grid.rows;for(let e=0;e<l;e++){let l=!1;for(let a=0;a<s;a++)e<o.minRow||e>o.maxRow||a<o.minCol||a>o.maxCol||(kr(t,e,a).filter(n).isNone()?al(r,l,e):l=!0)}})(r,l,a,o);const m=((e,t,o,n)=>{if(0===n.minCol&&t.grid.columns===n.maxCol+1)return 0;const r=Es(t,e,o),s=A(r,((e,t)=>e+t),0),l=A(r.slice(n.minCol,n.maxCol+1),((e,t)=>e+t),0),a=l/s*o.pixelWidth()-o.pixelWidth();return o.getCellDelta(a)})(e,Dr(e),s,a);return((e,t,o,n)=>{G(o.columns,(e=>{(e.column<t.minCol||e.column>t.maxCol)&&yo(e.element)}));const r=_(rr(e,"tr"),(e=>0===e.dom.childElementCount));N(r,yo),t.minCol!==t.maxCol&&t.minRow!==t.maxRow||N(rr(e,"th,td"),(e=>{bo(e,"rowspan"),bo(e,"colspan")})),bo(e,yr),bo(e,"data-snooker-col-series"),As(e).adjustTableWidth(n)})(n,a,l,m),n})(e,sd);return Fi(t),[t]})))(o).each((o=>{const n="text"===t.format?((e,t)=>{const o=e.getDoc(),n=Qt(Le.fromDom(e.getBody())),r=Le.fromTag("div",o);fo(r,"data-mce-bogus","all"),No(r,{position:"fixed",left:"-9999999px",top:"0",overflow:"hidden",opacity:"0"});const s=(e=>Jt(e)?e:Le.fromDom(Ht(e).dom.body))(n);mo(r,t),ao(s,r);const l=r.dom.innerText;return yo(r),l})(e,o):((e,t)=>E(t,(t=>e.selection.serializer.serialize(t.dom,{}))).join(""))(e,o);t.content=n}))};if(!0===t.selection){const t=(e=>_(dd(e),(e=>Ge(e,ld.selectedSelector))))(e);t.length>=1&&o(t)}})),e.on("BeforeSetContent",(o=>{if(!0===o.selection&&!0===o.paste){const n=dd(e);H(n).each((n=>{ir(n).each((r=>{const s=_((e=>{const t=document.createElement("div");return t.innerHTML=e,Kt(Le.fromDom(t))})(o.content),(e=>"meta"!==Bt(e))),l=It("table");if(Li(e)&&1===s.length&&l(s[0])){o.preventDefault();const l=Le.fromDom(e.getDoc()),a=pa(l),c=((e,t,o)=>({element:e,clipboard:t,generators:o}))(n,s[0],a);t.pasteCells(r,c).each((()=>{e.focus()}))}}))}))}}))},fd=(e,t)=>e.dispatch("NewRow",{node:t}),gd=(e,t)=>e.dispatch("NewCell",{node:t}),hd=(e,t,o)=>{e.dispatch("TableModified",{...o,table:t})},pd={structure:!1,style:!0},bd={structure:!0,style:!1},wd={structure:!0,style:!0},vd=(e,t)=>Bi(e)?Ls(t):_i(e)?Ws(t):As(t),yd=(e,t,o)=>{const n=e=>"table"===Bt(Pi(e)),r=Ri(e),s=Ei(e)?f:rl,l=t=>{switch(Di(e)){case"section":return Ir();case"sectionCells":return Fr();case"cells":return Hr();default:return((e,t)=>{var o;switch((o=Dr(e),V(o.all,(e=>{const t=hr(e);return"header"===t.type?C.from(t.subType):C.none()}))).getOr(t)){case"section":return Mr();case"sectionCells":return jr();case"cells":return Pr()}})(t,"section")}},a=(n,s,a,c)=>(i,m,d=!1)=>{Fi(i);const u=Le.fromDom(e.getDoc()),f=ha(a,u,r),g={sizing:vd(e,i),resize:Ei(e)?Jn():Qn(),section:l(i)};return s(i)?n(i,m,f,g).bind((n=>{t.refresh(i.dom),N(n.newRows,(t=>{fd(e,t.dom)})),N(n.newCells,(t=>{gd(e,t.dom)}));const r=((t,n)=>n.cursor.fold((()=>{const n=ar(t);return H(n).filter(Oo).map((n=>{o.clearSelectedCells(t.dom);const r=e.dom.createRng();return r.selectNode(n.dom),e.selection.setRng(r),fo(n,"data-mce-selected","1"),r}))}),(n=>{const r=Fl(Hl,n),s=e.dom.createRng();return s.setStart(r.element.dom,r.offset),s.setEnd(r.element.dom,r.offset),e.selection.setRng(s),o.clearSelectedCells(t.dom),C.some(s)})))(i,n);return Oo(i)&&(Fi(i),d||hd(e,i.dom,c)),r.map((e=>({rng:e,effect:c})))})):C.none()},c=a(Oc,(t=>!n(e)||ba(t).rows>1),f,bd),i=a(Dc,(t=>!n(e)||ba(t).columns>1),f,bd);return{deleteRow:c,deleteColumn:i,insertRowsBefore:a(Cc,x,f,bd),insertRowsAfter:a(Sc,x,f,bd),insertColumnsBefore:a(Tc,x,s,bd),insertColumnsAfter:a(Rc,x,s,bd),mergeCells:a(Wc,x,f,bd),unmergeCells:a(Lc,x,f,bd),pasteColsBefore:a(jc,x,f,bd),pasteColsAfter:a(Pc,x,f,bd),pasteRowsBefore:a(Ic,x,f,bd),pasteRowsAfter:a(Fc,x,f,bd),pasteCells:a(Mc,x,f,wd),makeCellsHeader:a(zc,x,f,bd),unmakeCellsHeader:a(Ac,x,f,bd),makeColumnsHeader:a(kc,x,f,bd),unmakeColumnsHeader:a(Ec,x,f,bd),makeRowsHeader:a(Nc,x,f,bd),makeRowsBody:a(Bc,x,f,bd),makeRowsFooter:a(_c,x,f,bd),getTableRowType:Vc,getTableCellType:$c,getTableColType:Hc}},xd=(e,t)=>{e.selection.select(t.dom,!0),e.selection.collapse(!0)},Cd=(e,t,o,n,s)=>{const l=(e=>{const t=e.options,o=t.get("table_default_styles");return t.isSet("table_default_styles")?o:((e,t)=>zi(e)||!Wi(e)?t:_i(e)?{...t,width:Ti(e)}:{...t,width:Si})(e,o)})(e),a={styles:l,attributes:Mi(e),colGroups:ji(e)};return e.undoManager.ignore((()=>{const r=((e,t,o,n,r,s=Zc)=>{const l=Le.fromTag("table"),a="cells"!==r;No(l,s.styles),go(l,s.attributes),s.colGroups&&ao(l,(e=>{const t=Le.fromTag("colgroup");return k(e,(()=>ao(t,Le.fromTag("col")))),t})(t));const c=Math.min(e,o);if(a&&o>0){const e=Le.fromTag("thead");ao(l,e);const s=ei(o,t,"sectionCells"===r?c:0,n);mo(e,s)}const i=Le.fromTag("tbody");ao(l,i);const m=ei(a?e-c:e,t,a?0:o,n);return mo(i,m),l})(o,t,s,n,Di(e),a);fo(r,"data-mce-id","__mce");const l=(e=>{const t=Le.fromTag("div"),o=Le.fromDom(e.dom.cloneNode(!0));return ao(t,o),(e=>e.dom.innerHTML)(t)})(r);e.insertContent(l),e.addVisual()})),ln(Pi(e),'table[data-mce-id="__mce"]').map((t=>(_i(e)?la(t):zi(e)?aa(t):(Bi(e)||(e=>r(e)&&-1!==e.indexOf("%"))(l.width))&&sa(t),Fi(t),bo(t,"data-mce-id"),((e,t)=>{N(vn(t,"tr"),(t=>{fd(e,t.dom),N(vn(t,"th,td"),(t=>{gd(e,t.dom)}))}))})(e,t),((e,t)=>{ln(t,"td,th").each(b(xd,e))})(e,t),t.dom))).getOrNull()};var Sd=tinymce.util.Tools.resolve("tinymce.FakeClipboard");const Td="x-tinymce/dom-table-",Rd=Td+"rows",Dd=Td+"columns",Od=e=>{const t=Sd.FakeClipboardItem(e);Sd.write([t])},kd=e=>{var t;const o=null!==(t=Sd.read())&&void 0!==t?t:[];return V(o,(t=>C.from(t.getType(e))))},Ed=e=>{kd(e).isSome()&&Sd.clear()},Nd=e=>{e.fold(_d,(e=>Od({[Rd]:e})))},Bd=()=>kd(Rd),_d=()=>Ed(Rd),zd=e=>{e.fold(Wd,(e=>Od({[Dd]:e})))},Ad=()=>kd(Dd),Wd=()=>Ed(Dd),Ld=e=>id(Hi(e),Ii(e)).filter(Ki),Md=(e,t)=>{const o=Ii(e),n=e=>ir(e,o),l=t=>(e=>md(Hi(e),Ii(e)).filter(Ki))(e).bind((e=>n(e).map((o=>t(o,e))))),a=t=>{e.focus()},c=(t,o=!1)=>l(((n,r)=>{const s=ad(dd(e),n,r);t(n,s,o).each(a)})),i=()=>l(((t,o)=>((e,t,o)=>{const n=Dr(e);return Ys(n,t).bind((e=>{const t=Hs(n,o,!1),r=ze(t).rows.slice(e[0].row,e[e.length-1].row+e[e.length-1].rowspan),s=j(r,(e=>{const t=_(e.cells,(e=>!e.isLocked));return t.length>0?[{...e,cells:t}]:[]})),l=$s(s);return de(l.length>0,l)})).map((e=>E(e,(e=>{const t=So(e.element);return N(e.cells,(e=>{const o=To(e.element);Ms(o,"colspan",e.colspan,1),Ms(o,"rowspan",e.rowspan,1),ao(t,o)})),t}))))})(t,ad(dd(e),t,o),ha(f,Le.fromDom(e.getDoc()),C.none())))),m=()=>l(((t,o)=>((e,t)=>{const o=Dr(e);return Js(o,t).map((e=>{const t=e[e.length-1],n=e[0].column,r=t.column+t.colspan,s=((e,t,o)=>{if(zr(e)){const n=_(_r(e),ll(t,o)),r=E(n,(e=>{const n=To(e.element);return sl(n,"span",o-t),n})),s=Le.fromTag("colgroup");return mo(s,r),[s]}return[]})(o,n,r),l=((e,t,o)=>E(e.all,(e=>{const n=_(e.cells,ll(t,o)),r=E(n,(e=>{const n=To(e.element);return sl(n,"colspan",o-t),n})),s=Le.fromTag("tr");return mo(s,r),s})))(o,n,r);return[...s,...l]}))})(t,ad(dd(e),t,o)))),d=(t,o)=>o().each((o=>{const n=E(o,(e=>To(e)));l(((o,r)=>{const s=pa(Le.fromDom(e.getDoc())),l=((e,t,o,n)=>({selection:om(e),clipboard:o,generators:n}))(dd(e),0,n,s);t(o,l).each(a)}))})),g=e=>(t,o)=>((e,t)=>X(e,t)?C.from(e[t]):C.none())(o,"type").each((t=>{c(e(t),o.no_events)}));G({mceTableSplitCells:()=>c(t.unmergeCells),mceTableMergeCells:()=>c(t.mergeCells),mceTableInsertRowBefore:()=>c(t.insertRowsBefore),mceTableInsertRowAfter:()=>c(t.insertRowsAfter),mceTableInsertColBefore:()=>c(t.insertColumnsBefore),mceTableInsertColAfter:()=>c(t.insertColumnsAfter),mceTableDeleteCol:()=>c(t.deleteColumn),mceTableDeleteRow:()=>c(t.deleteRow),mceTableCutCol:()=>m().each((e=>{zd(e),c(t.deleteColumn)})),mceTableCutRow:()=>i().each((e=>{Nd(e),c(t.deleteRow)})),mceTableCopyCol:()=>m().each((e=>zd(e))),mceTableCopyRow:()=>i().each((e=>Nd(e))),mceTablePasteColBefore:()=>d(t.pasteColsBefore,Ad),mceTablePasteColAfter:()=>d(t.pasteColsAfter,Ad),mceTablePasteRowBefore:()=>d(t.pasteRowsBefore,Bd),mceTablePasteRowAfter:()=>d(t.pasteRowsAfter,Bd),mceTableDelete:()=>Ld(e).each((t=>{ir(t,o).filter(w(o)).each((t=>{const o=Le.fromText("");if(so(t,o),yo(t),e.dom.isEmpty(e.getBody()))e.setContent(""),e.selection.setCursorLocation();else{const t=e.dom.createRng();t.setStart(o.dom,0),t.setEnd(o.dom,0),e.selection.setRng(t),e.nodeChanged()}}))})),mceTableCellToggleClass:(t,o)=>{l((t=>{const n=dd(e),r=P(n,(t=>e.formatter.match("tablecellclass",{value:o},t.dom))),s=r?e.formatter.remove:e.formatter.apply;N(n,(e=>s("tablecellclass",{value:o},e.dom))),hd(e,t.dom,pd)}))},mceTableToggleClass:(t,o)=>{l((t=>{e.formatter.toggle("tableclass",{value:o},t.dom),hd(e,t.dom,pd)}))},mceTableToggleCaption:()=>{Ld(e).each((t=>{ir(t,o).each((o=>{sn(o,"caption").fold((()=>{const t=Le.fromTag("caption");ao(t,Le.fromText("Caption")),((e,t)=>{Yt(e,0).fold((()=>{ao(e,t)}),(e=>{ro(e,t)}))})(o,t),e.selection.setCursorLocation(t.dom,0)}),(n=>{It("caption")(t)&&Ye("td",o).each((t=>e.selection.setCursorLocation(t.dom,0))),yo(n)})),hd(e,o.dom,bd)}))}))},mceTableSizingMode:(t,n)=>(t=>Ld(e).each((n=>{zi(e)||_i(e)||Bi(e)||ir(n,o).each((o=>{"relative"!==t||Rl(o)?"fixed"!==t||Dl(o)?"responsive"!==t||Ol(o)||aa(o):la(o):sa(o),Fi(o),hd(e,o.dom,bd)}))})))(n),mceTableCellType:g((e=>"th"===e?t.makeCellsHeader:t.unmakeCellsHeader)),mceTableColType:g((e=>"th"===e?t.makeColumnsHeader:t.unmakeColumnsHeader)),mceTableRowType:g((e=>{switch(e){case"header":return t.makeRowsHeader;case"footer":return t.makeRowsFooter;default:return t.makeRowsBody}}))},((t,o)=>e.addCommand(o,t))),e.addCommand("mceInsertTable",((t,o)=>{((e,t,o,n={})=>{const r=e=>u(e)&&e>0;if(r(t)&&r(o)){const r=n.headerRows||0,s=n.headerColumns||0;return Cd(e,o,t,s,r)}console.error("Invalid values for mceInsertTable - rows and columns values are required to insert a table.")})(e,o.rows,o.columns,o.options)})),e.addCommand("mceTableApplyCellStyle",((t,o)=>{const l=e=>"tablecell"+e.toLowerCase().replace("-","");if(!s(o))return;const a=_(dd(e),Ki);if(0===a.length)return;const c=((e,t)=>{const o={};return((e,t,o,n)=>{G(e,((e,r)=>{(t(e,r)?o:n)(e,r)}))})(e,t,(e=>(t,o)=>{e[o]=t})(o),f),o})(o,((t,o)=>e.formatter.has(l(o))&&r(t)));(e=>{for(const t in e)if(U.call(e,t))return!1;return!0})(c)||(G(c,((t,o)=>{const n=l(o);N(a,(o=>{""===t?e.formatter.remove(n,{value:null},o.dom,!0):e.formatter.apply(n,{value:t},o.dom)}))})),n(a[0]).each((t=>hd(e,t.dom,pd))))}))},jd=e=>!un(Le.fromDom(e.target),"ephox-snooker-resizer-bar"),Pd=(e,t)=>{const o=(r=ld.selectedSelector,{get:()=>em(Le.fromDom(e.getBody()),r).fold((()=>md(Hi(e),Ii(e)).fold(lm,cm)),am)}),n=((e,t,o)=>{const n=t=>{bo(t,e.selected),bo(t,e.firstSelected),bo(t,e.lastSelected)},r=t=>{fo(t,e.selected,"1")},s=e=>{l(e),o()},l=t=>{const o=vn(t,`${e.selectedSelector},${e.firstSelectedSelector},${e.lastSelectedSelector}`);N(o,n)};return{clearBeforeUpdate:l,clear:s,selectRange:(o,n,l,a)=>{s(o),N(n,r),fo(l,e.firstSelected,"1"),fo(a,e.lastSelected,"1"),t(n,l,a)},selectedSelector:e.selectedSelector,firstSelectedSelector:e.firstSelectedSelector,lastSelectedSelector:e.lastSelectedSelector}})(ld,((t,o,n)=>{ir(o).each((r=>{const s=E(t,(e=>e.dom)),l=Ri(e),a=ha(f,Le.fromDom(e.getDoc()),l),c=((e,t,o)=>{const n=Dr(e);return Ys(n,t).map((e=>{const t=Hs(n,o,!1),{rows:r}=ze(t),s=((e,t)=>{const o=e.slice(0,t[t.length-1].row+1),n=$s(o);return j(n,(e=>{const o=e.cells.slice(0,t[t.length-1].column+1);return E(o,(e=>e.element))}))})(r,e),l=((e,t)=>{const o=e.slice(t[0].row+t[0].rowspan-1,e.length),n=$s(o);return j(n,(e=>{const o=e.cells.slice(t[0].column+t[0].colspan-1,e.cells.length);return E(o,(e=>e.element))}))})(r,e);return{upOrLeftCells:s,downOrRightCells:l}}))})(r,{selection:dd(e)},a).map((e=>K(e,(e=>E(e,(e=>e.dom)))))).getOrUndefined();((e,t,o,n,r)=>{e.dispatch("TableSelectionChange",{cells:t,start:o,finish:n,otherCells:r})})(e,s,o.dom,n.dom,c)}))}),(()=>(e=>{e.dispatch("TableSelectionClear")})(e)));var r;return e.on("init",(o=>{const r=e.getWin(),s=Pi(e),l=Ii(e),a=((e,t,o,n)=>{const r=((e,t,o,n)=>{const r=fe(),s=r.clear,l=s=>{r.on((r=>{n.clearBeforeUpdate(t),Hm(s.target,o).each((l=>{Ji(r,l,o).each((o=>{const r=o.boxes.getOr([]);if(1===r.length){const e=r[0],o="false"===hn(e),l=ie(fn(s.target),e,Je);o&&l&&n.selectRange(t,r,e,e)}else r.length>1&&(n.selectRange(t,r,o.start,o.finish),e.selectContents(l))}))}))}))};return{clearstate:s,mousedown:e=>{n.clear(t),Hm(e.target,o).filter($m).each(r.set)},mouseover:e=>{l(e)},mouseup:e=>{l(e),s()}}})(Xm(e),t,o,n);return{clearstate:r.clearstate,mousedown:r.mousedown,mouseover:r.mouseover,mouseup:r.mouseup}})(r,s,l,n),c=((e,t,o,n)=>{const r=Xm(e),s=()=>(n.clear(t),C.none());return{keydown:(e,l,a,c,i,m)=>{const d=e.raw,u=d.which,f=!0===d.shiftKey,g=Qi(t,n.selectedSelector).fold((()=>(Ym(u)&&!f&&n.clearBeforeUpdate(t),Ym(u)&&f&&!td(l,c)?C.none:Km(u)&&f?b(Fm,r,t,o,Vm,c,l,n.selectRange):Gm(u)&&f?b(Fm,r,t,o,qm,c,l,n.selectRange):Km(u)?b(jm,r,o,Vm,c,l,Im):Gm(u)?b(jm,r,o,qm,c,l,Pm):C.none)),(e=>{const o=o=>()=>{const s=V(o,(o=>((e,t,o,n,r)=>Zi(n,e,t,r.firstSelectedSelector,r.lastSelectedSelector).map((e=>(r.clearBeforeUpdate(o),r.selectRange(o,e.boxes,e.start,e.finish),e.boxes))))(o.rows,o.cols,t,e,n)));return s.fold((()=>Xi(t,n.firstSelectedSelector,n.lastSelectedSelector).map((e=>{const o=Km(u)||m.isForward(u)?kn.after:kn.before;return r.setRelativeSelection(kn.on(e.first,0),o(e.table)),n.clear(t),sm(C.none(),!0)}))),(e=>C.some(sm(C.none(),!0))))};return Ym(u)&&f&&!td(l,c)?C.none:Km(u)&&f?o([Zm(1,0)]):Gm(u)&&f?o([Zm(-1,0)]):m.isBackward(u)&&f?o([Zm(0,-1),Zm(-1,0)]):m.isForward(u)&&f?o([Zm(0,1),Zm(1,0)]):Ym(u)&&!f?s:C.none}));return g()},keyup:(e,r,s,l,a)=>Qi(t,n.selectedSelector).fold((()=>{const c=e.raw,i=c.which;return!0===c.shiftKey&&Ym(i)&&td(r,l)?((e,t,o,n,r,s,l)=>Je(o,r)&&n===s?C.none():an(o,"td,th",t).bind((o=>an(r,"td,th",t).bind((n=>um(e,t,o,n,l))))))(t,o,r,s,l,a,n.selectRange):C.none()}),C.none)}})(r,s,l,n),i=((e,t,o,n)=>{const r=Xm(e);return(e,s)=>{n.clearBeforeUpdate(t),Ji(e,s,o).each((e=>{const o=e.boxes.getOr([]);n.selectRange(t,o,e.start,e.finish),r.selectContents(s),r.collapseSelection()}))}})(r,s,l,n);e.on("TableSelectorChange",(e=>i(e.start,e.finish)));const m=(t,o)=>{(e=>!0===e.raw.shiftKey)(t)&&(o.kill&&t.kill(),o.selection.each((t=>{const o=Nn.relative(t.start,t.finish),n=Ue(r,o);e.selection.setRng(n)})))},d=e=>0===e.button,u=(()=>{const e=ee(Le.fromDom(s)),t=ee(0);return{touchEnd:o=>{const n=Le.fromDom(o.target);if(It("td")(n)||It("th")(n)){const r=e.get(),s=t.get();Je(r,n)&&o.timeStamp-s<300&&(o.preventDefault(),i(n,n))}e.set(n),t.set(o.timeStamp)}}})();e.on("dragstart",(e=>{a.clearstate()})),e.on("mousedown",(e=>{d(e)&&jd(e)&&a.mousedown(no(e))})),e.on("mouseover",(e=>{var t;(void 0===(t=e).buttons||1&t.buttons)&&jd(e)&&a.mouseover(no(e))})),e.on("mouseup",(e=>{d(e)&&jd(e)&&a.mouseup(no(e))})),e.on("touchend",u.touchEnd),e.on("keyup",(t=>{const o=no(t);if(o.raw.shiftKey&&Ym(o.raw.which)){const t=e.selection.getRng(),n=Le.fromDom(t.startContainer),r=Le.fromDom(t.endContainer);c.keyup(o,n,t.startOffset,r,t.endOffset).each((e=>{m(o,e)}))}})),e.on("keydown",(o=>{const n=no(o);t.hide();const r=e.selection.getRng(),s=Le.fromDom(r.startContainer),l=Le.fromDom(r.endContainer),a=Zo(Jm,Qm)(Le.fromDom(e.selection.getStart()));c.keydown(n,s,r.startOffset,l,r.endOffset,a).each((e=>{m(n,e)})),t.show()})),e.on("NodeChange",(()=>{const t=e.selection,o=Le.fromDom(t.getStart()),r=Le.fromDom(t.getEnd());Zl(ir,[o,r]).fold((()=>n.clear(s)),f)}))})),e.on("PreInit",(()=>{e.serializer.addTempAttr(ld.firstSelected),e.serializer.addTempAttr(ld.lastSelected)})),{getSelectedCells:()=>((e,t)=>{switch(e.tag){case"none":return t();case"single":return(e=>[e.dom])(e.element);case"multiple":return(e=>E(e,(e=>e.dom)))(e.elements)}})(o.get(),g([])),clearSelectedCells:e=>n.clear(Le.fromDom(e))}},Id=e=>m(e)&&"TABLE"===e.nodeName,Fd="bar-",Hd=e=>"false"!==ho(e,"data-mce-resize"),$d=e=>{const t=fe(),o=fe(),n=fe();let r,s,l,a;const c=t=>vd(e,t),i=()=>ki(e)?Qn():Jn(),m=(t,o,n,m)=>{const d=(e=>{return pe(t=e,"corner-")?(e=>e.substring(7))(t):t;var t})(o),u=be(d,"e"),f=pe(d,"n");if(""===s&&sa(t),""===a&&(e=>{const t=(e=>jo(e)+"px")(e);Tl(e,C.none(),C.some(t)),ra(e)})(t),n!==r&&""!==s){Eo(t,"width",s);const o=i(),l=c(t),a=ki(e)||u?(e=>ba(e).columns)(t)-1:0;ol(t,n-r,a,o,l)}else if((e=>/^(\d+(\.\d+)?)%$/.test(e))(s)){const e=parseFloat(s.replace("%",""));Eo(t,"width",n*e/r+"%")}if((e=>/^(\d+(\.\d+)?)px$/.test(e))(s)&&(e=>{const t=Dr(e);zr(t)||N(ar(e),(e=>{const t=Bo(e,"width");Eo(e,"width",t),bo(e,"width")}))})(t),m!==l&&""!==a){Eo(t,"height",a);const e=f?0:(e=>ba(e).rows)(t)-1;nl(t,m-l,e)}};e.on("init",(()=>{const r=((e,t)=>((e,t)=>({parent:g(e),view:g(e),dragContainer:g(e),origin:()=>Go(e),isResizable:t}))(Le.fromDom(e.getBody()),t))(e,Hd);if(n.set(r),(e=>{const t=e.options.get("object_resizing");return D(t.split(","),"table")})(e)&&Ai(e)){const n=((e,t,o)=>{const n=ns,r=ss,s=xi(e),l=oi({beforeResize:ti(["table","type"]),afterResize:ti(["table","type"]),startDrag:ti([])});return s.events.adjustHeight.bind((e=>{const t=e.table;l.trigger.beforeResize(t,"row");const o=n.delta(e.delta,t);nl(t,o,e.row),l.trigger.afterResize(t,"row")})),s.events.startAdjust.bind((e=>{l.trigger.startDrag()})),s.events.adjustWidth.bind((e=>{const n=e.table;l.trigger.beforeResize(n,"col");const s=r.delta(e.delta,n),a=o(n);ol(n,s,e.column,t,a),l.trigger.afterResize(n,"col")})),{on:s.on,off:s.off,refreshBars:s.refresh,hideBars:s.hideBars,showBars:s.showBars,destroy:s.destroy,events:l.registry}})(r,i(),c);e.mode.isReadOnly()||n.on(),n.events.startDrag.bind((o=>{t.set(e.selection.getRng())})),n.events.beforeResize.bind((t=>{const o=t.table.dom;((e,t,o,n,r)=>{e.dispatch("ObjectResizeStart",{target:t,width:o,height:n,origin:r})})(e,o,$i(o),Vi(o),Fd+t.type)})),n.events.afterResize.bind((o=>{const n=o.table,r=n.dom;Fi(n),t.on((t=>{e.selection.setRng(t),e.focus()})),((e,t,o,n,r)=>{e.dispatch("ObjectResized",{target:t,width:o,height:n,origin:r})})(e,r,$i(r),Vi(r),Fd+o.type),e.undoManager.add()})),o.set(n)}})),e.on("ObjectResizeStart",(t=>{const o=t.target;if(Id(o)&&!e.mode.isReadOnly()){const n=Le.fromDom(o);N(e.dom.select(".mce-clonedresizable"),(t=>{e.dom.addClass(t,"mce-"+Oi(e)+"-columns")})),!Dl(n)&&_i(e)?la(n):!Rl(n)&&Bi(e)&&sa(n),Ol(n)&&pe(t.origin,Fd)&&sa(n),r=t.width,s=zi(e)?"":Ui(e,o).getOr(""),l=t.height,a=Gi(e,o).getOr("")}})),e.on("ObjectResized",(t=>{const o=t.target;if(Id(o)){const n=Le.fromDom(o),r=t.origin;(e=>pe(e,"corner-"))(r)&&m(n,r,t.width,t.height),Fi(n),hd(e,n.dom,pd)}}));const d=()=>{o.on((e=>{e.on(),e.showBars()}))},u=()=>{o.on((e=>{e.off(),e.hideBars()}))};return e.on("DisabledStateChange",(e=>{e.state?u():d()})),e.on("SwitchMode",(()=>{e.mode.isReadOnly()?u():d()})),e.on("dragstart dragend",(e=>{"dragstart"===e.type?u():d()})),e.on("remove",(()=>{o.on((e=>{e.destroy()}))})),{refresh:e=>{o.on((t=>t.refreshBars(Le.fromDom(e))))},hide:()=>{o.on((e=>e.hideBars()))},show:()=>{o.on((e=>e.showBars()))}}},Vd=e=>{(e=>{const t=e.options.register;t("table_clone_elements",{processor:"string[]"}),t("table_use_colgroups",{processor:"boolean",default:!0}),t("table_header_type",{processor:e=>{const t=D(["section","cells","sectionCells","auto"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be one of: section, cells, sectionCells or auto."}},default:"section"}),t("table_sizing_mode",{processor:"string",default:"auto"}),t("table_default_attributes",{processor:"object",default:{border:"1"}}),t("table_default_styles",{processor:"object",default:{"border-collapse":"collapse"}}),t("table_column_resizing",{processor:e=>{const t=D(["preservetable","resizetable"],e);return t?{value:e,valid:t}:{valid:!1,message:"Must be preservetable, or resizetable."}},default:"preservetable"}),t("table_resize_bars",{processor:"boolean",default:!0}),t("table_style_by_css",{processor:"boolean",default:!0}),t("table_merge_content_on_paste",{processor:"boolean",default:!0})})(e);const t=$d(e),o=Pd(e,t),n=yd(e,t,o);return Md(e,n),((e,t)=>{const o=Ii(e),n=t=>md(Hi(e)).bind((n=>ir(n,o).map((o=>{const r=ad(dd(e),o,n);return t(o,r)})))).getOr("");G({mceTableRowType:()=>n(t.getTableRowType),mceTableCellType:()=>n(t.getTableCellType),mceTableColType:()=>n(t.getTableColType)},((t,o)=>e.addQueryValueHandler(o,t)))})(e,n),ud(e,n),{getSelectedCells:o.getSelectedCells,clearSelectedCells:o.clearSelectedCells}};e.add("dom",(e=>({table:Vd(e)})))}();