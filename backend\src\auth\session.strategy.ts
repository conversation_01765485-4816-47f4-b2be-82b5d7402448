import { Injectable } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { Strategy } from "passport-local";
import { Request } from "express";

declare module "express-session" {
    interface SessionData {
        user?: {
            id: string;
            email: string;
            role: string;
        };
    }
}

@Injectable()
export class SessionStrategy extends PassportStrategy(Strategy, "session") {
    constructor() {
        super({
            usernameField: "email",
            passwordField: "otp",
            passReqToCallback: true,
        });
    }

    async validate(req: Request, email: string, otp: string): Promise<any> {
        // This strategy is mainly for session serialization/deserialization
        // The actual authentication logic is handled in the AuthService
        return req.session.user || null;
    }
}
