import { Sitemap } from "@commune/api";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CommuneService } from "src/commune/commune.service";
import { ReactorHubService } from "src/reactor/reactor-hub.service";
import { ReactorPostService } from "src/reactor/reactor-post.service";
import { ReactorCommunityService } from "src/reactor/reactor-community.service";

const SITEMAP_KEY =
    "BycS9tEQKMvasaJGni12BUBVl5xl1E7fsGqocvG5xKe5es3XWlrMXeRGYvJ7r3iS";

@Injectable()
export class SitemapService {
    constructor(
        private readonly communeService: CommuneService,
        private readonly reactorPostService: ReactorPostService,
        private readonly reactorHubService: ReactorHubService,
        private readonly reactorCommunityService: ReactorCommunityService,
    ) {}

    async getSitemapGenerationData(
        key: string,
    ): Promise<Sitemap.GetSitemapGenerationDataOutput> {
        if (key !== SITEMAP_KEY) {
            throw new ForbiddenException();
        }

        const [communeIds, reactorPostIds, reactorHubIds, reactorCommunityIds] =
            await Promise.all([
                this.communeService.getCommuneIds(),
                this.reactorPostService.getPostIds(),
                this.reactorHubService.getHubIds(),
                this.reactorCommunityService.getCommunityIds(),
            ]);

        return {
            communeIds,
            reactorPostIds,
            reactorHubIds,
            reactorCommunityIds,
        };
    }
}
