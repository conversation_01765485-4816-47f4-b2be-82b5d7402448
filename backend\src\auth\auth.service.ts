import { Auth } from "@commune/api";
import * as prisma from "@prisma/client";
import {
    ForbiddenException,
    Injectable,
    UnauthorizedException,
} from "@nestjs/common";
import { getError } from "src/common/errors";
import { UserService } from "src/user/user.service";
import { EmailService } from "src/email/email.service";
import { EmailOtpService } from "src/email/email-otp.service";
import { AdminService } from "src/admin/admin.service";
import { ConfigService } from "src/config/config.service";

type InfoDto = {
    ipAddress: string | null;
    userAgent: string | null;
};

@Injectable()
export class AuthService {
    constructor(
        private readonly configService: ConfigService,
        private readonly emailService: EmailService,
        private readonly userService: UserService,
        private readonly userOtpService: EmailOtpService,
        private readonly adminService: AdminService,
    ) {}

    protected createSessionUser(user: prisma.User) {
        return {
            id: user.id,
            email: user.email,
            role: user.role,
        };
    }

    protected generateOtp() {
        return Math.floor(100000 + Math.random() * 900000).toString();
    }

    async otp(dto: Auth.SendOtpInput & InfoDto) {
        const otp = this.generateOtp();

        await this.userOtpService.create({
            email: dto.email,
            otp,
            ipAddress: dto.ipAddress,
            userAgent: dto.userAgent,
        });

        console.log({ otp, email: dto.email });

        return await this.emailService.sendOtp({
            to: dto.email,
            otp,
        });
    }

    async login(dto: Auth.SigninInput & InfoDto) {
        const user = await this.userService.getUserByEmail(dto.email);

        if (!user) {
            throw new UnauthorizedException(...getError("user_not_found"));
        }

        if (!this.configService.config.auth.disableLoginOtpCheck) {
            const userOtp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });

            await this.userOtpService.softDelete({
                id: userOtp.id,
            });
        }

        const sessionUser = this.createSessionUser(user);

        return {
            user: sessionUser,
        };
    }

    async register(dto: Auth.SignupInput & InfoDto) {
        // check for duplicates
        {
            const user = await this.userService.getUserByEmail(dto.email);

            if (user) {
                throw new UnauthorizedException(
                    ...getError("user_already_exists"),
                );
            }
        }

        if (!this.configService.config.auth.disableRegisterOtpCheck) {
            const otp = await this.userOtpService.check({
                email: dto.email,
                otp: dto.otp,
            });

            await this.userOtpService.softDelete({
                id: otp.id,
            });
        }

        if (!this.configService.config.auth.disableRegisterInviteCheck) {
            const isInviteExists = await this.adminService.isInviteExists(
                dto.email,
            );

            if (!isInviteExists) {
                throw new ForbiddenException(...getError("must_have_invite"));
            }
        }

        const user = await this.userService.createUser({
            referrerId: dto.referrerId,
            email: dto.email,
        });

        if (!this.configService.config.auth.disableRegisterInviteCheck) {
            await this.adminService.useInvite(dto.email);
        }

        const sessionUser = this.createSessionUser(user);

        return {
            user: sessionUser,
        };
    }
}
