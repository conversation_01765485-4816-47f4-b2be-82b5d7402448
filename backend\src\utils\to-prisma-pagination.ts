type PagePagination = {
    page?: number;
    size?: number;
};

type OffsetPagination = {
    limit?: number;
    offset?: number;
};

type PrismaPagination = {
    skip: number;
    take: number;
};

export function toPrismaPagination(
    data: PagePagination | OffsetPagination | undefined,
): PrismaPagination;

export function toPrismaPagination(data: {
    pagination?: PagePagination | OffsetPagination;
}): PrismaPagination;

export function toPrismaPagination(
    data:
        | PagePagination
        | OffsetPagination
        | undefined
        | {
              pagination?: PagePagination | OffsetPagination;
          },
): PrismaPagination {
    let pagination: PagePagination | OffsetPagination | undefined = undefined;

    if (data) {
        if ("pagination" in data) {
            pagination = data.pagination;
        } else if ("page" in data && "size" in data) {
            pagination = data;
        } else if ("limit" in data && "offset" in data) {
            pagination = data;
        }
    }

    if (pagination) {
        if ("page" in pagination && "size" in pagination) {
            const page = pagination.page ?? 1;
            const size = pagination.size ?? 20;

            return {
                skip: (page - 1) * size,
                take: size,
            };
        } else if ("limit" in pagination && "offset" in pagination) {
            const limit = pagination.limit ?? 20;
            const offset = pagination.offset ?? 0;

            return {
                skip: offset,
                take: limit,
            };
        }
    }

    return {
        skip: 0,
        take: 20,
    };
}
