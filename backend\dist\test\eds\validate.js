"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const node_path_1 = __importDefault(require("node:path"));
const promises_1 = __importDefault(require("node:fs/promises"));
const node_crypto_1 = require("node:crypto");
const root = node_path_1.default.join(process.cwd(), "src/test/eds");
async function validate(document, signature, publicKey) {
    const verifier = (0, node_crypto_1.createVerify)("rsa-sha256");
    verifier.update(document);
    const isValid = verifier.verify(publicKey, signature);
    console.log({ isValid });
}
(async () => {
    const documentName = process.argv[2];
    const signatureName = process.argv[3];
    const publicKeyName = process.argv[4];
    if (!documentName) {
        throw new Error("Document name is required.");
    }
    if (!signatureName) {
        throw new Error("Signature name is required.");
    }
    if (!publicKeyName) {
        throw new Error("Public key name is required.");
    }
    const document = await promises_1.default.readFile(node_path_1.default.join(root, documentName));
    const signature = await promises_1.default.readFile(node_path_1.default.join(root, signatureName));
    const publicKey = await promises_1.default.readFile(node_path_1.default.join(root, `${publicKeyName}-public.pem`));
    await validate(document, signature, publicKey);
})();
//# sourceMappingURL=validate.js.map