import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, params, url }) => {
  // console.log(browser, dev);

  const { fetcher: api } = getClient();

  const [
    me,
    [commune],
    members,
  ] = await Promise.all([
    api.user.me.get({ fetch, skipInterceptor: true })
      .catch(() => null),
    api.commune.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.commune.member.list.get({ communeId: params.id }, { fetch, ctx: { url } }),
  ]);

  if (!commune) {
    throw error(404, "Commune not found");
  }

  // Determine user permissions and status
  const isLoggedIn = !!me;
  const isAdmin = me?.role === "admin";
  const isHeadMember = isLoggedIn && commune.headMember.actorType === "user" && commune.headMember.actorId === me.id;
  const isMember = isLoggedIn && members.some(member =>
    member.actorType === "user" &&
    member.actorId === me.id &&
    !member.deletedAt
  );

  let hasPendingJoinRequest = false;

  if (isLoggedIn && !isMember) {
    const joinRequests = await api.commune.joinRequest.list.get(
      {},
      { fetch, ctx: { url } },
    );

    hasPendingJoinRequest = joinRequests.some(
      ({ communeId, status }) => communeId === params.id && status === "pending",
    );
  }

  return {
    commune,
    members,
    userPermissions: {
      isLoggedIn,
      isAdmin,
      isHeadMember,
      isMember,
      canInvite: isAdmin || isHeadMember,
      canRequestJoin: isLoggedIn && !isMember && !hasPendingJoinRequest,
      hasPendingJoinRequest,
    },
  };
};
