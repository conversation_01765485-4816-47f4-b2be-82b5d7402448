"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RatingService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const errors_1 = require("../common/errors");
let RatingService = RatingService_1 = class RatingService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(RatingService_1.name);
    }
    async getUserFeedbacks(input) {
        return await this.prisma.userFeedback.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: {
                targetUserId: input.userId,
            },
            include: {
                text: true,
                sourceUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
            },
            orderBy: {
                createdAt: "desc",
            },
        });
    }
    async createUserFeedback(data, user) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("must_be_admin_or_source_user"));
            }
            if (data.sourceUserId === data.targetUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("source_and_target_users_must_differ"));
            }
        }
        const { id } = await this.prisma.userFeedback.create({
            data: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                value: data.value,
                isAnonymous: data.isAnonymous,
                text: {
                    create: (0, utils_1.toPrismaLocalizations)(data.text, "text"),
                },
            },
        });
        return { id };
    }
    async getUserSummary(input) {
        const [rating, karma, rate] = await Promise.all([
            this.prisma.userRating.aggregate({
                where: {
                    targetUserId: input.userId,
                },
                _sum: {
                    value: true,
                },
            }),
            this.prisma.userKarmaGivenPoint.aggregate({
                where: {
                    targetUserId: input.userId,
                },
                _sum: {
                    quantity: true,
                },
            }),
            this.prisma.userFeedback.aggregate({
                where: {
                    targetUserId: input.userId,
                },
                _avg: {
                    value: true,
                },
            }),
        ]);
        return {
            rating: rating._sum.value ?? 0,
            karma: karma._sum.quantity ?? 0,
            rate: rate._avg.value,
        };
    }
    async upsertRelativeUserRating(data, prisma = this.prisma) {
        await prisma.userRating.upsert({
            where: {
                sourceUserId_targetUserId_entityType_entityId: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    entityType: data.entityType,
                    entityId: data.entityId,
                },
            },
            create: {
                sourceUserId: data.sourceUserId,
                targetUserId: data.targetUserId,
                entityType: data.entityType,
                entityId: data.entityId,
                value: data.value,
            },
            update: {
                value: data.value,
            },
        });
    }
    async deleteRelativeUserRating(data, prisma = this.prisma) {
        await prisma.userRating.delete({
            where: {
                sourceUserId_targetUserId_entityType_entityId: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    entityType: data.entityType,
                    entityId: data.entityId,
                },
            },
        });
    }
};
exports.RatingService = RatingService;
exports.RatingService = RatingService = RatingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RatingService);
//# sourceMappingURL=rating.service.js.map