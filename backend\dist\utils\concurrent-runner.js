"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConcurrentRunner = void 0;
class ConcurrentRunner {
    static async run(tasks, maxConcurrent = 5) {
        const results = new Array(tasks.length);
        let currentIndex = 0;
        async function processTask(taskIndex) {
            results[taskIndex] = await tasks[taskIndex]();
            if (currentIndex < tasks.length) {
                const nextIndex = currentIndex++;
                await processTask(nextIndex);
            }
        }
        const initialBatch = [];
        const tasksToStart = Math.min(maxConcurrent, tasks.length);
        for (let i = 0; i < tasksToStart; i++) {
            initialBatch.push(processTask(currentIndex++));
        }
        await Promise.all(initialBatch);
        return results;
    }
}
exports.ConcurrentRunner = ConcurrentRunner;
//# sourceMappingURL=concurrent-runner.js.map