<script lang="ts">
  import type { Reactor, Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";

  interface Props {
    locale: Common.WebsiteLocale;
    selectedHubId: string | null;
    label?: string;
    placeholder?: string;
  }

  const i18n = {
    en: {
      hub: "Hub",
      selectHub: "Select hub",
      searchHubs: "Search hubs...",
      noHubsFound: "No hubs found",
      loading: "Loading...",
      clearSelection: "Clear selection",
    },
    ru: {
      hub: "Хаб",
      selectHub: "Выбрать хаб",
      searchHubs: "Поиск хабов...",
      noHubsFound: "Хабы не найдены",
      loading: "Загрузка...",
      clearSelection: "Очистить выбор",
    },
  };

  const { fetcher: api } = getClient();

  let { selectedHubId = $bindable(), locale, label, placeholder }: Props = $props();

  const t = $derived(i18n[locale]);

  // State
  let showDropdown = $state(false);
  let hubSearchQuery = $state("");
  let searchResults = $state<Reactor.GetHubsOutput>([]);
  let selectedHub = $state<Reactor.GetHubsOutput[0] | null>(null);
  let isSearching = $state(false);
  let searchDebounceTimeout = $state<ReturnType<typeof setTimeout> | null>(null);
  let inputElement: HTMLInputElement;

  // Helper function to get appropriate localization
  function getAppropriateLocalization(localizations: Common.Localizations): string {
    const localization = localizations.find((l) => l.locale === locale);
    return localization?.value || localizations[0]?.value || "";
  }

  // Load selected hub on mount and when selectedHubId changes
  $effect(() => {
    if (selectedHubId) {
      loadSelectedHub();
    } else {
      selectedHub = null;
    }
  });

  // Load hubs when dropdown is shown (even without search query)
  $effect(() => {
    if (showDropdown && !searchResults.length) {
      searchHubs("");
    }
  });

  async function loadSelectedHub() {
    if (!selectedHubId) return;

    try {
      const hubs = await api.reactor.hub.list.get({ ids: [selectedHubId] });
      selectedHub = hubs[0] || null;
    } catch (error) {
      console.error("Failed to load selected hub:", error);
      selectedHub = null;
    }
  }

  async function searchHubs(query: string) {
    const searchQuery = query.trim() || undefined;

    isSearching = true;

    try {
      searchResults = await api.reactor.hub.list.get({ query: searchQuery });
    } catch (error) {
      console.error("Failed to search hubs:", error);
      searchResults = [];
    } finally {
      isSearching = false;
    }
  }

  function debounceSearch(query: string) {
    if (searchDebounceTimeout) {
      clearTimeout(searchDebounceTimeout);
    }

    searchDebounceTimeout = setTimeout(() => {
      searchHubs(query);
    }, 300);
  }

  function handleSearchInput(event: Event) {
    const target = event.target as HTMLInputElement;
    hubSearchQuery = target.value;
    debounceSearch(hubSearchQuery);
  }

  function selectHub(hub: Reactor.GetHubsOutput[0]) {
    selectedHubId = hub.id;
    selectedHub = hub;

    // Clear search and hide dropdown
    hubSearchQuery = "";
    showDropdown = false;
    inputElement?.blur();
  }

  function clearSelection() {
    selectedHubId = null;
    selectedHub = null;
  }

  function handleInputFocus() {
    showDropdown = true;
  }

  function handleInputBlur() {
    // Use setTimeout to allow click events on dropdown items to fire first
    setTimeout(() => {
      showDropdown = false;
    }, 150);
  }
</script>

<div class="mb-3">
  {#if label}
    <label for="hub-search-input" class="form-label">{label}</label>
  {/if}

  <div class="hub-picker">
    <!-- Selected Hub Display -->
    {#if selectedHub}
      <div class="selected-hub d-flex align-items-center gap-2 mb-2">
        <div class="d-flex align-items-center bg-light border rounded p-2 flex-grow-1">
          {#if selectedHub.image}
            <img
              src={`/images/${selectedHub.image}`}
              alt={getAppropriateLocalization(selectedHub.name)}
              class="rounded me-2"
              style="width: 32px; height: 32px; object-fit: contain;"
            />
          {:else}
            <div
              class="rounded bg-primary d-flex align-items-center justify-content-center me-2"
              style="width: 32px; height: 32px; color: white; font-size: 14px;"
            >
              <i class="bi bi-collection"></i>
            </div>
          {/if}
          <div class="flex-grow-1">
            <div class="fw-medium">{getAppropriateLocalization(selectedHub.name)}</div>
            <small class="text-muted">{getAppropriateLocalization(selectedHub.description)}</small>
          </div>
          <button
            type="button"
            class="btn btn-sm btn-outline-danger"
            onclick={clearSelection}
            aria-label={t.clearSelection}
          >
            <i class="bi bi-x"></i>
          </button>
        </div>
      </div>
    {/if}

    <!-- Hub Search Input -->
    <div class="hub-search-container position-relative">
      <input
        bind:this={inputElement}
        type="text"
        id="hub-search-input"
        class="form-control"
        placeholder={placeholder || t.searchHubs}
        bind:value={hubSearchQuery}
        oninput={handleSearchInput}
        onfocus={handleInputFocus}
        onblur={handleInputBlur}
      />

      <!-- Search Results Dropdown -->
      {#if showDropdown && searchResults && (isSearching || searchResults.length > 0)}
        <div
          class="search-results position-absolute w-100 bg-white border border-top-0 rounded-bottom shadow-sm"
          style="z-index: 1000; max-height: 200px; overflow-y: auto;"
        >
          {#if isSearching}
            <div class="p-2 text-muted">
              <i class="bi bi-hourglass-split me-1"></i>
              {t.loading}
            </div>
          {:else if searchResults.length === 0}
            <div class="p-2 text-muted">
              {t.noHubsFound}
            </div>
          {:else}
            {#each searchResults as hub (hub.id)}
              <button
                type="button"
                class="dropdown-item d-flex align-items-center p-2"
                onclick={() => selectHub(hub)}
              >
                {#if hub.image}
                  <img
                    src={`/images/${hub.image}`}
                    alt={getAppropriateLocalization(hub.name)}
                    class="rounded me-2"
                    style="width: 24px; height: 24px; object-fit: contain;"
                  />
                {:else}
                  <div
                    class="rounded bg-primary d-flex align-items-center justify-content-center me-2"
                    style="width: 24px; height: 24px; color: white; font-size: 12px;"
                  >
                    <i class="bi bi-collection"></i>
                  </div>
                {/if}
                <div>
                  <div class="fw-medium">{getAppropriateLocalization(hub.name)}</div>
                  <small class="text-muted">{getAppropriateLocalization(hub.description)}</small>
                </div>
              </button>
            {/each}
          {/if}
        </div>
      {/if}
    </div>
  </div>
</div>

<style>
  .hub-picker {
    position: relative;
  }

  .selected-hub {
    min-height: 2rem;
  }

  .search-results {
    border-top: none !important;
  }

  .search-results .dropdown-item {
    border: none;
    background: none;
    text-align: left;
    width: 100%;
  }

  .search-results .dropdown-item:hover {
    background-color: var(--bs-light);
  }
</style>
