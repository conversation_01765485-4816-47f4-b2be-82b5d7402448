// @ts-nocheck
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const [
    me,
    communes,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.commune.list.get({}, { fetch, ctx: { url } }),
  ]);

  let pendingInvitationsCount = 0;

  if (me) {
    const invitations = await api.commune.invitation.list.get({}, { fetch, ctx: { url } });

    pendingInvitationsCount = invitations.filter(({ status }) => status === "pending").length;
  }

  return {
    communes,
    isHasMoreCommunes: communes.length === Consts.PAGE_SIZE,
    pendingInvitationsCount,
    isLoggedIn: !!me,
  };
};
