import fs from "node:fs/promises";
import { generateKeyPairSync } from "node:crypto";

const PRIVATE_KEY_PASSPHRASE = "secret";

const { publicKey, privateKey } = generateKeyPairSync("rsa", {
    modulusLength: 4096,
    publicKeyEncoding: {
        type: "spki",
        format: "pem",
    },
    privateKeyEncoding: {
        type: "pkcs8",
        format: "pem",
        cipher: "aes-256-cbc",
        passphrase: PRIVATE_KEY_PASSPHRASE,
    },
});

(async () => {
    const name = process.argv[2] ?? String(Date.now());

    await fs.writeFile(`./src/test/eds/${name}-public.pem`, publicKey);
    await fs.writeFile(`./src/test/eds/${name}-private.pem`, privateKey);
})();
