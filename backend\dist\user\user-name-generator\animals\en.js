"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WORDS = void 0;
exports.getRandomAnimal = getRandomAnimal;
exports.WORDS = [
    "albatross",
    "alpaca",
    "anteater",
    "antelope",
    "armadillo",
    "bear",
    "beaver",
    "bison",
    "bluejay",
    "bonobo",
    "buffalo",
    "butterfly",
    "camel",
    "canary",
    "capybara",
    "cardinal",
    "cat",
    "chimpanzee",
    "chinchilla",
    "chipmunk",
    "crane",
    "dog",
    "dolphin",
    "dove",
    "dugong",
    "elephant",
    "emu",
    "fennec",
    "ferret",
    "finch",
    "flamingo",
    "fox",
    "gazelle",
    "giraffe",
    "gorilla",
    "guineapig",
    "gull",
    "hedgehog",
    "heron",
    "horse",
    "hummingbird",
    "ibex",
    "ibis",
    "impala",
    "jellyfish",
    "kangaroo",
    "koala",
    "kookaburra",
    "kudu",
    "ladybug",
    "lemur",
    "llama",
    "loon",
    "magpie",
    "manatee",
    "marmoset",
    "meerkat",
    "mongoose",
    "moose",
    "mouse",
    "narwhal",
    "orangutan",
    "oryx",
    "otter",
    "owl",
    "panda",
    "parrot",
    "peacock",
    "pelican",
    "penguin",
    "pika",
    "platypus",
    "puffin",
    "quokka",
    "rabbit",
    "raccoon",
    "redpanda",
    "robin",
    "seahorse",
    "seal",
    "sealion",
    "sloth",
    "sparrow",
    "squirrel",
    "starfish",
    "stork",
    "sugarglider",
    "swallow",
    "swan",
    "toucan",
    "turtle",
    "walrus",
    "whale",
    "wren",
    "yak",
    "zebra",
];
function getRandomAnimal(variant) {
    return exports.WORDS[variant];
}
//# sourceMappingURL=en.js.map