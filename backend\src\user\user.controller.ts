import { Common } from "@commune/api";
import {
    Controller,
    NotFoundException,
    Param,
    Put,
    UploadedFile,
    UseGuards,
    UseInterceptors,
    ParseFilePipe,
    MaxFileSizeValidator,
    FileTypeValidator,
    BadRequestException,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { ZodPipe } from "src/zod";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { HttpCurrentUser } from "src/auth/http/current-user.decorator";
import { HttpSessionAuthGuard } from "src/auth/http/session-auth.guard";
import { UserService } from "./user.service";
import { UserNoteService } from "./user-note.service";
import { UserTitleService } from "./user-title.service";
import { getServer } from "src/acrpc";

// Constants for file upload validation
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];

@Controller("user")
@UseGuards(HttpSessionAuthGuard)
export class UserController {
    constructor(
        private readonly userService: UserService,
        private readonly userNoteService: UserNoteService,
        private readonly userTitleService: UserTitleService,
    ) {
        const acrpcServer = getServer();

        acrpcServer.register({
            user: {
                list: {
                    get: async (input, metadata) => {
                        const users = await this.userService.getUsers(
                            input,
                            metadata.user,
                        );

                        return users.map((user) => ({
                            ...user,
                            image: user.image?.url ?? null,
                        }));
                    },
                },
                me: {
                    get: async (_, metadata) => {
                        const user = await this.userService.getUser(
                            metadata.user.id,
                            metadata.user,
                        );

                        if (!user) {
                            throw new NotFoundException(
                                ...getError("user_not_found"),
                            );
                        }

                        return {
                            ...user,
                            image: user.image?.url ?? null,
                        };
                    },
                },
                patch: (input, metadata) =>
                    this.userService.updateUser(input, metadata.user),
                title: {
                    list: {
                        get: (input, metadata) =>
                            this.userTitleService.getUserTitles(
                                input,
                                metadata.user,
                            ),
                    },
                    post: (input, metadata) =>
                        this.userTitleService.createUserTitle(
                            input,
                            metadata.user,
                        ),
                    patch: (input, metadata) =>
                        this.userTitleService.updateUserTitle(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.userTitleService.deleteUserTitle(
                            input,
                            metadata.user,
                        ),
                },
                note: {
                    get: async (input, metadata) => {
                        const note = await this.userNoteService.getUserNote(
                            input,
                            metadata.user,
                        );

                        return {
                            text: note?.text ?? null,
                        };
                    },
                    put: (input, metadata) =>
                        this.userNoteService.updateUserNote(
                            input,
                            metadata.user,
                        ),
                },
            },
        });
    }

    // @Get("me")
    // @UseGuards(HttpSessionAuthGuard)
    // async me(@HttpCurrentUser() currentUser: CurrentUser) {
    //     const user = await this.userService.getUser(
    //         currentUser.id,
    //         currentUser,
    //     );

    //     if (!user) {
    //         throw new UnauthorizedException();
    //     }

    //     return Common.parseInput(User.GetMeOutputSchema, {
    //         ...user,
    //         image: user.image?.url ?? null,
    //     });
    // }

    // @Get()
    // async getUsers(
    //     @Query(new ZodPipe(Common.PaginationSchema))
    //     pagination: Common.Pagination,
    //     @Query(
    //         "body",
    //         new ZodPipe(
    //             Common.JsonStringToObject(User.GetUsersInputSchema.shape),
    //         ),
    //     )
    //     body: User.GetUsersInput,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     const users = await this.userService.getUsers(
    //         body,
    //         pagination,
    //         currentUser,
    //     );

    //     return Common.parseInput(
    //         User.GetUsersOutputSchema,
    //         users.map((user) => ({
    //             ...user,
    //             image: user.image?.url ?? null,
    //         })),
    //     );
    // }

    // @Get(":id")
    // async getUser(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     const user = await this.userService.getUser(id, currentUser);

    //     if (!user) {
    //         throw new NotFoundException(...getError("user_not_found"));
    //     }

    //     return Common.parseInput(User.GetUserOutputSchema, {
    //         ...user,
    //         image: user.image?.url ?? null,
    //     });
    // }

    // @Patch(":id")
    // @UseGuards(HttpSessionAuthGuard)
    // async updateUser(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(User.UpdateUserInputSchema))
    //     body: User.UpdateUserInput,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     await this.userService.updateUser(id, body, currentUser);
    // }

    @Put(":id/image")
    @UseGuards(HttpSessionAuthGuard)
    @UseInterceptors(FileInterceptor("image"))
    async uploadUserImage(
        @Param("id", new ZodPipe(Common.id)) id: string,
        @HttpCurrentUser() currentUser: CurrentUser,
        @UploadedFile(
            new ParseFilePipe({
                validators: [
                    new MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
                    new FileTypeValidator({
                        fileType: ALLOWED_FILE_TYPES.join("|"),
                    }),
                ],
            }),
        )
        file?: Express.Multer.File,
    ) {
        if (!file) {
            throw new BadRequestException("No file uploaded");
        }

        await this.userService.updateUserImage(id, file, currentUser);
    }

    // @Get("title")
    // async getUserTitles(
    //     @Query(
    //         "body",
    //         new ZodPipe(
    //             Common.JsonStringToObject(User.GetUserTitlesInputSchema.shape),
    //         ),
    //     )
    //     body: User.GetUserTitlesInput,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     const userTitles = await this.userTitleService.getUserTitles(
    //         body,
    //         currentUser,
    //     );

    //     return Common.parseInput(User.GetUserTitlesOutputSchema, userTitles);
    // }

    // @Post("title")
    // async createUserTitle(
    //     @Body(new ZodPipe(User.CreateUserTitleInputSchema))
    //     body: User.CreateUserTitleInput,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     const userTitle = await this.userTitleService.createUserTitle(
    //         body,
    //         currentUser,
    //     );

    //     return Common.parseInput(Common.ObjectWithIdSchema, userTitle);
    // }

    // @Patch("title/:id")
    // async updateUserTitle(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(User.UpdateUserTitleInputSchema))
    //     body: User.UpdateUserTitleInput,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     await this.userTitleService.updateUserTitle(id, body, currentUser);
    // }

    // @Delete("title/:id")
    // async deleteUserTitle(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     await this.userTitleService.deleteUserTitle(id, currentUser);
    // }

    // @Get(":id/note")
    // async getUserNote(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     const note = await this.userNoteService.getUserNote(id, currentUser);

    //     return Common.parseInput(User.GetUserNoteOutputSchema, {
    //         text: note?.text ?? null,
    //     });
    // }

    // @Put(":id/note")
    // async updateUserNote(
    //     @Param("id", new ZodPipe(Common.id)) id: string,
    //     @Body(new ZodPipe(User.UpdateUserNoteInputSchema))
    //     body: User.UpdateUserNoteInput,
    //     @HttpCurrentUser() currentUser: CurrentUser,
    // ) {
    //     await this.userNoteService.updateUserNote(
    //         {
    //             userId: id,
    //             text: body.text,
    //         },
    //         currentUser,
    //     );

    //     return true;
    // }
}
