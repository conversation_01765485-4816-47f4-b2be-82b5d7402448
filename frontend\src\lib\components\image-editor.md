# Image Editor Component

A comprehensive image editing component built for SvelteKit that provides crop, resize, rotate, and flip functionality without external dependencies.

## Features

- **Crop**: Interactive drag-to-select crop area with visual overlay
- **Resize**: Numeric input controls with aspect ratio preservation option
- **Rotate**: 90-degree rotation left/right
- **Flip**: Horizontal and vertical mirroring

- **Reset**: Restore original image state
- **Responsive Design**: Works on desktop and mobile devices
- **Accessibility**: Full keyboard navigation and screen reader support
- **Internationalization**: English and Russian language support

## Usage

```svelte
<script>
  import { ImageEditor } from "$lib/components";
  
  let selectedFile = null;
  
  function handleSave(editedFile) {
    // Handle the edited file
    console.log('Edited file:', editedFile);
  }
  
  function handleCancel() {
    // Handle cancellation
    console.log('Edit cancelled');
  }
</script>

{#if selectedFile}
  <ImageEditor
    imageFile={selectedFile}
    onSave={handleSave}
    onCancel={handleCancel}
    locale="en"
  />
{/if}
```

## Props

| Prop | Type | Required | Description |
|------|------|----------|-------------|
| `imageFile` | `File` | Yes | The image file to edit |
| `onSave` | `(editedFile: File) => void` | Yes | Callback when user saves the edited image |
| `onCancel` | `() => void` | Yes | Callback when user cancels editing |
| `locale` | `'en' \| 'ru'` | Yes | Language for the interface |
| `editedFile` | `File \| null` | No | Previously edited file to preserve settings |

## Implementation Details

### Canvas-based Editing
- Uses HTML5 Canvas for real-time image manipulation
- Maintains original image quality during editing
- Applies transformations only on final save

### Crop Functionality
- Interactive drag-to-select crop area
- Visual overlay with semi-transparent background
- Corner handles for precise selection
- Real-time dimension display

### Resize Functionality
- Numeric input controls for width and height
- Optional aspect ratio preservation
- Validation for reasonable size limits (1-4000px)

### Rotation and Flip
- 90-degree incremental rotation
- Independent horizontal and vertical flipping
- Visual preview of all transformations

### Quality Control
- Fixed high quality output (90%)
- Maintains original format

### Error Handling
- Graceful handling of invalid image files
- Console logging for debugging
- User-friendly error states

## Styling

The component uses Bootstrap classes for consistent styling with the rest of the application. Custom CSS provides:

- Responsive layout that adapts to screen size
- Accessible focus indicators
- Smooth transitions and animations
- Professional appearance with proper spacing

## Browser Compatibility

- Modern browsers with Canvas support
- File API support required
- Tested on Chrome, Firefox, Safari, Edge

## Performance Considerations

- Efficient canvas rendering
- Memory management with proper cleanup
- Optimized for images up to 5MB
- Responsive to user interactions

## Integration Example

The component is already integrated into the profile image upload modal (`upload-image-modal.svelte`) and can be easily added to other upload workflows throughout the application.
