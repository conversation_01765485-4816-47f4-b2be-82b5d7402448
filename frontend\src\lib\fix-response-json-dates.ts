export function fixResponseJsonDates(obj: any): any {
  return Object.assign(
    { ...obj },
    "createdAt" in obj ? { createdAt: new Date(obj.createdAt as string) } : null,
    "updatedAt" in obj ? { updatedAt: new Date(obj.updatedAt as string) } : null,
    "deletedAt" in obj ? { deletedAt: obj.deletedAt ? new Date(obj.deletedAt as string) : null } : null,
  );
}

export function fixResponseJsonDatesForArray(arr: any[]): any[] {
  return arr.map(fixResponseJsonDates);
}

export function fixResponseJsonDatesWithAdditionalFields(
  additionalFields: readonly string[],
  obj: any,
): any {
  return Object.assign(
    { ...obj },

    "createdAt" in obj ? { createdAt: new Date(obj.createdAt as string) } : null,
    "updatedAt" in obj ? { updatedAt: new Date(obj.updatedAt as string) } : null,
    "deletedAt" in obj ? { deletedAt: obj.deletedAt ? new Date(obj.deletedAt as string) : null } : null,

    ...additionalFields.map((field) => {
      if (field in obj) {
        return { [field]: obj[field] ? new Date(obj[field] as string) : null };
      }

      return null;
    }),
  );
}

export function fixResponseJsonDatesWithAdditionalFieldsForArray(
  additionalFields: readonly string[],
  arr: any[],
): any[] {
  return arr.map((obj) => fixResponseJsonDatesWithAdditionalFields(additionalFields, obj));
}
