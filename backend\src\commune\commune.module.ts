import { Modu<PERSON> } from "@nestjs/common";
import { UserModule } from "src/user/user.module";
import { MinioModule } from "src/minio/minio.module";
import { CommuneCore } from "./commune.core";
import { CommuneService } from "./commune.service";
import { CommuneController } from "./commune.controller";
import { CommuneMemberService } from "./commune-member.service";
import { CommuneInvitationService } from "./commune-invitation.service";
import { CommuneJoinRequestService } from "./commune-join-request.service";

@Module({
    imports: [UserModule, MinioModule],
    controllers: [CommuneController],
    providers: [
        CommuneCore,
        CommuneService,
        CommuneMemberService,
        CommuneInvitationService,
        CommuneJoinRequestService,
    ],
    exports: [
        CommuneCore,
        CommuneService,
        CommuneMemberService,
        CommuneInvitationService,
        CommuneJoinRequestService,
    ],
})
export class CommuneModule {}
