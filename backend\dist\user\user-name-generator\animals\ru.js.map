{"version": 3, "file": "ru.js", "sourceRoot": "", "sources": ["../../../../src/user/user-name-generator/animals/ru.ts"], "names": [], "mappings": ";;;AAmGA,0CAEC;AArGY,QAAA,KAAK,GAAsB;IACpC,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,YAAY,EAAE,CAAC,CAAC;IACjB,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,eAAe,EAAE,CAAC,CAAC;IACpB,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,KAAK,EAAE,CAAC,CAAC;IACV,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACrB,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,IAAI,EAAE,CAAC,CAAC;IACT,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,eAAe,EAAE,CAAC,CAAC;IACpB,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,eAAe,EAAE,CAAC,CAAC;IACpB,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,eAAe,EAAE,CAAC,CAAC;IACpB,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,aAAa,EAAE,CAAC,CAAC;IAClB,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,SAAS,EAAE,CAAC,CAAC;IACd,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,gBAAgB,EAAE,CAAC,CAAC;IACrB,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,0BAA0B,EAAE,CAAC,CAAC;IAC/B,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,QAAQ,EAAE,CAAC,CAAC;IACb,CAAC,OAAO,EAAE,CAAC,CAAC;IACZ,CAAC,UAAU,EAAE,CAAC,CAAC;IACf,CAAC,MAAM,EAAE,CAAC,CAAC;IACX,CAAC,KAAK,EAAE,CAAC,CAAC;IACV,CAAC,WAAW,EAAE,CAAC,CAAC;IAChB,CAAC,IAAI,EAAE,CAAC,CAAC;IACT,CAAC,OAAO,EAAE,CAAC,CAAC;CACf,CAAC;AAEF,SAAgB,eAAe,CAAC,OAAe;IAC3C,OAAO,aAAK,CAAC,OAAO,CAAE,CAAC;AAC3B,CAAC"}