<script lang="ts">
  import type { LayoutData } from "./$types";
  import type { Snippet } from "svelte";

  interface Props {
    data: LayoutData;
    children: Snippet;
  }

  const { data, children }: Props = $props();
</script>

<div class="admin-layout">
  <div class="container-fluid">
    <div class="row">
      <!-- Sidebar -->
      <nav class="col-md-3 col-lg-2 d-md-block bg-light sidebar">
        <div class="position-sticky pt-3">
          <div class="sidebar-header mb-3">
            <h5 class="text-primary">
              <i class="bi bi-gear-fill me-2"></i>
              Admin Panel
            </h5>
            <small class="text-muted">Welcome, {data.me.email}</small>
          </div>

          <ul class="nav flex-column">
            <li class="nav-item">
              <a class="nav-link" href="/admin" data-sveltekit-preload-data="hover">
                <i class="bi bi-house-door me-2"></i>
                Dashboard
              </a>
            </li>
            <li class="nav-item">
              <a class="nav-link" href="/admin/invites" data-sveltekit-preload-data="hover">
                <i class="bi bi-envelope me-2"></i>
                User Invites
              </a>
            </li>
          </ul>
        </div>
      </nav>

      <!-- Main content -->
      <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
        <div class="pt-3">
          {@render children()}
        </div>
      </main>
    </div>
  </div>
</div>

<style>
  .admin-layout {
    min-height: 100vh;
  }

  .sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, 0.1);
  }

  .sidebar-header {
    padding: 0 1rem;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 1rem;
  }

  .nav-link {
    color: #333;
    border-radius: 0.375rem;
    margin: 0.125rem 0.5rem;
    padding: 0.5rem 0.75rem;
  }

  .nav-link:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, 0.1);
  }

  /* SvelteKit automatically adds this class to active links */
  :global(.nav-link[aria-current="page"]) {
    color: #0d6efd !important;
    background-color: rgba(13, 110, 253, 0.1) !important;
    font-weight: 500;
  }

  @media (max-width: 767.98px) {
    .sidebar {
      position: static;
      padding: 0;
    }

    main {
      margin-left: 0 !important;
    }
  }
</style>
