export declare function pad2zeros(value: number): string;
export declare class NewCalendarDate extends Date {
    static readonly MS_PER_DAY: number;
    static readonly DAYS_PER_MONTH = 28;
    getIsLeapYear(year: number): boolean;
    getDayOfYear(year?: number): number;
    getDayPosition(dayOfYear: number): {
        month: number;
        day: number;
    };
    getParsed(): {
        year: number;
        month: number;
        day: number;
    };
    toString(): string;
    toISOString(): string;
}
