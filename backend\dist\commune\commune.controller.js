"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneController = void 0;
const api_1 = require("@commune/api");
const common_1 = require("@nestjs/common");
const zod_1 = require("../zod");
const acrpc_1 = require("../acrpc");
const platform_express_1 = require("@nestjs/platform-express");
const current_user_decorator_1 = require("../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../auth/http/session-auth.guard");
const commune_service_1 = require("./commune.service");
const commune_member_service_1 = require("./commune-member.service");
const commune_invitation_service_1 = require("./commune-invitation.service");
const commune_join_request_service_1 = require("./commune-join-request.service");
let CommuneController = class CommuneController {
    constructor(communeService, communeMemberService, communeInvitationService, communeJoinRequestService) {
        this.communeService = communeService;
        this.communeMemberService = communeMemberService;
        this.communeInvitationService = communeInvitationService;
        this.communeJoinRequestService = communeJoinRequestService;
        const acrpcServer = (0, acrpc_1.getServer)();
        acrpcServer.register({
            commune: {
                transferHeadStatus: {
                    post: (input, metadata) => this.communeService.transferHeadStatus(input, metadata.user),
                },
                list: {
                    get: (input, metadata) => this.communeService.getCommunes(input, metadata?.user ?? null),
                },
                post: (input, metadata) => this.communeService.createCommune(input, metadata.user),
                patch: (input, metadata) => this.communeService.updateCommune(input, metadata.user),
                delete: (input, metadata) => this.communeService.deleteCommune(input, metadata.user),
                member: {
                    list: {
                        get: (input) => this.communeMemberService.getCommuneMembers(input),
                    },
                    delete: (input, metadata) => this.communeMemberService.deleteCommuneMember(input, metadata.user),
                },
                invitation: {
                    list: {
                        get: (input, metadata) => this.communeInvitationService.getInvitations(input, metadata.user),
                    },
                    post: (input, metadata) => this.communeInvitationService.createInvitation(input, metadata.user),
                    delete: (input, metadata) => this.communeInvitationService.deleteInvitation(input, metadata.user),
                    accept: {
                        post: (input, metadata) => this.communeInvitationService.acceptInvitation(input, metadata.user),
                    },
                    reject: {
                        post: (input, metadata) => this.communeInvitationService.rejectInvitation(input, metadata.user),
                    },
                },
                joinRequest: {
                    list: {
                        get: (input, metadata) => this.communeJoinRequestService.getJoinRequests(input, metadata.user),
                    },
                    post: (input, metadata) => this.communeJoinRequestService.createJoinRequest(input, metadata.user),
                    delete: (input, metadata) => this.communeJoinRequestService.deleteJoinRequest(input, metadata.user),
                    accept: {
                        post: (input, metadata) => this.communeJoinRequestService.acceptJoinRequest(input, metadata.user),
                    },
                    reject: {
                        post: (input, metadata) => this.communeJoinRequestService.rejectJoinRequest(input, metadata.user),
                    },
                },
            },
        });
    }
    async updateCommuneImage(id, user, file) {
        if (!file) {
            throw new common_1.BadRequestException("No file uploaded");
        }
        await this.communeService.updateCommuneImage(id, file, user);
    }
    async deleteCommuneImage(id, user) {
        await this.communeService.deleteCommuneImage(id, user);
    }
};
exports.CommuneController = CommuneController;
__decorate([
    (0, common_1.Put)(":id/image"),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({
                maxSize: api_1.Consts.MAX_IMAGE_FILE_SIZE,
            }),
            new common_1.FileTypeValidator({
                fileType: api_1.Consts.ALLOWED_IMAGE_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "updateCommuneImage", null);
__decorate([
    (0, common_1.Delete)(":id/image"),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object]),
    __metadata("design:returntype", Promise)
], CommuneController.prototype, "deleteCommuneImage", null);
exports.CommuneController = CommuneController = __decorate([
    (0, common_1.Controller)("commune"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [commune_service_1.CommuneService,
        commune_member_service_1.CommuneMemberService,
        commune_invitation_service_1.CommuneInvitationService,
        commune_join_request_service_1.CommuneJoinRequestService])
], CommuneController);
//# sourceMappingURL=commune.controller.js.map