<script lang="ts">
  import type { Common } from "@commune/api";

  import { changeLocale } from "$lib";
  import { onMount } from "svelte";

  interface Props {
    currentLocale: Common.WebsiteLocale | null;
  }

  const { currentLocale }: Props = $props();

  let isOpen = $state(false);
  let dropdownRef: HTMLDivElement;

  // Close dropdown when clicking outside
  onMount(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef && !dropdownRef.contains(event.target as Node)) {
        isOpen = false;
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  });

  function handleLocaleChange(newLocale: Common.WebsiteLocale | null) {
    changeLocale(currentLocale, newLocale);
    isOpen = false;
  }

  function getLocaleDisplayText(locale: Common.WebsiteLocale | null): string {
    switch (locale) {
      case "en":
        return "English";
      case "ru":
        return "Русский";
      case null:
        return "Auto";
      default:
        return "Auto";
    }
  }

  function toggleDropdown() {
    isOpen = !isOpen;
  }
</script>

<div class="dropdown mx-2" bind:this={dropdownRef}>
  <button
    class="btn btn-outline-secondary btn-sm dropdown-toggle d-flex align-items-center justify-content-center"
    style="width: 110px; min-width: 110px;"
    type="button"
    id="locale-dropdown"
    onclick={toggleDropdown}
    aria-expanded={isOpen}
  >
    <i class="bi bi-globe me-1"></i>
    {getLocaleDisplayText(currentLocale)}
  </button>

  <ul class={`dropdown-menu ${isOpen ? "show" : ""}`} aria-labelledby="locale-dropdown">
    <li>
      <button
        class={`dropdown-item ${currentLocale === "en" ? "active" : ""}`}
        onclick={() => handleLocaleChange("en")}
      >
        <i class="bi bi-translate me-2"></i>
        English
      </button>
    </li>
    <li>
      <button
        class={`dropdown-item ${currentLocale === "ru" ? "active" : ""}`}
        onclick={() => handleLocaleChange("ru")}
      >
        <i class="bi bi-translate me-2"></i>
        Русский
      </button>
    </li>
    <li>
      <button
        class={`dropdown-item ${currentLocale === null ? "active" : ""}`}
        onclick={() => handleLocaleChange(null)}
      >
        <i class="bi bi-globe me-2"></i>
        Auto
      </button>
    </li>
  </ul>
</div>
