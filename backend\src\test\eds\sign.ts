import path from "node:path";
import fs from "node:fs/promises";
import { createSign, createPrivate<PERSON><PERSON> } from "node:crypto";

const root = path.join(process.cwd(), "src/test/eds");

const PRIVATE_KEY_PASSPHRASE = "secret";

async function sign(data: <PERSON>uffer, privateKey: Buffer) {
    const signer = createSign("rsa-sha256");

    signer.update(data);

    const privateKeyObject = createPrivateKey({
        key: privateKey,
        type: "pkcs8",
        format: "pem",
        passphrase: PRIVATE_KEY_PASSPHRASE,
    });

    return signer.sign(privateKeyObject);
}

(async () => {
    const documentName = process.argv[2];
    const privateKeyName = process.argv[3];

    if (!documentName) {
        throw new Error("Document name is required.");
    }

    if (!privateKeyName) {
        throw new Error("Private key name is required.");
    }

    const document = await fs.readFile(path.join(root, documentName));
    const privateKey = await fs.readFile(
        path.join(root, `${privateKeyName}-private.pem`),
    );

    const signature = await sign(document, privateKey);

    await fs.writeFile(path.join(root, `${documentName}.sig`), signature);
})();
