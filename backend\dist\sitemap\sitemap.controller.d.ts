import { SitemapService } from "./sitemap.service";
export declare class SitemapController {
    private readonly sitemapService;
    private readonly logger;
    constructor(sitemapService: SitemapService);
    getSitemapGenerationData(key: string): Promise<{
        communeIds: string[];
        reactorPostIds: string[];
        reactorHubIds: string[];
        reactorCommunityIds: string[];
    }>;
}
