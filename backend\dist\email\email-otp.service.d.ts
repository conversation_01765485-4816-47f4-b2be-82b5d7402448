import { ConfigService } from "src/config/config.service";
import { PrismaService } from "src/prisma/prisma.service";
type CheckEmailOtpDto = {
    email: string;
    otp: string;
};
type CreateEmailOtpDto = {
    email: string;
    otp: string;
    ipAddress: string | null;
    userAgent: string | null;
};
type DeleteEmailOtpDto = {
    id: string;
};
export declare class EmailOtpService {
    private readonly configService;
    private readonly prisma;
    constructor(configService: ConfigService, prisma: PrismaService);
    create(createUserOtpDto: CreateEmailOtpDto): Promise<{
        otp: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        email: string;
        ipAddress: string | null;
        userAgent: string | null;
        expiresAt: Date;
    }>;
    check(checkUserOtpDto: CheckEmailOtpDto): Promise<{
        otp: string;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        email: string;
        ipAddress: string | null;
        userAgent: string | null;
        expiresAt: Date;
    }>;
    softDelete(deleteUserOtpDto: DeleteEmailOtpDto): Promise<void>;
    delete(deleteUserOtpDto: DeleteEmailOtpDto): Promise<void>;
}
export {};
