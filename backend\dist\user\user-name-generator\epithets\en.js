"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WORDS = void 0;
exports.getRandomEpithet = getRandomEpithet;
exports.WORDS = [
    "abiding",
    "admirable",
    "adventurous",
    "agile",
    "beautiful",
    "benevolent",
    "blissful",
    "bold",
    "brave",
    "calm",
    "charismatic",
    "cheerful",
    "clever",
    "compassionate",
    "curious",
    "daring",
    "dauntless",
    "devoted",
    "diligent",
    "dynamic",
    "eager",
    "earnest",
    "eloquent",
    "enchanting",
    "energetic",
    "faithful",
    "fearless",
    "fervent",
    "frank",
    "gallant",
    "generous",
    "gentle",
    "glowing",
    "graceful",
    "happy",
    "helpful",
    "heroic",
    "honest",
    "imaginative",
    "ingenious",
    "inspiring",
    "jolly",
    "jovial",
    "joyful",
    "judicious",
    "keen",
    "kind",
    "kindhearted",
    "knowledgeable",
    "lofty",
    "loyal",
    "lucky",
    "luminous",
    "magnanimous",
    "mighty",
    "mindful",
    "nimble",
    "noble",
    "nurturing",
    "observant",
    "openhearted",
    "optimistic",
    "passionate",
    "patient",
    "peaceful",
    "playful",
    "quick",
    "quirky",
    "radiant",
    "reflective",
    "robust",
    "serene",
    "steadfast",
    "tenacious",
    "thoughtful",
    "trustworthy",
    "unknown",
    "upbeat",
    "uplifting",
    "valiant",
    "vibrant",
    "watchful",
    "wise",
    "witty",
    "xenial",
    "xenodochial",
    "youthful",
    "zealous",
];
function getRandomEpithet(variant) {
    return exports.WORDS[variant];
}
//# sourceMappingURL=en.js.map