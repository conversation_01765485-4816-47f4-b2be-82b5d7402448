-- MANUAL
CREATE EXTENSION IF NOT EXISTS ltree;


-- <PERSON><PERSON><PERSON>num
CREATE TYPE "commune_member_type" AS ENUM ('user');

-- CreateEnum
CREATE TYPE "commune_invitation_status" AS ENUM ('pending', 'accepted', 'rejected', 'expired');

-- CreateEnum
CREATE TYPE "commune_join_request_status" AS ENUM ('pending', 'accepted', 'rejected');

-- CreateEnum
CREATE TYPE "locale" AS ENUM ('en', 'ru');

-- CreateEnum
CREATE TYPE "merchandise_status" AS ENUM ('hidden', 'shown');

-- CreateEnum
CREATE TYPE "UserRatingEntityType" AS ENUM ('post', 'comment');

-- CreateEnum
CREATE TYPE "reactor_entity_type" AS ENUM ('post', 'comment');

-- CreateEnum
CREATE TYPE "reactor_rating_type" AS ENUM ('like', 'dislike');

-- CreateEnum
CREATE TYPE "user_role" AS ENUM ('admin', 'moderator', 'user');

-- CreateEnum
CREATE TYPE "vote_actor_type" AS ENUM ('commune', 'user');

-- CreateTable
CREATE TABLE "communes" (
    "id" TEXT NOT NULL,
    "image_id" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "communes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "commune_members" (
    "id" TEXT NOT NULL,
    "commune_id" TEXT NOT NULL,
    "actorType" "commune_member_type" NOT NULL,
    "actor_id" TEXT NOT NULL,
    "is_head" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "commune_members_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "commune_invitations" (
    "id" TEXT NOT NULL,
    "commune_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "expires_at" TIMESTAMPTZ(3) NOT NULL,
    "status" "commune_invitation_status" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "commune_invitations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "commune_join_requests" (
    "id" TEXT NOT NULL,
    "commune_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "status" "commune_join_request_status" NOT NULL DEFAULT 'pending',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "commune_join_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "images" (
    "id" TEXT NOT NULL,
    "url" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "images_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "localizations" (
    "id" TEXT NOT NULL,
    "key" TEXT NOT NULL,
    "locale" "locale" NOT NULL,
    "value" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "localizations_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "merchandises" (
    "id" TEXT NOT NULL,
    "category" TEXT NOT NULL,
    "price" INTEGER NOT NULL,
    "status" "merchandise_status" NOT NULL DEFAULT 'hidden',
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "merchandises_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_ratings" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "entity_type" "UserRatingEntityType" NOT NULL,
    "entity_id" TEXT NOT NULL,
    "value" SMALLINT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_ratings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_karma_spendable_points" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "points" SMALLINT NOT NULL DEFAULT 0,

    CONSTRAINT "user_karma_spendable_points_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_karma_given_points" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "quantity" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_karma_given_points_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_feedbacks" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "value" SMALLINT NOT NULL,
    "is_anonymous" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_feedbacks_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_posts" (
    "id" TEXT NOT NULL,
    "author_id" TEXT NOT NULL,
    "hub_id" TEXT,
    "community_id" TEXT,
    "is_anonymous" BOOLEAN NOT NULL DEFAULT false,
    "anonimity_reason" TEXT,
    "delete_reason" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_posts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_post_internal_numbers" (
    "id" TEXT NOT NULL,
    "post_id" TEXT NOT NULL,
    "internal_number" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "reactor_post_internal_numbers_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_comments" (
    "id" TEXT NOT NULL,
    "author_id" TEXT NOT NULL,
    "post_id" TEXT NOT NULL,
    "path" TEXT NOT NULL,
    "internal_number" INTEGER NOT NULL,
    "is_anonymous" BOOLEAN NOT NULL DEFAULT false,
    "anonimity_reason" TEXT,
    "delete_reason" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_comments_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_ratings" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "entity_type" "reactor_entity_type" NOT NULL,
    "entity_id" TEXT NOT NULL,
    "type" "reactor_rating_type" NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "reactor_ratings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_usefulnesses" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "entity_type" "reactor_entity_type" NOT NULL,
    "entity_id" TEXT NOT NULL,
    "value" INTEGER NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "reactor_usefulnesses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_hubs" (
    "id" TEXT NOT NULL,
    "head_user_id" TEXT NOT NULL,
    "image_id" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_hubs_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_communities" (
    "id" TEXT NOT NULL,
    "hub_id" TEXT,
    "head_user_id" TEXT NOT NULL,
    "image_id" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_communities_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "reactor_lenses" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "sql" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "reactor_lenses_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "tags" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "tags_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "users" (
    "id" TEXT NOT NULL,
    "referrer_id" TEXT,
    "email" TEXT NOT NULL,
    "role" "user_role" NOT NULL DEFAULT 'user',
    "image_id" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "users_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_titles" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "is_active" BOOLEAN NOT NULL DEFAULT false,
    "color" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "user_titles_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_otps" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "otp" TEXT NOT NULL,
    "ip_address" TEXT,
    "user_agent" TEXT,
    "expires_at" TIMESTAMPTZ(3) NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "user_otps_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_notes" (
    "id" TEXT NOT NULL,
    "source_user_id" TEXT NOT NULL,
    "target_user_id" TEXT NOT NULL,
    "text" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_notes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_invites" (
    "id" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "name" TEXT,
    "locale" "locale" NOT NULL,
    "is_used" BOOLEAN NOT NULL DEFAULT false,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,

    CONSTRAINT "user_invites_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "votings" (
    "id" TEXT NOT NULL,
    "votes_required" INTEGER NOT NULL,
    "ends_at" TIMESTAMPTZ(3) NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "votings_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "voting_options" (
    "id" TEXT NOT NULL,
    "voting_id" TEXT NOT NULL,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "voting_options_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "votes" (
    "id" TEXT NOT NULL,
    "actorType" "vote_actor_type" NOT NULL,
    "actor_id" TEXT NOT NULL,
    "voting_id" TEXT,
    "option_id" TEXT,
    "created_at" TIMESTAMPTZ(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMPTZ(3) NOT NULL,
    "deleted_at" TIMESTAMPTZ(3),

    CONSTRAINT "votes_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_commune_tags" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_commune_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_commune_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_commune_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_commune_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_commune_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_images" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_voting_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_merchandise_images" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_merchandise_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_post_images" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_post_images_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_title_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_title_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_title" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_voting_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_voting_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_option_title" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_voting_option_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_option_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_voting_option_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_tag_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_tag_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_post_title" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_post_title_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_post_body" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_post_body_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_comment_body" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_comment_body_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_hub_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_hub_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_hub_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_hub_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_community_name" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_community_name_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_community_description" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_community_description_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_karma_given_point_comment" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_karma_given_point_comment_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_user_feedback_text" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_user_feedback_text_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_merchandise_tags" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_merchandise_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_reactor_post_tags" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_reactor_post_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateTable
CREATE TABLE "_voting_tags" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_voting_tags_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "localizations_value_idx" ON "localizations"("value");

-- CreateIndex
CREATE INDEX "merchandises_category_idx" ON "merchandises"("category");

-- CreateIndex
CREATE UNIQUE INDEX "user_ratings_source_user_id_target_user_id_entity_type_enti_key" ON "user_ratings"("source_user_id", "target_user_id", "entity_type", "entity_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_karma_spendable_points_user_id_key" ON "user_karma_spendable_points"("user_id");

-- CreateIndex
CREATE INDEX "reactor_posts_author_id_idx" ON "reactor_posts"("author_id");

-- CreateIndex
CREATE INDEX "reactor_posts_hub_id_idx" ON "reactor_posts"("hub_id");

-- CreateIndex
CREATE INDEX "reactor_posts_community_id_idx" ON "reactor_posts"("community_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_post_internal_numbers_post_id_key" ON "reactor_post_internal_numbers"("post_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_comments_post_id_internal_number_key" ON "reactor_comments"("post_id", "internal_number");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_comments_post_id_path_key" ON "reactor_comments"("post_id", "path");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_ratings_entity_type_entity_id_user_id_key" ON "reactor_ratings"("entity_type", "entity_id", "user_id");

-- CreateIndex
CREATE UNIQUE INDEX "reactor_usefulnesses_entity_type_entity_id_user_id_key" ON "reactor_usefulnesses"("entity_type", "entity_id", "user_id");

-- CreateIndex
CREATE INDEX "reactor_communities_hub_id_idx" ON "reactor_communities"("hub_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "user_notes_source_user_id_target_user_id_key" ON "user_notes"("source_user_id", "target_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "user_invites_email_key" ON "user_invites"("email");

-- CreateIndex
CREATE INDEX "_commune_tags_B_index" ON "_commune_tags"("B");

-- CreateIndex
CREATE INDEX "_commune_name_B_index" ON "_commune_name"("B");

-- CreateIndex
CREATE INDEX "_commune_description_B_index" ON "_commune_description"("B");

-- CreateIndex
CREATE INDEX "_voting_images_B_index" ON "_voting_images"("B");

-- CreateIndex
CREATE INDEX "_merchandise_images_B_index" ON "_merchandise_images"("B");

-- CreateIndex
CREATE INDEX "_reactor_post_images_B_index" ON "_reactor_post_images"("B");

-- CreateIndex
CREATE INDEX "_user_name_B_index" ON "_user_name"("B");

-- CreateIndex
CREATE INDEX "_user_description_B_index" ON "_user_description"("B");

-- CreateIndex
CREATE INDEX "_user_title_name_B_index" ON "_user_title_name"("B");

-- CreateIndex
CREATE INDEX "_voting_title_B_index" ON "_voting_title"("B");

-- CreateIndex
CREATE INDEX "_voting_description_B_index" ON "_voting_description"("B");

-- CreateIndex
CREATE INDEX "_voting_option_title_B_index" ON "_voting_option_title"("B");

-- CreateIndex
CREATE INDEX "_voting_option_description_B_index" ON "_voting_option_description"("B");

-- CreateIndex
CREATE INDEX "_tag_name_B_index" ON "_tag_name"("B");

-- CreateIndex
CREATE INDEX "_reactor_post_title_B_index" ON "_reactor_post_title"("B");

-- CreateIndex
CREATE INDEX "_reactor_post_body_B_index" ON "_reactor_post_body"("B");

-- CreateIndex
CREATE INDEX "_reactor_comment_body_B_index" ON "_reactor_comment_body"("B");

-- CreateIndex
CREATE INDEX "_reactor_hub_name_B_index" ON "_reactor_hub_name"("B");

-- CreateIndex
CREATE INDEX "_reactor_hub_description_B_index" ON "_reactor_hub_description"("B");

-- CreateIndex
CREATE INDEX "_reactor_community_name_B_index" ON "_reactor_community_name"("B");

-- CreateIndex
CREATE INDEX "_reactor_community_description_B_index" ON "_reactor_community_description"("B");

-- CreateIndex
CREATE INDEX "_user_karma_given_point_comment_B_index" ON "_user_karma_given_point_comment"("B");

-- CreateIndex
CREATE INDEX "_user_feedback_text_B_index" ON "_user_feedback_text"("B");

-- CreateIndex
CREATE INDEX "_merchandise_tags_B_index" ON "_merchandise_tags"("B");

-- CreateIndex
CREATE INDEX "_reactor_post_tags_B_index" ON "_reactor_post_tags"("B");

-- CreateIndex
CREATE INDEX "_voting_tags_B_index" ON "_voting_tags"("B");

-- AddForeignKey
ALTER TABLE "communes" ADD CONSTRAINT "communes_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_members" ADD CONSTRAINT "commune_members_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_invitations" ADD CONSTRAINT "commune_invitations_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_invitations" ADD CONSTRAINT "commune_invitations_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_join_requests" ADD CONSTRAINT "commune_join_requests_commune_id_fkey" FOREIGN KEY ("commune_id") REFERENCES "communes"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "commune_join_requests" ADD CONSTRAINT "commune_join_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_ratings" ADD CONSTRAINT "user_ratings_source_user_id_fkey" FOREIGN KEY ("source_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_ratings" ADD CONSTRAINT "user_ratings_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_karma_given_points" ADD CONSTRAINT "user_karma_given_points_source_user_id_fkey" FOREIGN KEY ("source_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_karma_given_points" ADD CONSTRAINT "user_karma_given_points_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_feedbacks" ADD CONSTRAINT "user_feedbacks_source_user_id_fkey" FOREIGN KEY ("source_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_feedbacks" ADD CONSTRAINT "user_feedbacks_target_user_id_fkey" FOREIGN KEY ("target_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_posts" ADD CONSTRAINT "reactor_posts_community_id_fkey" FOREIGN KEY ("community_id") REFERENCES "reactor_communities"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_comments" ADD CONSTRAINT "reactor_comments_author_id_fkey" FOREIGN KEY ("author_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_ratings" ADD CONSTRAINT "reactor_ratings_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_usefulnesses" ADD CONSTRAINT "reactor_usefulnesses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_hubs" ADD CONSTRAINT "reactor_hubs_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_hubs" ADD CONSTRAINT "reactor_hubs_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_communities" ADD CONSTRAINT "reactor_communities_hub_id_fkey" FOREIGN KEY ("hub_id") REFERENCES "reactor_hubs"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_communities" ADD CONSTRAINT "reactor_communities_head_user_id_fkey" FOREIGN KEY ("head_user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_communities" ADD CONSTRAINT "reactor_communities_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "reactor_lenses" ADD CONSTRAINT "reactor_lenses_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_referrer_id_fkey" FOREIGN KEY ("referrer_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "users" ADD CONSTRAINT "users_image_id_fkey" FOREIGN KEY ("image_id") REFERENCES "images"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_titles" ADD CONSTRAINT "user_titles_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "voting_options" ADD CONSTRAINT "voting_options_voting_id_fkey" FOREIGN KEY ("voting_id") REFERENCES "votings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "votes" ADD CONSTRAINT "votes_voting_id_fkey" FOREIGN KEY ("voting_id") REFERENCES "votings"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "votes" ADD CONSTRAINT "votes_option_id_fkey" FOREIGN KEY ("option_id") REFERENCES "voting_options"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_tags" ADD CONSTRAINT "_commune_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_tags" ADD CONSTRAINT "_commune_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_name" ADD CONSTRAINT "_commune_name_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_name" ADD CONSTRAINT "_commune_name_B_fkey" FOREIGN KEY ("B") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_description" ADD CONSTRAINT "_commune_description_A_fkey" FOREIGN KEY ("A") REFERENCES "communes"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_commune_description" ADD CONSTRAINT "_commune_description_B_fkey" FOREIGN KEY ("B") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_images" ADD CONSTRAINT "_voting_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_images" ADD CONSTRAINT "_voting_images_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_images" ADD CONSTRAINT "_merchandise_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_images" ADD CONSTRAINT "_merchandise_images_B_fkey" FOREIGN KEY ("B") REFERENCES "merchandises"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_images" ADD CONSTRAINT "_reactor_post_images_A_fkey" FOREIGN KEY ("A") REFERENCES "images"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_images" ADD CONSTRAINT "_reactor_post_images_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_name" ADD CONSTRAINT "_user_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_name" ADD CONSTRAINT "_user_name_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_description" ADD CONSTRAINT "_user_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_description" ADD CONSTRAINT "_user_description_B_fkey" FOREIGN KEY ("B") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_title_name" ADD CONSTRAINT "_user_title_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_title_name" ADD CONSTRAINT "_user_title_name_B_fkey" FOREIGN KEY ("B") REFERENCES "user_titles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_title" ADD CONSTRAINT "_voting_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_title" ADD CONSTRAINT "_voting_title_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_description" ADD CONSTRAINT "_voting_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_description" ADD CONSTRAINT "_voting_description_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_title" ADD CONSTRAINT "_voting_option_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_title" ADD CONSTRAINT "_voting_option_title_B_fkey" FOREIGN KEY ("B") REFERENCES "voting_options"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_description" ADD CONSTRAINT "_voting_option_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_option_description" ADD CONSTRAINT "_voting_option_description_B_fkey" FOREIGN KEY ("B") REFERENCES "voting_options"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_tag_name" ADD CONSTRAINT "_tag_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_tag_name" ADD CONSTRAINT "_tag_name_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_title" ADD CONSTRAINT "_reactor_post_title_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_title" ADD CONSTRAINT "_reactor_post_title_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_body" ADD CONSTRAINT "_reactor_post_body_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_body" ADD CONSTRAINT "_reactor_post_body_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_comment_body" ADD CONSTRAINT "_reactor_comment_body_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_comment_body" ADD CONSTRAINT "_reactor_comment_body_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_name" ADD CONSTRAINT "_reactor_hub_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_name" ADD CONSTRAINT "_reactor_hub_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_hubs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_description" ADD CONSTRAINT "_reactor_hub_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_hub_description" ADD CONSTRAINT "_reactor_hub_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_hubs"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_name" ADD CONSTRAINT "_reactor_community_name_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_name" ADD CONSTRAINT "_reactor_community_name_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_communities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_description" ADD CONSTRAINT "_reactor_community_description_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_community_description" ADD CONSTRAINT "_reactor_community_description_B_fkey" FOREIGN KEY ("B") REFERENCES "reactor_communities"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_karma_given_point_comment" ADD CONSTRAINT "_user_karma_given_point_comment_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_karma_given_point_comment" ADD CONSTRAINT "_user_karma_given_point_comment_B_fkey" FOREIGN KEY ("B") REFERENCES "user_karma_given_points"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_feedback_text" ADD CONSTRAINT "_user_feedback_text_A_fkey" FOREIGN KEY ("A") REFERENCES "localizations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_user_feedback_text" ADD CONSTRAINT "_user_feedback_text_B_fkey" FOREIGN KEY ("B") REFERENCES "user_feedbacks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_tags" ADD CONSTRAINT "_merchandise_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "merchandises"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_merchandise_tags" ADD CONSTRAINT "_merchandise_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_tags" ADD CONSTRAINT "_reactor_post_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "reactor_posts"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_reactor_post_tags" ADD CONSTRAINT "_reactor_post_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_tags" ADD CONSTRAINT "_voting_tags_A_fkey" FOREIGN KEY ("A") REFERENCES "tags"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_voting_tags" ADD CONSTRAINT "_voting_tags_B_fkey" FOREIGN KEY ("B") REFERENCES "votings"("id") ON DELETE CASCADE ON UPDATE CASCADE;



-- MANUAL
CREATE INDEX idx_comments_path_ltree_gist ON reactor_comments USING gist ((path::ltree));
