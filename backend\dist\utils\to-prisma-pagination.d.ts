type PagePagination = {
    page?: number;
    size?: number;
};
type OffsetPagination = {
    limit?: number;
    offset?: number;
};
type PrismaPagination = {
    skip: number;
    take: number;
};
export declare function toPrismaPagination(data: PagePagination | OffsetPagination | undefined): PrismaPagination;
export declare function toPrismaPagination(data: {
    pagination?: PagePagination | OffsetPagination;
}): PrismaPagination;
export {};
