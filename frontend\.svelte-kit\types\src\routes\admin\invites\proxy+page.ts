// @ts-nocheck
import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  // Load initial batch of invites
  const invites = await api.user.invite.list.get(
    {
      pagination: {
        page: 1,
        size: Consts.PAGE_SIZE
      }
    },
    { fetch, ctx: { url } }
  );

  return {
    invites,
    isHasMoreInvites: invites.length === Consts.PAGE_SIZE,
  };
};
