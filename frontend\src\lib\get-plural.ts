export const getPlural = {
    en(n: number): 0 | 1 {
        if (n === 1) {
            return 0;
        }

        return 1;
    },

    ru(n: number): 0 | 1 | 2 {
        if (n % 10 === 1 && n % 100 !== 11) {
            return 0;
        }

        if (
            (n % 10 === 2 && n % 100 !== 12) ||
            (n % 10 === 3 && n % 100 !== 13) ||
            (n % 10 === 4 && n % 100 !== 14)
        ) {
            return 1;
        }

        return 2;
    },
};
