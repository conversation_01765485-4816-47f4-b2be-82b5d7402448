"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RatingKarmaService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RatingKarmaService = void 0;
const schedule_1 = require("@nestjs/schedule");
const common_1 = require("@nestjs/common");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
const utils_1 = require("../utils");
const WEEK_IN_MS = 7 * 24 * 60 * 60 * 1000;
let RatingKarmaService = RatingKarmaService_1 = class RatingKarmaService {
    constructor(prisma) {
        this.prisma = prisma;
        this.logger = new common_1.Logger(RatingKarmaService_1.name);
    }
    async getKarmaPoints(input) {
        return await this.prisma.userKarmaGivenPoint.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: {
                targetUserId: input.userId,
            },
            include: {
                comment: true,
                sourceUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
            },
        });
    }
    async spendKarmaPoint(data, user) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("must_be_admin_or_source_user"));
            }
            if (data.sourceUserId === data.targetUserId) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("source_and_target_users_must_differ"));
            }
            if (data.quantity !== 1 && data.quantity !== -1) {
                throw new common_1.ForbiddenException((0, errors_1.getError)("must_be_admin_or_use_only_one_point"));
            }
        }
        const id = await this.prisma.$transaction(async (trx) => {
            let userKarmaSpendablePointId = null;
            let availablePoints = Number.MAX_SAFE_INTEGER;
            if (!user.isAdmin) {
                const userKarmaSpendablePoint = await trx.userKarmaSpendablePoint.findUnique({
                    where: {
                        userId: data.sourceUserId,
                    },
                });
                if (!userKarmaSpendablePoint) {
                    throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_have_at_least_one_spendable_point"));
                }
                userKarmaSpendablePointId = userKarmaSpendablePoint.id;
                availablePoints = userKarmaSpendablePoint.points;
            }
            if (availablePoints < data.quantity) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_have_at_least_one_spendable_point"));
            }
            const { id } = await trx.userKarmaGivenPoint.create({
                data: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    quantity: data.quantity,
                    comment: {
                        create: (0, utils_1.toPrismaLocalizations)(data.comment, "comment"),
                    },
                },
            });
            if (userKarmaSpendablePointId) {
                await trx.userKarmaSpendablePoint.update({
                    where: {
                        id: userKarmaSpendablePointId,
                    },
                    data: {
                        points: {
                            decrement: data.quantity,
                        },
                    },
                });
            }
            return id;
        });
        return { id };
    }
    async addMissingKarmaPoints() {
        const weekAgo = new Date(Date.now() - WEEK_IN_MS);
        const staleSpendablePoints = await this.prisma.userKarmaSpendablePoint.findMany({
            where: {
                lastAddedAt: {
                    lte: weekAgo,
                },
            },
        });
        const howMuchPointsToAdd = staleSpendablePoints.map(({ userId, lastAddedAt }) => {
            const timePassed = Date.now() - lastAddedAt.getTime();
            const pointsToAdd = Math.floor(timePassed / WEEK_IN_MS);
            const newLastAddedAt = new Date(lastAddedAt.getTime() + pointsToAdd * WEEK_IN_MS);
            return { userId, points: pointsToAdd, newLastAddedAt };
        });
        await this.prisma.$transaction(async (trx) => {
            await Promise.all(howMuchPointsToAdd.map(({ userId, points, newLastAddedAt }) => trx.userKarmaSpendablePoint.update({
                where: { userId },
                data: {
                    points: { increment: points },
                    lastAddedAt: newLastAddedAt,
                },
            })));
        });
    }
};
exports.RatingKarmaService = RatingKarmaService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], RatingKarmaService.prototype, "addMissingKarmaPoints", null);
exports.RatingKarmaService = RatingKarmaService = RatingKarmaService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], RatingKarmaService);
//# sourceMappingURL=rating-karma.service.js.map