"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneInvitationService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const errors_1 = require("../common/errors");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const commune_core_1 = require("./commune.core");
const WEEK = 7 * 24 * 60 * 60 * 1000;
const INVITATION_EXPIRATION_TIME = WEEK;
let CommuneInvitationService = class CommuneInvitationService {
    constructor(prisma, communeCore) {
        this.prisma = prisma;
        this.communeCore = communeCore;
    }
    async getInvitations(input, user) {
        const invitations = await this.prisma.communeInvitation.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: Object.assign({ deletedAt: null }, input.communeId
                ? { communeId: input.communeId }
                : { userId: user.id }),
            orderBy: {
                createdAt: "desc",
            },
        });
        return invitations;
    }
    async createInvitation(input, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: input.communeId },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
            }
        }
        if (this.communeCore.isMember(commune, input.userId)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        const existingInvitation = await this.prisma.communeInvitation.findFirst({
            where: {
                communeId: input.communeId,
                userId: input.userId,
                status: client_1.CommuneInvitationStatus.pending,
                expiresAt: {
                    gt: new Date(),
                },
                deletedAt: null,
            },
        });
        if (existingInvitation) {
            return existingInvitation;
        }
        await this.communeCore.checkIsUserReachedMaxCommunesLimit(input.userId);
        return await this.prisma.communeInvitation.create({
            data: {
                communeId: input.communeId,
                userId: input.userId,
                expiresAt: new Date(Date.now() + INVITATION_EXPIRATION_TIME),
            },
        });
    }
    async deleteInvitation(input, user) {
        const invitation = await this.prisma.communeInvitation.findUniqueOrThrow({
            where: { id: input.id },
        });
        if (invitation.status !== client_1.CommuneInvitationStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("invitation_must_be_pending"));
        }
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: invitation.communeId },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
            }
        }
        await this.prisma.communeInvitation.update({
            where: { id: input.id },
            data: {
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async acceptInvitation(input, currentUser) {
        const invitation = await this.prisma.communeInvitation.findUniqueOrThrow({
            where: { id: input.id },
        });
        if (invitation.status !== client_1.CommuneInvitationStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("invitation_must_be_pending"));
        }
        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_invitation_target"));
        }
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: invitation.communeId },
            include: {
                members: true,
            },
        });
        if (this.communeCore.isMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        if (!currentUser.isAdmin) {
            await this.communeCore.checkIsUserReachedMaxCommunesLimit(invitation.userId);
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: invitation.communeId,
                    actorType: client_1.CommuneMemberType.user,
                    actorId: currentUser.id,
                    isHead: false,
                },
            });
            await trx.communeInvitation.update({
                where: { id: input.id },
                data: {
                    status: client_1.CommuneInvitationStatus.accepted,
                },
            });
        });
        return true;
    }
    async rejectInvitation(input, currentUser) {
        const invitation = await this.prisma.communeInvitation.findUniqueOrThrow({
            where: { id: input.id },
        });
        if (invitation.status !== client_1.CommuneInvitationStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("invitation_must_be_pending"));
        }
        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_invitation_target"));
        }
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: invitation.communeId },
            include: {
                members: true,
            },
        });
        if (this.communeCore.isMember(commune, currentUser.id)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        await this.prisma.communeInvitation.update({
            where: { id: input.id },
            data: {
                status: client_1.CommuneInvitationStatus.rejected,
            },
        });
        return true;
    }
};
exports.CommuneInvitationService = CommuneInvitationService;
exports.CommuneInvitationService = CommuneInvitationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        commune_core_1.CommuneCore])
], CommuneInvitationService);
//# sourceMappingURL=commune-invitation.service.js.map