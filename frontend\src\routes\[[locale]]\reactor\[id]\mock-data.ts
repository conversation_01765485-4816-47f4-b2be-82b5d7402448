export interface Post {
  id: string;
  rating: number;
  usefulness: number;
  title: string;
  content: string;
  tags: string[];
  commentCount: number;
  author: {
    id: string;
    name: string;
    avatar: string;
  };
  createdAt: string;
}

export interface Comment {
  id: string;
  postId: string;
  parentId: string | null;
  author: {
    id: string;
    name: string;
    avatar: string;
  };
  content: string;
  rating: number;
  createdAt: string;
  children: Comment[];
  expanded?: boolean;
}

// Mock post data
export const mockPost: Post = {
  id: "1",
  rating: 142,
  usefulness: 4,
  title: "Understanding Svelte 5 Runes: A Comprehensive Guide",
  content: `Svelte 5 introduces a revolutionary concept called "runes" that fundamentally changes how reactivity works in the framework. In this comprehensive guide, I'll explain what runes are, how they work, and why they're such a game-changer for Svelte developers.

Runes are special $ prefixed variables and functions that unlock new capabilities. The $state rune creates a reactive variable. When the variable changes, any part of the UI that depends on it will update. This is similar to Svelte 3's reactive statements, but more flexible and powerful.

The $derived rune creates a value that depends on other reactive values. It's recalculated whenever those dependencies change. This is similar to Svelte 3's derived stores, but with a cleaner syntax.

The $effect rune runs a function whenever its dependencies change. This is similar to Svelte 3's $: reactive statements, but more explicit and easier to reason about.

The $props rune is used to define component props in a type-safe way. It's a replacement for the export let syntax in Svelte 3.

These new primitives make Svelte code more explicit, easier to understand, and more powerful. They also enable new patterns that weren't possible before, like fine-grained reactivity and better TypeScript integration.`,
  tags: ["svelte", "javascript", "frontend", "programming", "web-development"],
  commentCount: 8,
  author: {
    id: "user1",
    name: "Jane Developer",
    avatar: "/images/avatars/user1.jpg"
  },
  createdAt: "2023-06-15T14:23:45Z"
};

// Mock comments data with nested structure
export const mockComments: Comment[] = [
  {
    id: "comment1",
    postId: "1",
    parentId: null,
    author: {
      id: "user2",
      name: "Alex Smith",
      avatar: "/images/avatars/user2.jpg"
    },
    content: "This is a fantastic explanation of Svelte 5 runes! I've been trying to understand them for a while, and this really cleared things up for me.",
    rating: 24,
    createdAt: "2023-06-15T15:30:12Z",
    children: [
      {
        id: "comment2",
        postId: "1",
        parentId: "comment1",
        author: {
          id: "user3",
          name: "Sam Johnson",
          avatar: "/images/avatars/user3.jpg"
        },
        content: "I agree! The explanation of $derived vs the old reactive statements was particularly helpful.",
        rating: 12,
        createdAt: "2023-06-15T16:05:22Z",
        children: [
          {
            id: "comment3",
            postId: "1",
            parentId: "comment2",
            author: {
              id: "user1",
              name: "Jane Developer",
              avatar: "/images/avatars/user1.jpg"
            },
            content: "Thanks for the feedback! I'm glad it was helpful. I spent a lot of time trying to make the distinctions clear.",
            rating: 8,
            createdAt: "2023-06-15T16:30:45Z",
            children: [
              {
                id: "comment4",
                postId: "1",
                parentId: "comment3",
                author: {
                  id: "user4",
                  name: "Taylor Wilson",
                  avatar: "/images/avatars/user4.jpg"
                },
                content: "Jane, do you have any examples of when you'd use $derived vs $effect? I'm still a bit confused about that distinction.",
                rating: 5,
                createdAt: "2023-06-15T17:15:33Z",
                children: [
                  {
                    id: "comment5",
                    postId: "1",
                    parentId: "comment4",
                    author: {
                      id: "user1",
                      name: "Jane Developer",
                      avatar: "/images/avatars/user1.jpg"
                    },
                    content: "Great question! Use $derived when you want to compute a value based on other reactive values. Use $effect when you want to run side effects (like fetching data or updating the DOM) when reactive values change.",
                    rating: 15,
                    createdAt: "2023-06-15T17:45:12Z",
                    children: [
                      {
                        id: "comment6",
                        postId: "1",
                        parentId: "comment5",
                        author: {
                          id: "user4",
                          name: "Taylor Wilson",
                          avatar: "/images/avatars/user4.jpg"
                        },
                        content: "That makes perfect sense! Thanks for clarifying.",
                        rating: 3,
                        createdAt: "2023-06-15T18:02:45Z",
                        children: [
                          {
                            id: "comment7",
                            postId: "1",
                            parentId: "comment6",
                            author: {
                              id: "user5",
                              name: "Jordan Lee",
                              avatar: "/images/avatars/user5.jpg"
                            },
                            content: "This thread has been super helpful. I'm just getting started with Svelte 5 and this explanation of runes is exactly what I needed.",
                            rating: 7,
                            createdAt: "2023-06-15T19:23:18Z",
                            children: [
                              {
                                id: "comment8",
                                postId: "1",
                                parentId: "comment7",
                                author: {
                                  id: "user6",
                                  name: "Casey Brown",
                                  avatar: "/images/avatars/user6.jpg"
                                },
                                content: "I'm still on Svelte 4. Is it worth upgrading to Svelte 5 for these runes features?",
                                rating: 2,
                                createdAt: "2023-06-15T20:15:42Z",
                                children: [
                                  {
                                    id: "comment9",
                                    postId: "1",
                                    parentId: "comment8",
                                    author: {
                                      id: "user1",
                                      name: "Jane Developer",
                                      avatar: "/images/avatars/user1.jpg"
                                    },
                                    content: "It depends on your project needs. Svelte 5 is still in development, so for production apps, you might want to wait. But for new projects or if you want to learn the latest features, it's definitely worth exploring!",
                                    rating: 9,
                                    createdAt: "2023-06-15T21:05:33Z",
                                    children: [
                                      {
                                        id: "comment10",
                                        postId: "1",
                                        parentId: "comment9",
                                        author: {
                                          id: "user6",
                                          name: "Casey Brown",
                                          avatar: "/images/avatars/user6.jpg"
                                        },
                                        content: "Thanks for the advice! I'll probably start experimenting with it in a side project first.",
                                        rating: 4,
                                        createdAt: "2023-06-15T21:30:15Z",
                                        children: []
                                      }
                                    ]
                                  }
                                ]
                              }
                            ]
                          }
                        ]
                      }
                    ]
                  }
                ]
              }
            ]
          }
        ]
      }
    ]
  },
  {
    id: "comment11",
    postId: "1",
    parentId: null,
    author: {
      id: "user7",
      name: "Robin Garcia",
      avatar: "/images/avatars/user7.jpg"
    },
    content: "I'm not sure I understand the benefit of runes over the previous reactive syntax. It seems more verbose to me.",
    rating: -3,
    createdAt: "2023-06-16T09:12:33Z",
    children: [
      {
        id: "comment12",
        postId: "1",
        parentId: "comment11",
        author: {
          id: "user1",
          name: "Jane Developer",
          avatar: "/images/avatars/user1.jpg"
        },
        content: "That's a fair point! The main benefits are better TypeScript support, more explicit reactivity, and more consistent behavior. But there is a learning curve, and the syntax is a bit more verbose.",
        rating: 18,
        createdAt: "2023-06-16T10:05:22Z",
        children: []
      }
    ]
  }
];
