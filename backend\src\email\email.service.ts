import { Common } from "@commune/api";
import { Injectable, OnModuleInit } from "@nestjs/common";
import { createTransport, Transporter } from "nodemailer";
import SMTPTransport from "nodemailer/lib/smtp-transport";
import { ConfigService } from "src/config/config.service";
import { generateInviteText } from "./invite-text";

type SendEmailDto = {
    transporter: Transporter;

    from: string;
    to: string[];
    subject: string;
    text?: string;
    html?: string;
};

@Injectable()
export class EmailService implements OnModuleInit {
    private otpTransporter: Transporter;
    private inviteTransporter: Transporter;

    constructor(private readonly configService: ConfigService) {}

    onModuleInit() {
        const options = {
            host: this.configService.config.email.host,
            secure: false,
            port: this.configService.config.email.port,

            tls: {
                rejectUnauthorized:
                    this.configService.config.email.rejectUnauthorized,
            },
        } satisfies SMTPTransport.Options;

        const otpOptions = {
            ...options,

            auth: {
                user: this.configService.config.email.otpUser,
                pass: this.configService.config.email.otpPassword,
            },
        } satisfies SMTPTransport.Options;

        const inviteOptions = {
            ...options,

            auth: {
                user: this.configService.config.email.inviteUser,
                pass: this.configService.config.email.invitePassword,
            },
        } satisfies SMTPTransport.Options;

        this.otpTransporter = createTransport(otpOptions);
        this.inviteTransporter = createTransport(inviteOptions);

        // await Promise.all([
        //     this.configService.config.email.disableOtpEmails
        //         ? null
        //         : this.otpTransporter.verify(),

        //     this.configService.config.email.disableInviteEmails
        //         ? null
        //         : this.inviteTransporter.verify(),
        // ]);
    }

    joinAddress(sender: string, domain: string) {
        return `${sender}@${domain}`;
    }

    async send(dto: SendEmailDto) {
        if (this.configService.config.email.disableAllEmails) {
            return false;
        }

        try {
            await dto.transporter.sendMail({
                from: dto.from,
                to: dto.to,
                subject: dto.subject,
                text: dto.text,
                html: dto.html,
            });

            return true;
        } catch (e) {
            if (!this.configService.config.email.ignoreErrors) {
                throw e;
            }

            return false;
        }
    }

    async sendOtp(dto: { to: string; otp: string }) {
        if (this.configService.config.email.disableOtpEmails) {
            return false;
        }

        return await this.send({
            transporter: this.otpTransporter,
            from: this.configService.config.email.otpUser,
            to: [dto.to],
            subject: `${this.configService.config.instance.name} - OTP`,
            text: `Your OTP is ${dto.otp}.`,
        });
    }

    async sendInvite(dto: {
        to: string;
        name: string | null;
        locale: Common.LocalizationLocale;
    }) {
        if (this.configService.config.email.disableInviteEmails) {
            return false;
        }

        const subject: Record<Common.LocalizationLocale, string> = {
            ru: "Коммуна — приглашение",
            en: "Commune — invite",
        };

        return await this.send({
            transporter: this.inviteTransporter,
            from: this.configService.config.email.inviteUser,
            to: [dto.to],
            subject: subject[dto.locale],
            html: generateInviteText(dto.name, dto.locale),
        });
    }
}
