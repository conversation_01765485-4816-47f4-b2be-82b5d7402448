/**
 * Calculates array difference.
 * Iterates both sides and uses `Set` to remove duplicates.
 */
export function symmetricArrayDiff<T>(a: T[], b: T[]): T[] {
    return [
        ...new Set([
            ...a.filter((x) => !b.includes(x)),
            ...b.filter((x) => !a.includes(x)),
        ]),
    ];
}

/**
 * Calculates array difference.
 * Iterares over `a` checking if `b` contains `x`.
 */
export function asymmetricArrayDiff<T>(a: T[], b: T[]): T[] {
    return [...new Set(a.filter((x) => !b.includes(x)))];
}
