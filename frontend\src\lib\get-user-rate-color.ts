export function getUserRateColor(rate: number) {
    let red,
        green,
        blue = 0;

    if (rate <= 5) {
        // From red to orange (0 to 5)
        // Red stays high, green increases
        red = 255;
        green = Math.round((rate / 5) * 165); // 0 to 165 (orange)
    } else {
        // From orange to green (5 to 10)
        // Red decreases, green increases to full
        const progress = (rate - 5) / 5; // 0 to 1
        red = Math.round(255 * (1 - progress)); // 255 to 0
        green = Math.round(165 + 90 * progress); // 165 to 255
    }

    return `rgb(${red}, ${green}, ${blue})`;
}