import { Rating } from "@commune/api";
import { <PERSON><PERSON>, CronExpression } from "@nestjs/schedule";
import { ForbiddenException, Injectable, Logger } from "@nestjs/common";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { PrismaService } from "src/prisma/prisma.service";
import { toPrismaLocalizations, toPrismaPagination } from "src/utils";

const WEEK_IN_MS = 7 * 24 * 60 * 60 * 1000;

@Injectable()
export class RatingKarmaService {
    private readonly logger = new Logger(RatingKarmaService.name);

    constructor(private readonly prisma: PrismaService) {}

    async getKarmaPoints(input: Rating.GetKarmaPointsInput) {
        return await this.prisma.userKarmaGivenPoint.findMany({
            ...toPrismaPagination(input.pagination),
            where: {
                targetUserId: input.userId,
            },
            include: {
                comment: true,
                sourceUser: {
                    include: {
                        name: true,
                        image: true,
                    },
                },
            },
        });
    }

    async spendKarmaPoint(
        data: Rating.SpendKarmaPointInput,
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            if (user.id !== data.sourceUserId) {
                throw new ForbiddenException(
                    getError("must_be_admin_or_source_user"),
                );
            }

            if (data.sourceUserId === data.targetUserId) {
                throw new ForbiddenException(
                    getError("source_and_target_users_must_differ"),
                );
            }

            if (data.quantity !== 1 && data.quantity !== -1) {
                throw new ForbiddenException(
                    getError("must_be_admin_or_use_only_one_point"),
                );
            }
        }

        const id = await this.prisma.$transaction(async (trx) => {
            let userKarmaSpendablePointId: string | null = null;
            let availablePoints = Number.MAX_SAFE_INTEGER;

            if (!user.isAdmin) {
                const userKarmaSpendablePoint =
                    await trx.userKarmaSpendablePoint.findUnique({
                        where: {
                            userId: data.sourceUserId,
                        },
                    });

                if (!userKarmaSpendablePoint) {
                    throw new ForbiddenException(
                        ...getError("must_have_at_least_one_spendable_point"),
                    );
                }

                userKarmaSpendablePointId = userKarmaSpendablePoint.id;
                availablePoints = userKarmaSpendablePoint.points;
            }

            if (availablePoints < data.quantity) {
                throw new ForbiddenException(
                    ...getError("must_have_at_least_one_spendable_point"),
                );
            }

            const { id } = await trx.userKarmaGivenPoint.create({
                data: {
                    sourceUserId: data.sourceUserId,
                    targetUserId: data.targetUserId,
                    quantity: data.quantity,
                    comment: {
                        create: toPrismaLocalizations(data.comment, "comment"),
                    },
                },
            });

            if (userKarmaSpendablePointId) {
                await trx.userKarmaSpendablePoint.update({
                    where: {
                        id: userKarmaSpendablePointId,
                    },
                    data: {
                        points: {
                            decrement: data.quantity,
                        },
                    },
                });
            }

            return id;
        });

        return { id };
    }

    @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
    async addMissingKarmaPoints() {
        const weekAgo = new Date(Date.now() - WEEK_IN_MS);

        const staleSpendablePoints =
            await this.prisma.userKarmaSpendablePoint.findMany({
                where: {
                    lastAddedAt: {
                        lte: weekAgo,
                    },
                },
            });

        const howMuchPointsToAdd: {
            userId: string;
            points: number;
            newLastAddedAt: Date;
        }[] = staleSpendablePoints.map(({ userId, lastAddedAt }) => {
            const timePassed = Date.now() - lastAddedAt.getTime();
            const pointsToAdd = Math.floor(timePassed / WEEK_IN_MS);
            const newLastAddedAt = new Date(
                lastAddedAt.getTime() + pointsToAdd * WEEK_IN_MS,
            );

            return { userId, points: pointsToAdd, newLastAddedAt };
        });

        await this.prisma.$transaction(async (trx) => {
            await Promise.all(
                howMuchPointsToAdd.map(({ userId, points, newLastAddedAt }) =>
                    trx.userKarmaSpendablePoint.update({
                        where: { userId },
                        data: {
                            points: { increment: points },
                            lastAddedAt: newLastAddedAt,
                        },
                    }),
                ),
            );
        });
    }
}
