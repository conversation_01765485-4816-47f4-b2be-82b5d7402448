"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthController = void 0;
const common_1 = require("@nestjs/common");
const acrpc_1 = require("../../acrpc");
const auth_service_1 = require("../auth.service");
let AuthController = class AuthController {
    constructor(authService) {
        this.authService = authService;
        const acrpcServer = (0, acrpc_1.getServer)();
        acrpcServer.register({
            auth: {
                otp: {
                    post: async (input, _, rest) => {
                        const isSent = await this.authService.otp({
                            ...input,
                            ipAddress: rest.req.ip ?? null,
                            userAgent: rest.req.headers["user-agent"] ?? null,
                        });
                        return {
                            isSent,
                        };
                    },
                },
                signUp: {
                    post: async (input, _, rest) => {
                        const { user } = await this.authService.register({
                            ...input,
                            ipAddress: rest.req.ip ?? null,
                            userAgent: rest.req.headers["user-agent"] ?? null,
                        });
                        rest.req.session.user = user;
                        return user;
                    },
                },
                signIn: {
                    post: async (input, _, rest) => {
                        const { user } = await this.authService.login({
                            ...input,
                            ipAddress: rest.req.ip ?? null,
                            userAgent: rest.req.headers["user-agent"] ?? null,
                        });
                        rest.req.session.user = user;
                        return user;
                    },
                },
                signOut: {
                    get: async (_, __, rest) => {
                        await new Promise((resolve) => {
                            rest.req.session.destroy((err) => {
                                if (err) {
                                    console.error("Error destroying session:", err);
                                }
                                rest.res.clearCookie("session");
                                rest.res.sendStatus(common_1.HttpStatus.OK);
                                resolve();
                            });
                        });
                    },
                },
            },
        });
    }
};
exports.AuthController = AuthController;
exports.AuthController = AuthController = __decorate([
    (0, common_1.Controller)("auth"),
    __metadata("design:paramtypes", [auth_service_1.AuthService])
], AuthController);
//# sourceMappingURL=auth.controller.js.map