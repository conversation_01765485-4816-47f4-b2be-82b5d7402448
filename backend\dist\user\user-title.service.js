"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserTitleService = void 0;
const common_1 = require("@nestjs/common");
const errors_1 = require("../common/errors");
const prisma_service_1 = require("../prisma/prisma.service");
const utils_1 = require("../utils");
let UserTitleService = class UserTitleService {
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getUserTitles(input, currentUser) {
        const { userId, ids, isActive } = input;
        return await this.prisma.userTitle.findMany({
            where: {
                userId,
                id: ids && { in: ids },
                isActive: isActive ?? undefined,
            },
            select: {
                id: true,
                userId: true,
                color: true,
                isActive: true,
                name: {
                    select: {
                        locale: true,
                        value: true,
                    },
                },
                createdAt: true,
                updatedAt: true,
                deletedAt: currentUser.isAdmin,
            },
        });
    }
    async createUserTitle(input, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        return await this.prisma.userTitle.create({
            data: {
                userId: input.userId,
                name: {
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                isActive: input.isActive,
                color: input.color,
            },
        });
    }
    async updateUserTitle(input, currentUser) {
        const userTitle = await this.prisma.userTitle.findUniqueOrThrow({
            where: {
                id: input.id,
                deletedAt: currentUser.isAdmin ? undefined : null,
            },
        });
        if (!currentUser.isAdmin) {
            if (userTitle.userId !== currentUser.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_owner"));
            }
        }
        return await this.prisma.userTitle.update({
            where: {
                id: input.id,
            },
            data: {
                name: input.name && {
                    deleteMany: {},
                    create: (0, utils_1.toPrismaLocalizations)(input.name, "name"),
                },
                isActive: input.isActive,
                color: input.color,
            },
        });
    }
    async deleteUserTitle(input, currentUser) {
        if (!currentUser.isAdmin) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin"));
        }
        return await this.prisma.userTitle.update({
            where: {
                id: input.id,
            },
            data: {
                deletedAt: new Date(),
            },
        });
    }
};
exports.UserTitleService = UserTitleService;
exports.UserTitleService = UserTitleService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], UserTitleService);
//# sourceMappingURL=user-title.service.js.map