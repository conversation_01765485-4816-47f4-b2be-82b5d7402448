import { Consts } from "@commune/api";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CommuneMember, CommuneMemberType, Prisma } from "@prisma/client";
import { getError } from "src/common/errors";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class CommuneCore {
    constructor(private readonly prisma: PrismaService) {}

    isHeadMember(commune: { members: CommuneMember[] }, userId: string) {
        return commune.members.some(
            (member) =>
                member.deletedAt === null &&
                member.isHead &&
                member.actorId === userId &&
                member.actorType === CommuneMemberType.user,
        );
    }

    isMember(commune: { members: CommuneMember[] }, userId: string) {
        return commune.members.some(
            (member) =>
                member.deletedAt === null &&
                member.actorId === userId &&
                member.actorType === CommuneMemberType.user,
        );
    }

    async getCountOfCommunesWhereUserIsHead(
        userId: string,
        trx: Prisma.TransactionClient = this.prisma,
    ) {
        return await trx.communeMember.count({
            where: {
                actorType: CommuneMemberType.user,
                actorId: userId,
                isHead: true,
                deletedAt: null,
            },
        });
    }

    async getCountOfCommunesWhereUserIsMember(
        userId: string,
        trx: Prisma.TransactionClient = this.prisma,
    ) {
        return await trx.communeMember.count({
            where: {
                actorType: CommuneMemberType.user,
                actorId: userId,
                deletedAt: null,
            },
        });
    }

    async checkIsUserReachedMaxCommuneHeadsLimit(
        userId: string,
        trx: Prisma.TransactionClient = this.prisma,
    ) {
        const countOfCommunesWhereUserIsHead =
            await this.getCountOfCommunesWhereUserIsHead(userId, trx);

        if (countOfCommunesWhereUserIsHead >= Consts.MAX_COMMUNES_TO_BE_HEAD) {
            throw new ForbiddenException(
                ...getError("user_reached_max_commune_heads_limit"),
            );
        }
    }

    async checkIsUserReachedMaxCommunesLimit(
        userId: string,
        trx: Prisma.TransactionClient = this.prisma,
    ) {
        const countOfCommunesWhereUserIsMember =
            await this.getCountOfCommunesWhereUserIsMember(userId, trx);

        if (
            countOfCommunesWhereUserIsMember >= Consts.MAX_COMMUNES_TO_BE_MEMBER
        ) {
            throw new ForbiddenException(
                ...getError("user_reached_max_communes_limit"),
            );
        }
    }
}
