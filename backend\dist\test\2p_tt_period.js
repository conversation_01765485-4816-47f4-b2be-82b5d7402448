"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Calendar = void 0;
const MS_PER_HOUR = 60 * 60 * 1000;
const MS_PER_DAY = 86400 * 1000;
class Calendar {
    constructor(intervals) {
        this.intervals = intervals;
    }
    getFirstInterval(date, index) {
        for (let i = index; i < this.intervals.length; i++) {
            const interval = this.intervals[i];
            if (date >= interval.from && date <= interval.to) {
                return [interval, i];
            }
        }
        return [null, -1];
    }
    addIntervals(intervals) {
        let lastIntervalIndex = 0;
        for (const newInterval of intervals) {
            const [startInterval, startIntervalIndex] = this.getFirstInterval(newInterval.from, lastIntervalIndex);
            if (!startInterval) {
                throw new Error(`No start interval found for ${newInterval.from.toISOString()}`);
            }
            const [endInterval, endIntervalIndex] = this.getFirstInterval(newInterval.to, startIntervalIndex);
            if (!endInterval) {
                throw new Error(`No end interval found for ${newInterval.to.toISOString()}`);
            }
            const oldIntervals = this.intervals.slice(startIntervalIndex, endIntervalIndex + 1);
            const newIntervals = [];
            for (const oldInterval of oldIntervals) {
                if (oldInterval.from < newInterval.from) {
                    newIntervals.push({
                        from: oldInterval.from,
                        to: newInterval.from,
                        isWorking: oldInterval.isWorking,
                    });
                }
                newIntervals.push({
                    from: new Date(Math.max(oldInterval.from.getTime(), newInterval.from.getTime())),
                    to: new Date(Math.min(oldInterval.to.getTime(), newInterval.to.getTime())),
                    isWorking: newInterval.isWorking,
                });
                if (oldInterval.to > newInterval.to) {
                    newIntervals.push({
                        from: newInterval.to,
                        to: oldInterval.to,
                        isWorking: oldInterval.isWorking,
                    });
                }
            }
            this.intervals.splice(startIntervalIndex, oldIntervals.length, ...newIntervals);
            lastIntervalIndex = startIntervalIndex;
        }
        this.mergeIntervals();
    }
    mergeIntervals() {
        const newIntervals = [];
        for (let i = 0; i < this.intervals.length; i++) {
            const startInterval = this.intervals[i];
            let endIntervalIndex = i;
            let endInterval = startInterval;
            for (let j = i + 1; j < this.intervals.length; j++) {
                const newEndInterval = this.intervals[j];
                if (newEndInterval.isWorking !== startInterval.isWorking) {
                    break;
                }
                endIntervalIndex = j;
                endInterval = newEndInterval;
            }
            newIntervals.push({
                from: startInterval.from,
                to: endInterval.to,
                isWorking: startInterval.isWorking,
            });
            i = endIntervalIndex;
        }
        this.intervals = newIntervals;
    }
    fillWithWorkingTime() {
        if (this.intervals.length !== 1) {
            throw new Error("Calendar must have only one interval when filling with working time");
        }
        const from = this.intervals[0].from;
        const to = this.intervals[0].to;
        console.log({
            from: from.toISOString(),
            to: to.toISOString(),
        });
        const intervals = [];
        for (let date = new Date(from); date < to; date.setDate(date.getDate() + 1)) {
            const dayOfWeek = date.getDay() || 7;
            const startOfDay = new Date(Math.floor((date.getTime() / MS_PER_DAY) * MS_PER_DAY));
            const startOfNextDay = new Date(Math.floor((date.getTime() / MS_PER_DAY) * MS_PER_DAY) +
                MS_PER_DAY);
            if (dayOfWeek < 6) {
                const startOfWork = new Date(Math.floor((date.getTime() / MS_PER_DAY) * MS_PER_DAY) +
                    9 * MS_PER_HOUR);
                const endOfWork = new Date(Math.floor((date.getTime() / MS_PER_DAY) * MS_PER_DAY) +
                    18 * MS_PER_HOUR);
                intervals.push({
                    from: startOfDay,
                    to: startOfWork,
                    isWorking: false,
                }, {
                    from: startOfWork,
                    to: endOfWork,
                    isWorking: true,
                }, {
                    from: endOfWork,
                    to: startOfNextDay,
                    isWorking: false,
                });
            }
            else {
                intervals.push({
                    from: startOfDay,
                    to: startOfNextDay,
                    isWorking: false,
                });
            }
        }
        this.addIntervals(intervals);
    }
}
exports.Calendar = Calendar;
const calendar = new Calendar([
    {
        from: new Date("2025-01-01T00:00:00.000Z"),
        to: new Date("2025-01-07T00:00:00.000Z"),
        isWorking: false,
    },
]);
calendar.fillWithWorkingTime();
console.dir({
    intervals0: calendar.intervals,
}, {
    depth: null,
});
calendar.addIntervals([
    {
        from: new Date("2025-01-01T12:00:00.000Z"),
        to: new Date("2025-01-01T14:00:00.000Z"),
        isWorking: false,
    },
    {
        from: new Date("2025-01-02T16:00:00.000Z"),
        to: new Date("2025-01-03T12:00:00.000Z"),
        isWorking: false,
    },
    {
        from: new Date("2025-01-03T17:00:00.000Z"),
        to: new Date("2025-01-05T14:00:00.000Z"),
        isWorking: false,
    },
]);
console.dir({
    intervals1: calendar.intervals,
}, {
    depth: null,
});
calendar.addIntervals([
    {
        from: new Date("2025-01-06T12:00:00.000Z"),
        to: new Date("2025-01-06T14:00:00.000Z"),
        isWorking: true,
    },
]);
console.dir({
    intervals2: calendar.intervals,
}, {
    depth: null,
});
//# sourceMappingURL=2p_tt_period.js.map