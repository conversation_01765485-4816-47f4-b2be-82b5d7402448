import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();

  const joinRequests = await api.commune.joinRequest.list.get({}, { fetch, ctx: { url } });

  const communes = joinRequests.length
    ? await api.commune.list.get(
      { ids: joinRequests.map(({ communeId }) => communeId) },
      { fetch, ctx: { url } },
    )
    : [];
  const communeMap = new Map(communes.map((commune) => [commune.id, commune]));

  const joinRequestsWithDetails = joinRequests.map((joinRequest) => ({
    ...joinRequest,
    commune: communeMap.get(joinRequest.communeId)!,
  }));

  return {
    joinRequests: joinRequestsWithDetails,
    isHasMoreJoinRequests: joinRequests.length === Consts.PAGE_SIZE,
  };
};
