<script lang="ts">
  import type { Common } from "@commune/api";

  import { getClient } from "$lib/acrpc";
  import { fetchWithAuth } from "$lib";
  import {
    Modal,
    LocalizedInput,
    LocalizedTextarea,
    EntityImageInput,
    OfficialMarkIcon,
  } from "$lib/components";

  const i18n = {
    en: {
      _page: { title: "— Reactor of Commune" },
      head: "Head",
      createdOn: "Created on",
      editCommunity: "Edit Community",
      uploadImage: "Upload Image",
      deleteImage: "Delete Image",
      hub: "Hub",
      noHub: "No hub",
      editCommunityTitle: "Edit Community",
      communityName: "Community Name",
      communityDescription: "Community Description",
      communityNamePlaceholder: "Enter community name",
      communityDescriptionPlaceholder: "Enter community description",
      isOfficial: "Official Community",
      isOfficialDescription: "Mark this community as official (admin only)",
      save: "Save",
      cancel: "Cancel",
      saving: "Saving...",
      communityUpdatedSuccess: "Community updated successfully!",
      errorUpdatingCommunity: "Failed to update community",
      required: "This field is required",
      uploadImageTitle: "Upload Community Image",
      upload: "Upload",
      uploading: "Uploading...",
      imageUploadedSuccess: "Image uploaded successfully!",
      errorUploadingImage: "Failed to upload image",
      pleaseSelectImage: "Please select an image to upload",
      invalidFileType: "Invalid file type. Please upload a JPG, PNG, or WebP image.",
      fileTooLarge: "File is too large. Maximum size is 5MB.",
      uploadImageMaxSize: "Upload an image (JPG, PNG, WebP), max 5MB.",
      editImage: "Edit Image",
      selectDifferentImage: "Select Different Image",
      confirmDeleteImage: "Are you sure you want to delete this image?",
      deleteImageTitle: "Delete Image",
      delete: "Delete",
      deleting: "Deleting...",
      imageDeletedSuccess: "Image deleted successfully!",
      errorDeletingImage: "Failed to delete image",
    },
    ru: {
      _page: { title: "— Реактор Коммуны" },
      head: "Глава",
      createdOn: "Создано",
      editCommunity: "Редактировать сообщество",
      uploadImage: "Загрузить изображение",
      deleteImage: "Удалить изображение",
      hub: "Хаб",
      noHub: "Нет хаба",
      editCommunityTitle: "Редактировать сообщество",
      communityName: "Название сообщества",
      communityDescription: "Описание сообщества",
      communityNamePlaceholder: "Введите название сообщества",
      communityDescriptionPlaceholder: "Введите описание сообщества",
      isOfficial: "Официальное сообщество",
      isOfficialDescription: "Отметить это сообщество как официальное (только для администраторов)",
      save: "Сохранить",
      cancel: "Отмена",
      saving: "Сохранение...",
      communityUpdatedSuccess: "Сообщество успешно обновлено!",
      errorUpdatingCommunity: "Не удалось обновить сообщество",
      required: "Это поле обязательно",
      uploadImageTitle: "Загрузить изображение сообщества",
      upload: "Загрузить",
      uploading: "Загрузка...",
      imageUploadedSuccess: "Изображение загружено успешно!",
      errorUploadingImage: "Не удалось загрузить изображение",
      pleaseSelectImage: "Пожалуйста, выберите изображение для загрузки",
      invalidFileType: "Неверный тип файла. Пожалуйста, загрузите JPG, PNG или WebP изображение.",
      fileTooLarge: "Файл слишком большой. Максимальный размер - 5MB.",
      uploadImageMaxSize: "Загрузите изображение (JPG, PNG, WebP), максимальный размер - 5MB.",
      editImage: "Редактировать изображение",
      selectDifferentImage: "Выбрать другое изображение",
      confirmDeleteImage: "Вы уверены, что хотите удалить это изображение?",
      deleteImageTitle: "Удалить изображение",
      delete: "Удалить",
      deleting: "Удаление...",
      imageDeletedSuccess: "Изображение удалено успешно!",
      errorDeletingImage: "Не удалось удалить изображение",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, toLocaleHref, getAppropriateLocalization } = $derived(data);
  const t = $derived(i18n[locale]);

  // State management
  let community = $state(data.community);
  let error = $state<string | null>(null);

  // Edit community modal state
  let showEditModal = $state(false);
  let isUpdating = $state(false);
  let updateError = $state<string | null>(null);
  let updateSuccess = $state<string | null>(null);
  let communityName = $state<Common.Localizations>([]);
  let communityDescription = $state<Common.Localizations>([]);
  let communityIsOfficial = $state(false);

  // Form change tracking for edit community modal
  let initialCommunityName = $state<string>("");
  let initialCommunityDescription = $state<string>("");
  let initialCommunityIsOfficial = $state(false);
  let isCommunityFormValuesChanged = $state(false);

  // Image upload modal state
  let showUploadModal = $state(false);
  let isUploading = $state(false);
  let uploadError = $state<string | null>(null);
  let uploadSuccess = $state<string | null>(null);
  let selectedFile = $state<File | null>(null);

  // Image delete modal state
  let showDeleteImageModal = $state(false);
  let isDeleting = $state(false);
  let deleteError = $state<string | null>(null);
  let deleteSuccess = $state<string | null>(null);

  // Edit community functions
  function openEditModal() {
    showEditModal = true;
    communityName = [...community.name];
    communityDescription = [...community.description];
    communityIsOfficial = community.isOfficial;

    // Set initial values for change tracking
    initialCommunityName = JSON.stringify(community.name);
    initialCommunityDescription = JSON.stringify(community.description);
    initialCommunityIsOfficial = community.isOfficial;
    isCommunityFormValuesChanged = false;

    resetEditForm();
  }

  function closeEditModal() {
    showEditModal = false;
  }

  // Track community form changes
  $effect(() => {
    if (showEditModal) {
      isCommunityFormValuesChanged =
        JSON.stringify(communityName) !== initialCommunityName ||
        JSON.stringify(communityDescription) !== initialCommunityDescription ||
        communityIsOfficial !== initialCommunityIsOfficial;
    }
  });

  function resetEditForm() {
    updateError = null;
    updateSuccess = null;
    isUpdating = false;
  }

  function validateEditForm(): boolean {
    if (!communityName.some((item) => item.value.trim().length > 0)) {
      updateError = t.required;
      return false;
    }

    if (!communityDescription.some((item) => item.value.trim().length > 0)) {
      updateError = t.required;
      return false;
    }

    return true;
  }

  async function handleUpdateCommunity() {
    if (!validateEditForm()) return;

    isUpdating = true;
    updateError = null;
    updateSuccess = null;

    try {
      await api.reactor.community.patch({
        id: community.id,
        name: communityName,
        description: communityDescription,
        ...(data.me?.role === "admin" && { isOfficial: communityIsOfficial }),
      });

      updateSuccess = t.communityUpdatedSuccess;

      setTimeout(() => {
        refresh();
      }, 1500);
    } catch (err) {
      updateError = err instanceof Error ? err.message : t.errorUpdatingCommunity;
      console.error(err);
    } finally {
      isUpdating = false;
    }
  }

  // Image upload functions
  function openUploadModal() {
    showUploadModal = true;
    resetUploadForm();
  }

  function closeUploadModal() {
    showUploadModal = false;
    selectedFile = null;
  }

  function resetUploadForm() {
    uploadError = null;
    uploadSuccess = null;
    isUploading = false;
    selectedFile = null;
  }

  async function handleUploadImage() {
    if (!selectedFile) {
      uploadError = t.pleaseSelectImage;
      return;
    }

    isUploading = true;
    uploadError = null;
    uploadSuccess = null;

    try {
      const formData = new FormData();
      formData.append("image", selectedFile);

      const response = await fetchWithAuth(`/api/reactor/community/${community.id}/image`, {
        method: "PUT",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`${t.errorUploadingImage}: ${response.statusText}`);
      }

      uploadSuccess = t.imageUploadedSuccess;

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      uploadError = err instanceof Error ? err.message : t.errorUploadingImage;
      console.error(err);
    } finally {
      isUploading = false;
    }
  }

  // Image delete functions
  function openDeleteImageModal() {
    showDeleteImageModal = true;
    resetDeleteForm();
  }

  function closeDeleteImageModal() {
    showDeleteImageModal = false;
  }

  function resetDeleteForm() {
    deleteError = null;
    deleteSuccess = null;
    isDeleting = false;
  }

  async function handleDeleteImage() {
    isDeleting = true;
    deleteError = null;
    deleteSuccess = null;

    try {
      const response = await fetchWithAuth(`/api/reactor/community/${community.id}/image`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error(`${t.errorDeletingImage}: ${response.statusText}`);
      }

      deleteSuccess = t.imageDeletedSuccess;

      setTimeout(() => {
        window.location.reload();
      }, 1500);
    } catch (err) {
      deleteError = err instanceof Error ? err.message : t.errorDeletingImage;
      console.error(err);
    } finally {
      isDeleting = false;
    }
  }

  function refresh() {
    window.location.reload();
  }
</script>

<svelte:head>
  <title>{getAppropriateLocalization(community.name) ?? "No name?"} {t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <!-- Community Header -->
  <div class="row mb-4">
    <!-- Community Image -->
    <div class="col-md-4 col-lg-3 mb-3">
      <div class="community-image-container">
        {#if community.image}
          <img
            src={`/images/${community.image}`}
            alt={getAppropriateLocalization(community.name) || "Community"}
            class="community-image"
          />
        {:else}
          <div class="community-image-placeholder">
            <i class="bi bi-people fs-1 text-muted"></i>
          </div>
        {/if}
      </div>

      <!-- Image Management Buttons -->
      {#if data.canEdit}
        <div class="mt-3 d-grid gap-2">
          <button class="btn btn-outline-primary btn-sm" onclick={openUploadModal}>
            <i class="bi bi-upload me-1"></i>
            {t.uploadImage}
          </button>
          {#if community.image}
            <button class="btn btn-outline-danger btn-sm" onclick={openDeleteImageModal}>
              <i class="bi bi-trash me-1"></i>
              {t.deleteImage}
            </button>
          {/if}
        </div>
      {/if}
    </div>

    <!-- Community Info -->
    <div class="col-md-8 col-lg-9">
      <div class="d-flex justify-content-between align-items-start mb-3">
        <h1 class="mb-0 d-flex align-items-center">
          {#if community.isOfficial}
            <span class="official-mark-icon">
              <OfficialMarkIcon />
            </span>
          {/if}
          {getAppropriateLocalization(community.name) || "No name?"}
        </h1>
        {#if data.canEdit}
          <button class="btn btn-primary" onclick={openEditModal}>
            <i class="bi bi-pencil me-1"></i>
            {t.editCommunity}
          </button>
        {/if}
      </div>

      <p class="text-muted mb-3 fs-5">
        {getAppropriateLocalization(community.description) || ""}
      </p>

      <!-- Community Metadata -->
      <!-- Hub Info (if exists) -->
      {#if community.hub}
        <div class="row g-3 mb-3">
          <div class="col-12">
            <div class="d-flex align-items-center">
              <div class="me-3">
                {#if community.hub.image}
                  <img
                    src={`/images/${community.hub.image}`}
                    alt={getAppropriateLocalization(community.hub.name)}
                    class="rounded"
                    style="width: 48px; height: 48px; object-fit: contain;"
                  />
                {:else}
                  <div
                    class="rounded bg-secondary d-flex align-items-center justify-content-center"
                    style="width: 48px; height: 48px;"
                  >
                    <i class="bi bi-collection text-white"></i>
                  </div>
                {/if}
              </div>
              <div>
                <!-- <div class="small text-muted">{t.hub}:</div> -->
                <a
                  href={toLocaleHref(`/reactor/hubs/${community.hub.id}`)}
                  class="fw-medium"
                  style="text-decoration: none;"
                >
                  {getAppropriateLocalization(community.hub.name)}
                </a>
              </div>
            </div>
          </div>
        </div>
      {/if}

      <!-- Head User -->
      <div class="row g-3">
        <div class="col-12">
          <div class="d-flex align-items-center">
            <div class="me-3">
              {#if community.headUser.image}
                <img
                  src={`/images/${community.headUser.image}`}
                  alt={getAppropriateLocalization(community.headUser.name)}
                  class="rounded"
                  style="width: 48px; height: 48px; object-fit: contain;"
                />
              {:else}
                <div
                  class="rounded-circle bg-secondary d-flex align-items-center justify-content-center"
                  style="width: 48px; height: 48px;"
                >
                  <i class="bi bi-person-fill text-white"></i>
                </div>
              {/if}
            </div>
            <div>
              <!-- <div class="small text-muted">{t.head}:</div> -->
              <a
                href={toLocaleHref(`/people/${community.headUser.id}`)}
                class="fw-medium"
                style="text-decoration: none;"
              >
                {getAppropriateLocalization(community.headUser.name)}
              </a>
            </div>
          </div>
        </div>

        <!-- Creation Date -->
        <!-- <div class="col-sm-6">
          <div class="small text-muted">{t.createdOn}:</div>
          <div class="fw-medium">{formatDate(community.createdAt, locale)}</div>
        </div> -->
      </div>
    </div>
  </div>

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>

<!-- Edit Community Modal -->
{#if data.canEdit}
  <Modal
    show={showEditModal}
    title={t.editCommunityTitle}
    onClose={closeEditModal}
    onSubmit={handleUpdateCommunity}
    submitText={isUpdating ? t.saving : t.save}
    cancelText={t.cancel}
    submitDisabled={isUpdating ||
      !communityName.some((item) => item.value.trim().length > 0) ||
      !communityDescription.some((item) => item.value.trim().length > 0) ||
      !isCommunityFormValuesChanged}
    isSubmitting={isUpdating}
  >
    {#if updateError}
      <div class="alert alert-danger mb-3">
        {updateError}
      </div>
    {/if}

    {#if updateSuccess}
      <div class="alert alert-success mb-3">
        {updateSuccess}
      </div>
    {/if}

    <form>
      <!-- Community Name Input -->
      <LocalizedInput
        {locale}
        id="community-name"
        label={t.communityName}
        placeholder={t.communityNamePlaceholder}
        required
        bind:value={communityName}
      />

      <!-- Community Description Textarea -->
      <LocalizedTextarea
        {locale}
        label={t.communityDescription}
        placeholder={t.communityDescriptionPlaceholder}
        rows={4}
        required
        bind:value={communityDescription}
      />

      <!-- Official Community Checkbox (Admin Only) -->
      {#if data.me?.role === "admin"}
        <div class="mb-3">
          <div class="form-check">
            <input
              class="form-check-input"
              type="checkbox"
              id="community-is-official"
              bind:checked={communityIsOfficial}
            />
            <label class="form-check-label" for="community-is-official">
              {t.isOfficial}
            </label>
          </div>
          <div class="form-text text-muted">
            {t.isOfficialDescription}
          </div>
        </div>
      {/if}
    </form>
  </Modal>
{/if}

<!-- Upload Image Modal -->
{#if data.canEdit}
  <Modal
    show={showUploadModal}
    title={t.uploadImageTitle}
    onClose={closeUploadModal}
    onSubmit={handleUploadImage}
    submitText={isUploading ? t.uploading : t.upload}
    cancelText={t.cancel}
    submitDisabled={!selectedFile || isUploading}
    isSubmitting={isUploading}
    size="lg"
  >
    {#if uploadSuccess}
      <div class="alert alert-success mb-3">
        {uploadSuccess}
      </div>
    {/if}

    {#if uploadError}
      <div class="alert alert-danger mb-3">
        {uploadError}
      </div>
    {/if}

    <EntityImageInput {locale} bind:file={selectedFile} disabled={isUploading} />
  </Modal>
{/if}

<!-- Delete Image Modal -->
{#if data.canEdit && community.image}
  <Modal
    show={showDeleteImageModal}
    title={t.deleteImageTitle}
    onClose={closeDeleteImageModal}
    onSubmit={handleDeleteImage}
    submitText={isDeleting ? t.deleting : t.delete}
    cancelText={t.cancel}
    submitDisabled={isDeleting}
    isSubmitting={isDeleting}
  >
    {#if deleteSuccess}
      <div class="alert alert-success mb-3">
        {deleteSuccess}
      </div>
    {/if}

    {#if deleteError}
      <div class="alert alert-danger mb-3">
        {deleteError}
      </div>
    {/if}

    <p>{t.confirmDeleteImage}</p>
  </Modal>
{/if}

<style>
  .community-image-container {
    width: 100%;
    height: 300px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    border: 1px solid #dee2e6;
  }

  .community-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .community-image-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
  }

  .official-mark-icon {
    display: inline-flex;
    align-items: center;
    width: 32px;
    height: 32px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  @media (max-width: 768px) {
    .community-image-container {
      height: 250px;
    }
  }

  @media (max-width: 576px) {
    .community-image-container {
      height: 200px;
    }
  }
</style>
