import { CommuneMember, Prisma } from "@prisma/client";
import { PrismaService } from "src/prisma/prisma.service";
export declare class CommuneCore {
    private readonly prisma;
    constructor(prisma: PrismaService);
    isHeadMember(commune: {
        members: CommuneMember[];
    }, userId: string): boolean;
    isMember(commune: {
        members: CommuneMember[];
    }, userId: string): boolean;
    getCountOfCommunesWhereUserIsHead(userId: string, trx?: Prisma.TransactionClient): Promise<number>;
    getCountOfCommunesWhereUserIsMember(userId: string, trx?: Prisma.TransactionClient): Promise<number>;
    checkIsUserReachedMaxCommuneHeadsLimit(userId: string, trx?: Prisma.TransactionClient): Promise<void>;
    checkIsUserReachedMaxCommunesLimit(userId: string, trx?: Prisma.TransactionClient): Promise<void>;
}
