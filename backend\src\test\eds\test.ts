import fs from "node:fs/promises";
import path from "node:path";
import {
    createPrivateKey,
    createSign,
    createVerify,
    generateKeyPairSync,
} from "node:crypto";

const root = path.join(process.cwd(), "src/test/eds");

const PRIVATE_KEY_PASSPHRASE = "secret";

function generatePair(privateKeyPassphrase: string) {
    const { publicKey, privateKey } = generateKeyPairSync("rsa", {
        modulusLength: 4096,
        publicKeyEncoding: {
            type: "spki",
            format: "pem",
        },
        privateKeyEncoding: {
            type: "pkcs8",
            format: "pem",
            cipher: "aes-256-cbc",
            passphrase: privateKeyPassphrase,
        },
    });

    return { publicKey, privateKey };
}

function sign(
    content: string,
    privateKey: string,
    privateKeyPassphrase: string,
) {
    const signer = createSign("rsa-sha256");

    signer.update(content);

    const privateKeyObject = createPrivateKey({
        key: privateKey,
        type: "pkcs8",
        format: "pem",
        passphrase: privateKeyPassphrase,
    });

    const signature = signer.sign(privateKeyObject);

    return { signature };
}

function verify(content: string, publicKey: string, signature: Buffer) {
    const verifier = createVerify("rsa-sha256");

    verifier.update(content);

    const isValid = verifier.verify(publicKey, signature);

    return { isValid };
}

(async () => {
    // const { publicKey, privateKey } = generatePair(PRIVATE_KEY_PASSPHRASE);
    const publicKey = await fs.readFile(
        path.join(root, "pair1-public.pem"),
        "utf-8",
    );
    const privateKey = await fs.readFile(
        path.join(root, "pair1-private.pem"),
        "utf-8",
    );

    // console.log(publicKey);
    // console.log(privateKey);

    // const content = "test1";
    const content = await fs.readFile(path.join(root, "test1.txt"), "utf-8");

    const { signature } = sign(content, privateKey, PRIVATE_KEY_PASSPHRASE);

    console.log(signature);
    console.log(signature.toString("base64"));

    const { isValid } = verify(content, publicKey, signature);

    console.log();
    console.log({ isValid, v: 2 });
})();
