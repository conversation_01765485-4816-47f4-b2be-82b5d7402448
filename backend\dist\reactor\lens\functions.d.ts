import type { tokenize as Tokenize } from "./tokenize.mjs" with { "resolution-mode": "import" };
import type { createAst as CreateAst } from "./create-ast.mjs" with { "resolution-mode": "import" };
import type { validate as Validate } from "./validate.mjs" with { "resolution-mode": "import" };
import type { generateSql as GenerateSql } from "./generate-sql.mjs" with { "resolution-mode": "import" };
declare let tokenize: typeof Tokenize;
declare let createAst: typeof CreateAst;
declare let validate: typeof Validate;
declare let generateSql: typeof GenerateSql;
export declare const onReady: Promise<void>;
export { tokenize, createAst, validate, generateSql };
