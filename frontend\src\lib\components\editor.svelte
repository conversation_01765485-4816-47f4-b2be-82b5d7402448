<script lang="ts">
  import type { Editor as TEditor, RawEditorOptions } from "tinymce";

  import TinyMceEditor from "@tinymce/tinymce-svelte";

  export type Props = {
    content: string;
    onEditorInit?: (editor: TEditor) => void;
  };

  let { content = $bindable(), onEditorInit }: Props = $props();

  const editorConfig: RawEditorOptions = {
    // plugins: ["lists", "link", "image", "code", "table"],
    // toolbar:
    //   "undo redo | formatselect | bold italic | alignleft aligncenter alignright | bullist numlist | link image | code",
    // menubar: false,
    // height: 300,

    toolbar: "undo redo | bold italic strikethrough underline | alignleft aligncenter alignright",
    menubar: false,
    height: 300,
    promotion: false,
    // branding: false,

    // Preserve absolute URLs and prevent TinyMCE from modifying them
    relative_urls: false,

    init_instance_callback: (editor: TEditor) => {
      onEditorInit?.(editor);
    },
  };
</script>

<TinyMceEditor
  licenseKey="gpl"
  scriptSrc="/tinymce/tinymce.min.js"
  conf={editorConfig}
  bind:value={content}
/>
