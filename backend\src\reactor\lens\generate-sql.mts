import type { Statement } from "./validate.mjs";

function array(value: string[]) {
    return `(${value.map((v) => `'${v}'`).join(", ")})`;
}

function arrayEq(operator: "=" | "!=") {
    if (operator === "=") {
        return "IN";
    }

    return "NOT IN";
}

function ilike(value: string) {
    return `ILIKE '%${value}%'`;
}

export function generateSql(statement: Statement): string {
    switch (statement.type) {
        case "comparison": {
            switch (statement.identifier) {
                case "hub": {
                    return `hub ->> 'id' ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }

                case "community": {
                    return `community_id ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }

                case "author": {
                    return `author_id ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }

                case "rating": {
                    return `total_rating ${statement.operator} ${statement.value}`;
                }

                case "usefulness": {
                    return `average_usefulness ${statement.operator} ${statement.value}`;
                }

                case "tag": {
                    switch (statement.operator) {
                        case "=": {
                            return `tag_ids @> ${array(statement.value)}`;
                        }

                        case "!=": {
                            return `NOT (tag_ids @> ${array(statement.value)})`;
                        }
                    }
                }

                // currently not used
                case "difficulty": {
                    return `difficulty ${arrayEq(statement.operator)} ${array(statement.value)}`;
                }

                // currently not used
                case "duration": {
                    return `duration ${statement.operator} ${statement.value}`;
                }

                // ms
                case "age": {
                    return `age ${statement.operator} ${statement.value}`;
                }

                case "title": {
                    switch (statement.operator) {
                        case "~": {
                            return `EXISTS (SELECT 1 FROM JSONB_ARRAY_ELEMENTS(title) AS t WHERE t ->> 'value' ${ilike(statement.value)})`;
                        }
                    }
                }

                case "content": {
                    switch (statement.operator) {
                        case "~": {
                            return `EXISTS (SELECT 1 FROM JSONB_ARRAY_ELEMENTS(body) AS b WHERE b ->> 'value' ${ilike(statement.value)})`;
                        }
                    }
                }
            }
        }

        case "and": {
            return `(${statement.statements.map(generateSql).join(" AND ")})`;
        }

        case "or": {
            return `(${statement.statements.map(generateSql).join(" OR ")})`;
        }
    }
}
