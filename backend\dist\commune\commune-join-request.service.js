"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneJoinRequestService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const errors_1 = require("../common/errors");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const commune_core_1 = require("./commune.core");
let CommuneJoinRequestService = class CommuneJoinRequestService {
    constructor(prisma, communeCore) {
        this.prisma = prisma;
        this.communeCore = communeCore;
    }
    async getJoinRequests(input, user) {
        const joinRequests = await this.prisma.communeJoinRequest.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: Object.assign({ deletedAt: null }, input.communeId
                ? { communeId: input.communeId }
                : { userId: user.id }),
            orderBy: {
                createdAt: "desc",
            },
        });
        return joinRequests;
    }
    async createJoinRequest(input, user) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: input.communeId },
            include: {
                members: true,
            },
        });
        const userId = input.userId ?? user.id;
        if (!user.isAdmin) {
            if (user.id !== userId) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_self"));
            }
        }
        if (this.communeCore.isMember(commune, userId)) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("user_already_in_commune"));
        }
        const existingJoinRequest = await this.prisma.communeJoinRequest.findFirst({
            where: {
                communeId: input.communeId,
                userId,
                status: client_1.CommuneJoinRequestStatus.pending,
                deletedAt: null,
            },
        });
        if (existingJoinRequest) {
            return existingJoinRequest;
        }
        if (!user.isAdmin) {
            await this.communeCore.checkIsUserReachedMaxCommunesLimit(userId);
        }
        const joinRequest = await this.prisma.communeJoinRequest.create({
            data: {
                communeId: input.communeId,
                userId,
            },
        });
        return joinRequest;
    }
    async deleteJoinRequest(input, user) {
        const joinRequest = await this.prisma.communeJoinRequest.findUniqueOrThrow({
            where: { id: input.id },
        });
        if (joinRequest.status !== client_1.CommuneJoinRequestStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("join_request_must_be_pending"));
        }
        if (!user.isAdmin) {
            if (joinRequest.userId !== user.id) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_join_request_target"));
            }
        }
        await this.prisma.communeJoinRequest.update({
            where: { id: input.id },
            data: {
                deletedAt: new Date(),
            },
        });
        return true;
    }
    async acceptJoinRequest(input, user) {
        const joinRequest = await this.prisma.communeJoinRequest.findUniqueOrThrow({
            where: { id: input.id },
        });
        if (joinRequest.status !== client_1.CommuneJoinRequestStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("join_request_must_be_pending"));
        }
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: joinRequest.communeId },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
            }
            await this.communeCore.checkIsUserReachedMaxCommunesLimit(joinRequest.userId);
        }
        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: joinRequest.communeId,
                    actorType: client_1.CommuneMemberType.user,
                    actorId: joinRequest.userId,
                    isHead: false,
                },
            });
            await this.prisma.communeJoinRequest.update({
                where: { id: input.id },
                data: {
                    status: client_1.CommuneJoinRequestStatus.accepted,
                },
            });
        });
        return true;
    }
    async rejectJoinRequest(input, user) {
        const joinRequest = await this.prisma.communeJoinRequest.findUniqueOrThrow({
            where: { id: input.id },
        });
        if (joinRequest.status !== client_1.CommuneJoinRequestStatus.pending) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("join_request_must_be_pending"));
        }
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: joinRequest.communeId },
            include: {
                members: true,
            },
        });
        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member"));
            }
        }
        await this.prisma.communeJoinRequest.update({
            where: { id: input.id },
            data: {
                status: client_1.CommuneJoinRequestStatus.rejected,
            },
        });
        return true;
    }
};
exports.CommuneJoinRequestService = CommuneJoinRequestService;
exports.CommuneJoinRequestService = CommuneJoinRequestService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        commune_core_1.CommuneCore])
], CommuneJoinRequestService);
//# sourceMappingURL=commune-join-request.service.js.map