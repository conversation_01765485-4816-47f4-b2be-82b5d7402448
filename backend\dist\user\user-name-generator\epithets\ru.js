"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WORDS = void 0;
exports.getRandomEpithet = getRandomEpithet;
exports.WORDS = [
    ["неизменный", "неизменная"],
    ["восхитительный", "восхитительная"],
    ["авантюрный", "авантюрная"],
    ["проворный", "проворная"],
    ["красивый", "красивая"],
    ["доброжелательный", "доброжелательная"],
    ["блаженный", "блаженная"],
    ["смелый", "смелая"],
    ["храбрый", "храбрый"],
    ["спокойный", "спокойная"],
    ["харизматичный", "харизматичная"],
    ["жизнерадостный", "жизнерадостная"],
    ["умный", "умная"],
    ["сочувствующий", "сочувствующая"],
    ["любознательный", "любознательная"],
    ["отважный", "отважная"],
    ["неустрашимый", "неустрашимая"],
    ["преданный", "преданная"],
    ["усердный", "усердная"],
    ["динамичный", "динамичная"],
    ["стремящийся", "стремящаяся"],
    ["искренний", "искренняя"],
    ["красноречивый", "красноречивая"],
    ["очаровательный", "очаровательная"],
    ["энергичный", "энергичная"],
    ["верный", "верная"],
    ["бесстрашный", "бесстрашная"],
    ["пылкий", "пылкая"],
    ["откровенный", "откровенная"],
    ["галантный", "галантная"],
    ["щедрый", "щедрая"],
    ["нежный", "нежная"],
    ["светящийся", "светящаяся"],
    ["грациозный", "грациозная"],
    ["счастливый", "счастливая"],
    ["полезный", "полезная"],
    ["героический", "героическая"],
    ["честный", "честная"],
    ["образный", "образная"],
    ["изобретательный", "изобретательная"],
    ["вдохновляющий", "вдохновляющая"],
    ["веселый", "веселая"],
    ["добродушный", "добродушная"],
    ["радостный", "радостная"],
    ["благоразумный", "благоразумная"],
    ["острый", "острая"],
    ["добрый", "добрая"],
    ["добросердечный", "добросердечная"],
    ["знающий", "знающая"],
    ["возвышенный", "возвышенная"],
    ["лояльный", "лояльная"],
    ["удачливый", "удачная"],
    ["сияющий", "сияющая"],
    ["великодушный", "великодушная"],
    ["могучий", "могучая"],
    ["внимательный", "внимательная"],
    ["ловкий", "ловкая"],
    ["благородный", "благородная"],
    ["заботливый", "заботливая"],
    ["наблюдательный", "наблюдательная"],
    ["открытый", "открытая"],
    ["оптимистичный", "оптимистичная"],
    ["страстный", "страстная"],
    ["терпеливый", "терпеливая"],
    ["мирный", "мирная"],
    ["игривый", "игривая"],
    ["быстрый", "быстрая"],
    ["необычный", "необычная"],
    ["лучистый", "лучистая"],
    ["задумчивый", "задумчивая"],
    ["крепкий", "крепкая"],
    ["безмятежный", "безмятежная"],
    ["непоколебимый", "непоколебимая"],
    ["упорный", "упорная"],
    ["вдумчивый", "вдумчивая"],
    ["надежный", "надежная"],
    ["неизвестный", "неизвестная"],
    ["бодрый", "бодрая"],
    ["воодушевляющий", "воодушевляющая"],
    ["доблестный", "доблестная"],
    ["яркий", "яркая"],
    ["бдительный", "бдительная"],
    ["мудрый", "мудрая"],
    ["остроумный", "остроумная"],
    ["гостеприимный", "гостеприимная"],
    ["дружелюбный", "дружелюбная"],
    ["молодой", "молодая"],
    ["ревностный", "ревностная"],
];
function getRandomEpithet(variant, form) {
    return exports.WORDS[variant][form];
}
//# sourceMappingURL=ru.js.map