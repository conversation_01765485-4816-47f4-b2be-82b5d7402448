"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateVoting = exports.Votings = exports.Voting = exports.votesRequired = void 0;
const zod_1 = require("zod");
const api_1 = require("@commune/api");
exports.votesRequired = zod_1.z.number().int().positive();
exports.Voting = zod_1.z.object({
    id: api_1.Common.id,
    votesRequired: exports.votesRequired,
    endsAt: api_1.Common.stringToDate,
    title: api_1.Common.LocalizationsSchema,
    description: api_1.Common.LocalizationsSchema,
    options: zod_1.z.array(zod_1.z.object({
        id: api_1.Common.id,
        title: api_1.Common.LocalizationsSchema,
    })),
    createdAt: api_1.Common.stringToDate,
    updatedAt: api_1.Common.stringToDate,
});
exports.Votings = zod_1.z.array(exports.Voting);
exports.CreateVoting = zod_1.z.object({
    votesRequired: exports.votesRequired,
    endsAt: api_1.Common.stringToDate,
    title: api_1.Common.LocalizationsSchema,
    description: api_1.Common.LocalizationsSchema,
    options: zod_1.z.array(zod_1.z.object({
        title: api_1.Common.LocalizationsSchema,
    })),
});
//# sourceMappingURL=dto.js.map