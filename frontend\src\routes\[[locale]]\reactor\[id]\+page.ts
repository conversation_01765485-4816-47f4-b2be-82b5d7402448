import type { PageLoad } from './$types';

import { error } from '@sveltejs/kit';
import { getClient } from '$lib/acrpc';

export const load: PageLoad = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();

  const [
    [post],
    comments,
  ] = await Promise.all([
    api.reactor.post.list.get({ id: params.id, lensId: null }, { fetch, ctx: { url } }),
    api.reactor.comment.list.get({ entityType: "post", entityId: params.id }, { fetch, ctx: { url } }),
  ]);

  if (!post) {
    throw error(404, "Post not found");
  }

  return {
    post,
    comments,
  };
};
