import path from "node:path";
import fs from "node:fs/promises";
import { createVerify } from "node:crypto";
const root = path.join(process.cwd(), "src/test/eds");

async function validate(
    document: <PERSON><PERSON><PERSON>,
    signature: <PERSON><PERSON><PERSON>,
    publicKey: <PERSON><PERSON><PERSON>,
) {
    const verifier = createVerify("rsa-sha256");

    verifier.update(document);

    const isValid = verifier.verify(publicKey, signature);

    console.log({ isValid });
}

(async () => {
    const documentName = process.argv[2];
    const signatureName = process.argv[3];
    const publicKeyName = process.argv[4];

    if (!documentName) {
        throw new Error("Document name is required.");
    }

    if (!signatureName) {
        throw new Error("Signature name is required.");
    }

    if (!publicKeyName) {
        throw new Error("Public key name is required.");
    }

    const document = await fs.readFile(path.join(root, documentName));
    const signature = await fs.readFile(path.join(root, signatureName));
    const publicKey = await fs.readFile(
        path.join(root, `${publicKeyName}-public.pem`),
    );

    await validate(document, signature, publicKey);
})();
