import { Common, Commune } from "@commune/api";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CommuneInvitationStatus, CommuneMemberType } from "@prisma/client";
import { getError } from "src/common/errors";
import { CurrentUser } from "src/auth/types";
import { toPrismaPagination } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";

const WEEK = 7 * 24 * 60 * 60 * 1000;
const INVITATION_EXPIRATION_TIME = WEEK;

@Injectable()
export class CommuneInvitationService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly communeCore: CommuneCore,
    ) {}

    async getInvitations(
        input: Commune.GetCommuneInvitationsInput,
        user: CurrentUser,
    ) {
        const invitations = await this.prisma.communeInvitation.findMany({
            ...toPrismaPagination(input.pagination),
            where: Object.assign(
                { deletedAt: null },
                input.communeId
                    ? { communeId: input.communeId }
                    : { userId: user.id },
            ),
            orderBy: {
                createdAt: "desc",
            },
        });

        return invitations;
    }

    async createInvitation(
        input: Commune.CreateCommuneInvitationInput,
        user: CurrentUser,
    ) {
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: input.communeId },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_member"),
                );
            }
        }

        if (this.communeCore.isMember(commune, input.userId)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        const existingInvitation =
            await this.prisma.communeInvitation.findFirst({
                where: {
                    communeId: input.communeId,
                    userId: input.userId,
                    status: CommuneInvitationStatus.pending,
                    expiresAt: {
                        gt: new Date(),
                    },
                    deletedAt: null,
                },
            });

        if (existingInvitation) {
            return existingInvitation;
        }

        await this.communeCore.checkIsUserReachedMaxCommunesLimit(input.userId);

        return await this.prisma.communeInvitation.create({
            data: {
                communeId: input.communeId,
                userId: input.userId,
                expiresAt: new Date(Date.now() + INVITATION_EXPIRATION_TIME),
            },
        });
    }

    async deleteInvitation(input: Common.ObjectWithId, user: CurrentUser) {
        const invitation =
            await this.prisma.communeInvitation.findUniqueOrThrow({
                where: { id: input.id },
            });

        if (invitation.status !== CommuneInvitationStatus.pending) {
            throw new ForbiddenException(
                ...getError("invitation_must_be_pending"),
            );
        }

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: invitation.communeId },
            include: {
                members: true,
            },
        });

        if (!user.isAdmin) {
            if (!this.communeCore.isHeadMember(commune, user.id)) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_head_member"),
                );
            }
        }

        await this.prisma.communeInvitation.update({
            where: { id: input.id },
            data: {
                deletedAt: new Date(),
            },
        });

        return true;
    }

    async acceptInvitation(
        input: Common.ObjectWithId,
        currentUser: CurrentUser,
    ) {
        const invitation =
            await this.prisma.communeInvitation.findUniqueOrThrow({
                where: { id: input.id },
            });

        if (invitation.status !== CommuneInvitationStatus.pending) {
            throw new ForbiddenException(
                ...getError("invitation_must_be_pending"),
            );
        }

        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_invitation_target"),
            );
        }

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: invitation.communeId },
            include: {
                members: true,
            },
        });

        if (this.communeCore.isMember(commune, currentUser.id)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        if (!currentUser.isAdmin) {
            await this.communeCore.checkIsUserReachedMaxCommunesLimit(
                invitation.userId,
            );
        }

        await this.prisma.$transaction(async (trx) => {
            await trx.communeMember.create({
                data: {
                    communeId: invitation.communeId,
                    actorType: CommuneMemberType.user,
                    actorId: currentUser.id,
                    isHead: false,
                },
            });

            await trx.communeInvitation.update({
                where: { id: input.id },
                data: {
                    status: CommuneInvitationStatus.accepted,
                },
            });
        });

        return true;
    }

    async rejectInvitation(
        input: Common.ObjectWithId,
        currentUser: CurrentUser,
    ) {
        const invitation =
            await this.prisma.communeInvitation.findUniqueOrThrow({
                where: { id: input.id },
            });

        if (invitation.status !== CommuneInvitationStatus.pending) {
            throw new ForbiddenException(
                ...getError("invitation_must_be_pending"),
            );
        }

        if (!currentUser.isAdmin && invitation.userId !== currentUser.id) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_invitation_target"),
            );
        }

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: invitation.communeId },
            include: {
                members: true,
            },
        });

        if (this.communeCore.isMember(commune, currentUser.id)) {
            throw new ForbiddenException(
                ...getError("user_already_in_commune"),
            );
        }

        await this.prisma.communeInvitation.update({
            where: { id: input.id },
            data: {
                status: CommuneInvitationStatus.rejected,
            },
        });

        return true;
    }
}
