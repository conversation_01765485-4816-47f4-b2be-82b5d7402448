{"version": 3, "file": "validate.mjs", "sourceRoot": "", "sources": ["../../../src/test/lensing/validate.mts"], "names": [], "mappings": "AAAA,OAAO,EAAE,CAAC,EAAE,MAAM,KAAK,CAAC;AACxB,OAAO,EAAE,GAAG,EAA6B,MAAM,WAAW,CAAC;AAE3D,MAAM,SAAS,GAAG;IACd,MAAM,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;IACtB,SAAS,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAC1B,WAAW,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;IAC3B,kBAAkB,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IACnC,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;IACxB,eAAe,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAChC,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC;CACvB,CAAC;AAGF,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC;IACjB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;CACpC,CAAC,CAAC;AAGH,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;IACnB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;CACpC,CAAC,CAAC;AAGH,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;IACpB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC/B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;CACpC,CAAC,CAAC;AAGH,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;IACpB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IAC/B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;QACd,SAAS,CAAC,MAAM;QAChB,SAAS,CAAC,SAAS;QACnB,SAAS,CAAC,WAAW;QACrB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,QAAQ;QAClB,SAAS,CAAC,eAAe;KAC5B,CAAC;IACF,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE;CACjC,CAAC,CAAC;AAGH,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC;IACxB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IACnC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;QACd,SAAS,CAAC,MAAM;QAChB,SAAS,CAAC,SAAS;QACnB,SAAS,CAAC,WAAW;QACrB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,QAAQ;QAClB,SAAS,CAAC,eAAe;KAC5B,CAAC;IACF,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC;CAChD,CAAC,CAAC;AAGH,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC;IACjB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC;CACpC,CAAC,CAAC;AAGH,MAAM,UAAU,GAAG,CAAC,CAAC,MAAM,CAAC;IACxB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;IACnC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,SAAS,CAAC,MAAM,EAAE,SAAS,CAAC,SAAS,CAAC,CAAC;IAC1D,KAAK,EAAE,CAAC,CAAC,KAAK,CACV,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CACvE;CACJ,CAAC,CAAC;AAGH,MAAM,KAAK,GAAG,CAAC,CAAC,MAAM,CAAC;IACnB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC;IAC9B,QAAQ,EAAE,SAAS,CAAC,IAAI;IACxB,KAAK,EAAE,CAAC;SACH,MAAM,EAAE;SACR,QAAQ,EAAE;SACV,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;CACpB,CAAC,CAAC;AAGH,MAAM,IAAI,GAAG,CAAC,CAAC,MAAM,CAAC;IAClB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC;IAC7B,QAAQ,EAAE,SAAS,CAAC,IAAI;IACxB,KAAK,EAAE,CAAC;SACH,MAAM,EAAE;SACR,QAAQ,EAAE;SACV,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;CACpB,CAAC,CAAC;AAGH,MAAM,QAAQ,GAAG,CAAC,CAAC,MAAM,CAAC;IACtB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC;IACjC,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;QACd,SAAS,CAAC,WAAW;QACrB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,QAAQ;QAClB,SAAS,CAAC,eAAe;KAC5B,CAAC;IACF,KAAK,EAAE,CAAC,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;CACxC,CAAC,CAAC;AAEH,MAAM,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC;AACzB,MAAM,IAAI,GAAG,EAAE,GAAG,MAAM,CAAC;AACzB,MAAM,GAAG,GAAG,EAAE,GAAG,IAAI,CAAC;AACtB,MAAM,KAAK,GAAG,EAAE,GAAG,GAAG,CAAC;AACvB,MAAM,IAAI,GAAG,GAAG,GAAG,GAAG,CAAC;AAGvB,MAAM,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC;IACjB,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;IAC5B,QAAQ,EAAE,CAAC,CAAC,KAAK,CAAC;QACd,SAAS,CAAC,WAAW;QACrB,SAAS,CAAC,kBAAkB;QAC5B,SAAS,CAAC,QAAQ;QAClB,SAAS,CAAC,eAAe;KAC5B,CAAC;IACF,KAAK,EAAE,CAAC;SACH,MAAM,EAAE;SACR,KAAK,CAAC,mBAAmB,CAAC;SAC1B,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE;QACb,MAAM,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;QAE7D,IAAI,CAAC,KAAK,EAAE,CAAC;YACT,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;QAC/C,CAAC;QAED,MAAM,MAAM,GAAG,KAAK,CAAC,MAGpB,CAAC;QAEF,MAAM,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAEvC,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,GAAG;gBACJ,OAAO,MAAM,GAAG,IAAI,CAAC;YACzB,KAAK,IAAI;gBACL,OAAO,MAAM,GAAG,KAAK,CAAC;YAC1B,KAAK,GAAG;gBACJ,OAAO,MAAM,GAAG,CAAC,GAAG,GAAG,CAAC;YAC5B,KAAK,GAAG;gBACJ,OAAO,MAAM,GAAG,GAAG,CAAC;YACxB,KAAK,GAAG;gBACJ,OAAO,MAAM,GAAG,IAAI,CAAC;YACzB,KAAK,GAAG;gBACJ,OAAO,MAAM,GAAG,MAAM,CAAC;QAC/B,CAAC;IACL,CAAC,CAAC;CACT,CAAC,CAAC;AAGH,MAAM,gBAAgB,GAAG,CAAC,CAAC,YAAY,CACnC,CAAC,CAAC,MAAM,CAAC;IACL,IAAI,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;CAChC,CAAC,EAEF,CAAC,CAAC,KAAK,CAAC;IACJ,GAAG;IACH,KAAK;IACL,MAAM;IACN,MAAM;IACN,UAAU;IACV,GAAG;IACH,UAAU;IACV,KAAK;IACL,IAAI;IACJ,QAAQ;IACR,GAAG;CACN,CAAC,CACL,CAAC;AAcF,MAAM,UAAU,QAAQ,CAAC,SAAuB;IAC5C,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;QACrB,KAAK,KAAK,CAAC;QACX,KAAK,IAAI;YACL,OAAO;gBACH,GAAG,SAAS;gBACZ,UAAU,EAAE,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC;aACjD,CAAC;QAEN,KAAK,YAAY,CAAC,CAAC,CAAC;YAChB,MAAM,MAAM,GAAG,gBAAgB,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAErD,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAC;gBAClB,MAAM,MAAM,CAAC,KAAK,CAAC;YACvB,CAAC;YAED,OAAO,MAAM,CAAC,IAAI,CAAC;QACvB,CAAC;IACL,CAAC;AACL,CAAC;AAED,MAAM,CAAC,MAAM,SAAS,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC;AAEvC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC"}