import { User } from "@commune/api";
import { Injectable, NotFoundException } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
import { toPrismaPagination } from "src/utils";
import { EmailService } from "src/email/email.service";
import { PrismaService } from "src/prisma/prisma.service";

@Injectable()
export class AdminService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly emailService: EmailService,
    ) {}

    async isInviteExists(email: string) {
        const invite = await this.prisma.userInvite.findUnique({
            where: {
                email,
            },
        });

        return !!invite;
    }

    async useInvite(email: string) {
        return await this.prisma.userInvite.update({
            where: {
                email,
            },
            data: {
                isUsed: true,
            },
        });
    }

    async getUserInvites(input: User.GetUserInvitesInput, user: CurrentUser) {
        if (!user.isAdmin) {
            throw new NotFoundException();
        }

        return await this.prisma.userInvite.findMany({
            ...toPrismaPagination(input.pagination),
            orderBy: {
                isUsed: "asc",
            },
        });
    }

    async upsertUserInvite(
        input: User.UpsertUserInviteInput,
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            throw new NotFoundException();
        }

        return await this.prisma.$transaction(async (trx) => {
            const invite = await trx.userInvite.upsert({
                where: {
                    email: input.email,
                },
                update: {
                    name: input.name,
                    locale: input.locale,
                    isUsed: false,
                },
                create: {
                    email: input.email,
                    name: input.name,
                    locale: input.locale,
                },
            });

            await this.emailService.sendInvite({
                to: input.email,
                name: input.name,
                locale: input.locale,
            });

            return invite;
        });
    }

    async deleteUserInvite(
        input: User.DeleteUserInviteInput,
        user: CurrentUser,
    ) {
        if (!user.isAdmin) {
            throw new NotFoundException();
        }

        return await this.prisma.userInvite.delete({
            where: {
                id: input.id,
            },
        });
    }
}
