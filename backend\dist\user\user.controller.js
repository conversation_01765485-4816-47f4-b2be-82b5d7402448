"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserController = void 0;
const api_1 = require("@commune/api");
const common_1 = require("@nestjs/common");
const platform_express_1 = require("@nestjs/platform-express");
const zod_1 = require("../zod");
const errors_1 = require("../common/errors");
const current_user_decorator_1 = require("../auth/http/current-user.decorator");
const session_auth_guard_1 = require("../auth/http/session-auth.guard");
const user_service_1 = require("./user.service");
const user_note_service_1 = require("./user-note.service");
const user_title_service_1 = require("./user-title.service");
const acrpc_1 = require("../acrpc");
const MAX_FILE_SIZE = 5 * 1024 * 1024;
const ALLOWED_FILE_TYPES = ["image/jpeg", "image/png", "image/webp"];
let UserController = class UserController {
    constructor(userService, userNoteService, userTitleService) {
        this.userService = userService;
        this.userNoteService = userNoteService;
        this.userTitleService = userTitleService;
        const acrpcServer = (0, acrpc_1.getServer)();
        acrpcServer.register({
            user: {
                list: {
                    get: async (input, metadata) => {
                        const users = await this.userService.getUsers(input, metadata.user);
                        return users.map((user) => ({
                            ...user,
                            image: user.image?.url ?? null,
                        }));
                    },
                },
                me: {
                    get: async (_, metadata) => {
                        const user = await this.userService.getUser(metadata.user.id, metadata.user);
                        if (!user) {
                            throw new common_1.NotFoundException(...(0, errors_1.getError)("user_not_found"));
                        }
                        return {
                            ...user,
                            image: user.image?.url ?? null,
                        };
                    },
                },
                patch: (input, metadata) => this.userService.updateUser(input, metadata.user),
                title: {
                    list: {
                        get: (input, metadata) => this.userTitleService.getUserTitles(input, metadata.user),
                    },
                    post: (input, metadata) => this.userTitleService.createUserTitle(input, metadata.user),
                    patch: (input, metadata) => this.userTitleService.updateUserTitle(input, metadata.user),
                    delete: (input, metadata) => this.userTitleService.deleteUserTitle(input, metadata.user),
                },
                note: {
                    get: async (input, metadata) => {
                        const note = await this.userNoteService.getUserNote(input, metadata.user);
                        return {
                            text: note?.text ?? null,
                        };
                    },
                    put: (input, metadata) => this.userNoteService.updateUserNote(input, metadata.user),
                },
            },
        });
    }
    async uploadUserImage(id, currentUser, file) {
        if (!file) {
            throw new common_1.BadRequestException("No file uploaded");
        }
        await this.userService.updateUserImage(id, file, currentUser);
    }
};
exports.UserController = UserController;
__decorate([
    (0, common_1.Put)(":id/image"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    (0, common_1.UseInterceptors)((0, platform_express_1.FileInterceptor)("image")),
    __param(0, (0, common_1.Param)("id", new zod_1.ZodPipe(api_1.Common.id))),
    __param(1, (0, current_user_decorator_1.HttpCurrentUser)()),
    __param(2, (0, common_1.UploadedFile)(new common_1.ParseFilePipe({
        validators: [
            new common_1.MaxFileSizeValidator({ maxSize: MAX_FILE_SIZE }),
            new common_1.FileTypeValidator({
                fileType: ALLOWED_FILE_TYPES.join("|"),
            }),
        ],
    }))),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, Object]),
    __metadata("design:returntype", Promise)
], UserController.prototype, "uploadUserImage", null);
exports.UserController = UserController = __decorate([
    (0, common_1.Controller)("user"),
    (0, common_1.UseGuards)(session_auth_guard_1.HttpSessionAuthGuard),
    __metadata("design:paramtypes", [user_service_1.UserService,
        user_note_service_1.UserNoteService,
        user_title_service_1.UserTitleService])
], UserController);
//# sourceMappingURL=user.controller.js.map