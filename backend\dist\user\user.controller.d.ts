import { CurrentUser } from "src/auth/types";
import { UserService } from "./user.service";
import { UserNoteService } from "./user-note.service";
import { UserTitleService } from "./user-title.service";
export declare class UserController {
    private readonly userService;
    private readonly userNoteService;
    private readonly userTitleService;
    constructor(userService: UserService, userNoteService: UserNoteService, userTitleService: UserTitleService);
    uploadUserImage(id: string, currentUser: CurrentUser, file?: Express.Multer.File): Promise<void>;
}
