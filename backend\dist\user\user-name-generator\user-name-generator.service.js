"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserNameGeneratorService = void 0;
const common_1 = require("@nestjs/common");
const epithets_1 = require("./epithets");
const animals_1 = require("./animals");
function capitalize(word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
}
let UserNameGeneratorService = class UserNameGeneratorService {
    generateUserName() {
        const epithetVariant = Math.floor(Math.random() * epithets_1.VARIANT_COUNT);
        const animalVariant = Math.floor(Math.random() * animals_1.VARIANT_COUNT);
        return [
            {
                locale: "en",
                value: this.generateEnName(epithetVariant, animalVariant),
            },
            {
                locale: "ru",
                value: this.generateRuName(epithetVariant, animalVariant),
            },
        ];
    }
    generateEnName(epithetVariant, animalVariant) {
        const epithet = (0, epithets_1.getRandomEnEpithet)(epithetVariant);
        const animal = (0, animals_1.getRandomEnAnimal)(animalVariant);
        return `${capitalize(epithet)} ${capitalize(animal)}`;
    }
    generateRuName(epithetVariant, animalVariant) {
        const [animal, form] = (0, animals_1.getRandomRuAnimal)(animalVariant);
        const epithet = (0, epithets_1.getRandomRuEpithet)(epithetVariant, form);
        return `${capitalize(epithet)} ${capitalize(animal)}`;
    }
};
exports.UserNameGeneratorService = UserNameGeneratorService;
exports.UserNameGeneratorService = UserNameGeneratorService = __decorate([
    (0, common_1.Injectable)()
], UserNameGeneratorService);
//# sourceMappingURL=user-name-generator.service.js.map