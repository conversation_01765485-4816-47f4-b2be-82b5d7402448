"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.HeapdumpService = void 0;
const heapdump_1 = __importDefault(require("heapdump"));
const schedule_1 = require("@nestjs/schedule");
const common_1 = require("@nestjs/common");
let HeapdumpService = class HeapdumpService {
    onModuleInit() {
        this.logData();
    }
    saveHeapdump() {
        console.log("Saving heapdump");
        heapdump_1.default.writeSnapshot(`./.heapdumps/${Date.now()}.heapsnapshot`, (err, filename) => {
            if (err) {
                console.error(err);
                return;
            }
            console.log(`Heapdump saved to ${filename}`);
        });
    }
    logData() {
        const memoryUsage = process.memoryUsage();
        const ratio = 1024 * 1024;
        console.log(new Date().toISOString(), {
            rss: memoryUsage.rss / ratio,
            heapTotal: memoryUsage.heapTotal / ratio,
            heapUsed: memoryUsage.heapUsed / ratio,
            external: memoryUsage.external / ratio,
            arrayBuffers: memoryUsage.arrayBuffers / ratio,
        });
    }
};
exports.HeapdumpService = HeapdumpService;
__decorate([
    (0, schedule_1.Cron)("*/10 * * * *"),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], HeapdumpService.prototype, "logData", null);
exports.HeapdumpService = HeapdumpService = __decorate([
    (0, common_1.Injectable)()
], HeapdumpService);
//# sourceMappingURL=heapdump.service.js.map