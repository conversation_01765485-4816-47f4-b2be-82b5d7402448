import type { Client } from "@commune/api/acrpc/client";

import { redirect } from "@sveltejs/kit";
import { env } from "$env/dynamic/public";
import { browser } from "$app/environment";
import { createClient } from "@commune/api/acrpc/client";
import { schema, transformer } from "@commune/api/acrpc/schema";
import { removeCurrentUser } from "./current-user";

let __client: Client<typeof schema, { url: URL }> | undefined = undefined;

export function getClient() {
    return __client ??= createClient(schema, {
        entrypointUrl: "/api",
        transformer,
        init: {
            credentials: "include",
        },
        interceptor: async ({ response, ctx }) => {
            if (response.status === 401) {
                removeCurrentUser();

                const redirectFrom = ctx
                    ? ctx.url.pathname + ctx.url.search
                    : browser
                        ? window.location.pathname + window.location.search
                        : "/";

                redirect(302, `/auth?redirectFrom=${encodeURIComponent(redirectFrom)}`)
            }
        }
    });
}
