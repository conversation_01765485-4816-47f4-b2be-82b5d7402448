import { Common, Commune } from "@commune/api";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CommuneMember, CommuneMemberType } from "@prisma/client";
import { CurrentUser } from "src/auth/types";
import { getError } from "src/common/errors";
import { toPrismaPagination } from "src/utils";
import { PrismaService } from "src/prisma/prisma.service";
import { CommuneCore } from "./commune.core";

@Injectable()
export class CommuneMemberService {
    constructor(
        private readonly prisma: PrismaService,
        private readonly communeCore: CommuneCore,
    ) {}

    async hydrateMembers(
        members: CommuneMember[],
    ): Promise<Commune.GetCommuneMembersOutput> {
        const userIds = members
            .filter((member) => member.actorType === CommuneMemberType.user)
            .map((member) => member.actorId);

        const users = await this.prisma.user.findMany({
            where: {
                id: { in: userIds },
            },
            include: {
                name: true,
                image: true,
            },
        });

        const userMap = new Map(users.map((user) => [user.id, user]));

        return members.map((member) => {
            switch (member.actorType) {
                case CommuneMemberType.user: {
                    const user = userMap.get(member.actorId)!;
                    return {
                        ...member,
                        name: user.name,
                        image: user.image?.url ?? null,
                    };
                }
            }
        });
    }

    async getCommuneMembers(input: Commune.GetCommuneMembersInput) {
        const members = await this.prisma.communeMember.findMany({
            ...toPrismaPagination(input.pagination),
            where: {
                communeId: input.communeId,

                deletedAt: null,
            },
            orderBy: [{ isHead: "desc" }, { createdAt: "asc" }],
        });

        return await this.hydrateMembers(members);
    }

    async deleteCommuneMember(input: Common.ObjectWithId, user: CurrentUser) {
        const member = await this.prisma.communeMember.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });

        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: member.communeId },
            include: {
                members: true,
            },
        });

        const isNotAdmin = !user.isAdmin;
        const isNotHeadMember = !this.communeCore.isHeadMember(
            commune,
            user.id,
        );
        const isNotSelf =
            member.actorType === CommuneMemberType.user &&
            member.actorId !== user.id;

        if (isNotAdmin && isNotHeadMember && isNotSelf) {
            throw new ForbiddenException(
                ...getError("must_be_admin_or_head_member_or_self"),
            );
        }

        if (member.isHead) {
            throw new ForbiddenException(
                ...getError("cannot_remove_head_member"),
            );
        }

        return await this.prisma.communeMember.update({
            where: { id: input.id },
            data: { deletedAt: new Date() },
        });
    }
}
