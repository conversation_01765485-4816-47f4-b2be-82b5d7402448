import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, params, url }) => {
  const { fetcher: api } = getClient();

  const [
    me,
    [community],
  ] = await Promise.all([
    api.user.me.get({ fetch, skipInterceptor: true })
      .catch(() => null),
    api.reactor.community.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
  ]);

  if (!community) {
    throw error(404, "Community not found");
  }

  // Check if user can edit this community (admin or head user)
  const canEdit = me && (me.role === "admin" || me.id === community.headUser.id);

  return {
    me,
    community,
    canEdit,
  };
};
