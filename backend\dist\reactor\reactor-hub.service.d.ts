import { Reactor } from "@commune/api";
import { CurrentUser } from "src/auth/types";
import { MinioService } from "src/minio/minio.service";
import { PrismaService } from "src/prisma/prisma.service";
export declare class ReactorHubService {
    private readonly prisma;
    private readonly minioService;
    private readonly logger;
    constructor(prisma: PrismaService, minioService: MinioService);
    getHubIds(): Promise<string[]>;
    getHubs(input: Reactor.GetHubsInput, user: CurrentUser | null): Promise<{
        description: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        image: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            url: string;
        } | null;
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        name: {
            value: string;
            locale: import("@prisma/client").$Enums.Locale;
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            key: string;
        }[];
        isOfficial: boolean;
        headUser: {
            image: {
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                url: string;
            } | null;
            name: {
                value: string;
                locale: import("@prisma/client").$Enums.Locale;
                id: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                key: string;
            }[];
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            email: string;
            referrerId: string | null;
            role: import("@prisma/client").$Enums.UserRole;
            imageId: string | null;
            alignmentSystemType: import("@prisma/client").$Enums.UserAlignmentSystemType | null;
        };
    }[]>;
    createHub(input: Reactor.CreateHubInput, user: CurrentUser): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        imageId: string | null;
        isOfficial: boolean;
        headUserId: string;
    }>;
    updateHub(input: Reactor.UpdateHubInput, user: CurrentUser): Promise<void>;
    updateHubImage(id: string, file: Express.Multer.File, user: CurrentUser): Promise<void>;
    deleteHubImage(id: string, user: CurrentUser): Promise<void>;
}
