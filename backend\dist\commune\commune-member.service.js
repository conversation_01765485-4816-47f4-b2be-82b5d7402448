"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CommuneMemberService = void 0;
const common_1 = require("@nestjs/common");
const client_1 = require("@prisma/client");
const errors_1 = require("../common/errors");
const utils_1 = require("../utils");
const prisma_service_1 = require("../prisma/prisma.service");
const commune_core_1 = require("./commune.core");
let CommuneMemberService = class CommuneMemberService {
    constructor(prisma, communeCore) {
        this.prisma = prisma;
        this.communeCore = communeCore;
    }
    async hydrateMembers(members) {
        const userIds = members
            .filter((member) => member.actorType === client_1.CommuneMemberType.user)
            .map((member) => member.actorId);
        const users = await this.prisma.user.findMany({
            where: {
                id: { in: userIds },
            },
            include: {
                name: true,
                image: true,
            },
        });
        const userMap = new Map(users.map((user) => [user.id, user]));
        return members.map((member) => {
            switch (member.actorType) {
                case client_1.CommuneMemberType.user: {
                    const user = userMap.get(member.actorId);
                    return {
                        ...member,
                        name: user.name,
                        image: user.image?.url ?? null,
                    };
                }
            }
        });
    }
    async getCommuneMembers(input) {
        const members = await this.prisma.communeMember.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            where: {
                communeId: input.communeId,
                deletedAt: null,
            },
            orderBy: [{ isHead: "desc" }, { createdAt: "asc" }],
        });
        return await this.hydrateMembers(members);
    }
    async deleteCommuneMember(input, user) {
        const member = await this.prisma.communeMember.findUniqueOrThrow({
            where: { id: input.id, deletedAt: null },
        });
        const commune = await this.prisma.commune.findUniqueOrThrow({
            where: { id: member.communeId },
            include: {
                members: true,
            },
        });
        const isNotAdmin = !user.isAdmin;
        const isNotHeadMember = !this.communeCore.isHeadMember(commune, user.id);
        const isNotSelf = member.actorType === client_1.CommuneMemberType.user &&
            member.actorId !== user.id;
        if (isNotAdmin && isNotHeadMember && isNotSelf) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("must_be_admin_or_head_member_or_self"));
        }
        if (member.isHead) {
            throw new common_1.ForbiddenException(...(0, errors_1.getError)("cannot_remove_head_member"));
        }
        return await this.prisma.communeMember.update({
            where: { id: input.id },
            data: { deletedAt: new Date() },
        });
    }
};
exports.CommuneMemberService = CommuneMemberService;
exports.CommuneMemberService = CommuneMemberService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        commune_core_1.CommuneCore])
], CommuneMemberService);
//# sourceMappingURL=commune-member.service.js.map