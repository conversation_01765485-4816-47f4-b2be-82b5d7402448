import { Controller } from "@nestjs/common";
import { getServer } from "src/acrpc";
import { AdminService } from "./admin.service";

@Controller("admin")
export class AdminController {
    constructor(private readonly adminService: AdminService) {
        const acrpcServer = getServer();

        acrpcServer.register({
            user: {
                invite: {
                    list: {
                        get: (input, metadata) =>
                            this.adminService.getUserInvites(
                                input,
                                metadata.user,
                            ),
                    },
                    put: (input, metadata) =>
                        this.adminService.upsertUserInvite(
                            input,
                            metadata.user,
                        ),
                    delete: (input, metadata) =>
                        this.adminService.deleteUserInvite(
                            input,
                            metadata.user,
                        ),
                },
            },
        });
    }
}
