"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateInviteText = generateInviteText;
function generateInviteTextRu(name) {
    return `
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Приглашение в Коммуну — сообщество нового типа</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .logo {
            width: 96px;
            height: 96px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .header h1 {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            position: relative;
            z-index: 2;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .message {
            color: #5a6c7d;
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.8;
        }
        
        .features {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #3498db;
        }
        
        .features h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .features h3::before {
            content: '✨';
            margin-right: 10px;
            font-size: 20px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            color: #5a6c7d;
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
            line-height: 1.7;
        }
        
        .feature-list li::before {
            content: '🔹';
            position: absolute;
            left: 0;
            top: 0;
        }
        
        .feature-list li strong {
            color: #2c3e50;
            font-weight: 600;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-decoration: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
            transform: translateY(0);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .social-links {
            margin-top: 20px;
        }
        
        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            margin: 0 10px;
            border-radius: 50%;
            text-decoration: none;
            color: white;
            font-size: 18px;
            line-height: 40px;
            transition: transform 0.3s ease;
        }
        
        .social-links a:hover {
            transform: scale(1.1);
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img class="logo" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAMUExURf///wAAAAADAAAAAL8seJ0AAAAEdFJOU////wBAKqn0AAAACXBIWXMAAA6/AAAOvwE4BVMkAAAAGHRFWHRTb2Z0d2FyZQBQYWludC5ORVQgNS4xLjgbaeqoAAAAtmVYSWZJSSoACAAAAAUAGgEFAAEAAABKAAAAGwEFAAEAAABSAAAAKAEDAAEAAAACAAAAMQECABAAAABaAAAAaYcEAAEAAABqAAAAAAAAAI12AQDoAwAAjXYBAOgDAABQYWludC5ORVQgNS4xLjgAAwAAkAcABAAAADAyMzABoAMAAQAAAAEAAAAFoAQAAQAAAJQAAAAAAAAAAgABAAIABAAAAFI5OAACAAcABAAAADAxMDAAAAAAy1oKaOdC6EEAAAPHSURBVGhDvZfRjt04DEO7nf//57WlI1m2aScB2h7gJjJNkXPRh878+nnFLwFXD7ywkSfBcuHJQtAFjCeu90Q8gVtzuWX9DWwojnesvoWtndMNex9gcUXr7HyE5Rml4lf812AUsD8hRNyVCO352SCaSKjsGt5BCbWxzj4WCBlsCsZCSbLxWrDn8Q6wVSzJo/Q4Q1CwnDFNlCg9LhAF8xGLE9sepQkLb4cwZzphcHLdozTp8AGIM+qBa6Ps+6gpBhuBwM69oG0xHCnWAomNOlbY+gCLQGYt4CZg7TWsBYSWAi4S9l7DWkLsuUA1cHO7GkRuvDfYDFAT5AC1EMG8kjD7HpiywaUTir0NknkluO0F3AgwGLkakOzPxEwz3GjwFLhoeLQ/E1wD9CPYBugNj57zNz/yBYwJckcVLH7EK1gB0YgCjoDRSKk/+ORQrgdIIAuKH+GpQGzAQwFnouIjCsQKWAFzgLWa+2zC+DAEttDhHPyxgq4YHIOpAEuAuukTWM7LrYBp9SA2ECRYGgiA+FjA/ICwmtQpBVyB0k4or2uXb4DWQZFg6aA4aCvcdlA6KBIsHZQOigJHsSAcwdZAuMVvJk4PLGaOGjzi5zqBrYHwteBegcVAelXAyUGTYHGUVuDaQLpmDzDLhAEXDloH5Qi2DoqDNkB30Bw0CRYHzUEboDtoDpoEi4PmoA3QHbQOyhFsHRQHrcCFgfSY7mCWCSvqGk2CxVHaCp7JhCTBYiB9LUA4gq2B8KmA0wOLmaMGj/i5TmBrINwKcHRQOigSLAZSA2GFWwet006/f7u6gaWD4qB1Xv2nfywwZq/j2r/9teVkubZgaSAA4q1geDhLsJyXW8FocLCUH65JJsdQDomtNDgGf+y3a1vocA5UgTDbGJ/vf4Acv0LanwqwNxAC+ScUViOl/uCzF2A2kEAVYATEK1gB0aGgNmBLkC9gTJA7lv1Q8NiAbYDeyYLSgKvAxQFMBS4aHu3PASZ7ATcCDEauJh5tz/krxHtgygaXTij2NkjmteN7CeoAHRALHhwFooHNCje3q0EE894aWHwNawmxf62A1FKwNLD2ARaB0FowN+QWw5FiLRDZKOPUUJZ81BSDjUBgp87bd8jhSDp8AOKMc0HiURosM6Q5y0lRovS4QBasR0GJ0uMMScF23ilJNtbZxwo5ySbsFSXJxmsBIYNdkV8i3jV1TxdhqkD/UzslX8D+hBRvFWfYXTjJ32Fz5aR/rWBr53zzpYINxe3ubQVuzf32TQfGE0/3DYIkWC68sHTIm+Dqys/P/x9tIYfiZGHlAAAAAElFTkSuQmCC" alt="Лого" />
            <h1>Коммуна</h1>
            <p>Цифровое сообщество нового типа</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                ${name ? `Здравствуйте, ${name}!` : "Здравствуйте!"}
            </div>
            
            <div class="message">
                Я пишу, чтобы пригласить вас стать одним из первых участников проекта Цифровое Сообщество «Коммуна».
            </div>
            
            <div class="message">
                Мы создаём Коммуну как ответ на то, во что превратились современные платформы: шумные, обезличенные пространства, где главное — это борьба за внимание. Мы верим, что возможно иначе.
            </div>
            
            <div class="message">
                Коммуна — это наша попытка построить цифровую среду, основанную на доверии и осмысленном общении. Место, где можно быть собой среди «своих», где ваш голос слышен, а вклад действительно ценен. У нас нет рекламы и не будет, потому что цель — не извлечь выгоду из участников, а создать для них максимально комфортные и полезные условия.
            </div>
            
            <div class="features">
                <h3>Внутри вы найдёте:</h3>
                <ul class="feature-list">
                    <li>Систему небольших сообществ — <strong>коммун</strong>, где можно объединяться с близкими по духу людьми.</li>
                    <li><strong>Реактор</strong> — нашу общую площадку для глубоких дискуссий и обмена идеями, свободную от информационного мусора.</li>
                    <li>Свой язык, календарь и <strong>Право</strong> — систему правил, созданную с учётом опыта современного мира.</li>
                </ul>
            </div>
            
            <div class="message">
                Сейчас проект находится на самом раннем этапе. Мы не зовём вас просто «зарегистрироваться». Мы приглашаем вас к диалогу. Именно ваш взгляд, ваши идеи и критика сейчас могут определить, какой Коммуна станет в будущем. Вы получаете возможность не просто наблюдать, а влиять на создание сообщества, в котором вам самим захочется оставаться.
            </div>
            
            <div class="message">
                Наша цель — выйти за пределы онлайна и направить общие ресурсы на реальные проекты, которые улучшат жизнь участников. Но начинается всё здесь и сейчас, с формирования нашего ядра — людей, которые разделяют наши ценности.
            </div>
            
            <div class="message">
                Присоединяйтесь к нам. На этом этапе весь функционал площадки доступен всем участникам без ограничений.
            </div>
            
            <center>
                <a href="https://commune.my" class="cta-button" target="_blank">
                    Присоединиться
                </a>
            </center>
        </div>
        
        <div class="footer">
            <p>С уважением,<br><strong>Команда проекта «Коммуна»</strong></p>
            
            <div class="social-links">
                <a href="https://t.me/ds_commune_ru" title="Telegram" target="_blank">
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAYdEVYdFNvZnR3YXJlAFBhaW50Lk5FVCA1LjEuOBtp6qgAAAC2ZVhJZklJKgAIAAAABQAaAQUAAQAAAEoAAAAbAQUAAQAAAFIAAAAoAQMAAQAAAAIAAAAxAQIAEAAAAFoAAABphwQAAQAAAGoAAAAAAAAAYAAAAAEAAABgAAAAAQAAAFBhaW50Lk5FVCA1LjEuOAADAACQBwAEAAAAMDIzMAGgAwABAAAAAQAAAAWgBAABAAAAlAAAAAAAAAACAAEAAgAEAAAAUjk4AAIABwAEAAAAMDEwMAAAAACrgCETU544KAAAByhJREFUWEelWGtsVUUQnt1zHy0t1KgYRR5tQi2lqYWa8jTxkWBC+KHGokIwphZFf5AYf+lff/lXDK8QqKAo+CDGxMSICRJFShEQWijQImqaEHwQINTe1znrzO6Ze/ace257C1+y2dnZ2ZlvZ1/nXgF3gO5vB9XZ0Rrw0ItUoGsbSaXgkbvzsOmJubcdZ9IDV315Rl0RdZCIDFVIRgijI5nAbYJC8aGqHOxdOTmyFRsv3X9BZZ0UBkUiShaD2yQqgwdT3Cz8sLqpooEVGbUfGFHCzfutgJSDw2lZKWNSSq2rFNJzoLfzwQnjj2vQ+skl5SRzIDzKnDG1MxaVeZlt/XhIqByOScPRzlllB5TtmPf5ZVXlulqOBowjwW2q6cBQ7Wmd2Y+owcJyBEJC37Mzww59xCrn7xtSScvXeGTsmkAyn+ig317+eJLCkXDs6VKSJRtn3scXlSy4ern4NFLteZ6WCSyz3u4nuaCi/R7WtBplMohQrgeL9g2XGJQQdGRGO+bCBFi2Cdl9caVcP+njIFO+YCGU0pZPL+H0c1qOLhsjWLbKdIS4fgK1o7qCFHDymeDQFIW2ntMq7yS1HHVOe0rhQBtRm2ig8frL2XI9xR3Fe7JZN4pLnMN7LG4ZSMYdGeoj8PLZclTHIFlg08W9KPGi9iBf0m/X/znVuiZolm3bjqhMdR2+EMEsKJB9+dqz5gva1tkygdqsy6Cf/tUNkPDwskfVewMZ+Gzw76KNHYvaHqZkyTQJm1fUm/OfSU9D+uWzxIXb0YzahUHyS7MScKqzAQZXz8S3GyFxCwkH+keuahuCwLgEsmf/9JT2Xtdqs8Ts3C6MiXR2oWDKQ+drGuHUi43w1rI5/t41e1vb4fSGbhaKbZosy1xrX/6CiKd6Dqvf3DrT8LVcEyaSqRYyD7XZAvzc1Q55VKf8YHGgbdT+xeUiIYLtl0G6tbNrQV64NTXEPlrbjuw2rYx0FXyzphkG1jbD0a6FuL1UkZzAA9H+4Y9oHw6OrwCOpTfY+IoWbePXX/05CtJJa7nEkI0IQVuAizOjD9Fza+thoKsF6mlziaTJJJJxMUUHrwlo3Xsenp/fhJvfZINLznXAw0L++NTa8bimvlGcpGjYfkLRQIaRaWDwzVeFz9Q/6Qw8d+902LSyUWcqCgwPLi5z275B8KQD6xrr4J0lM/xe45eC02db2/5LGMH4j8a22wRRv60PCTp+kwmaOuXijGUCBl5tgRSRwgwxObbTS40TuOY48FjPacjgeXh3WQO80DCtdCI45iZWj+8ZhoLjlpKxYnMt5mw9jgTp1JSSHO5uBRcJ6isiFmaJWnf1QlZOxXEu/PTyApiu8JSK0lEu2nfsPg0FkdJZZmgiWHSGI7IkgYu5h9AN1jKbQ3eGnG3DhdB/XcLcXf2QBTxtmMXjmhza41VOiI5x8H4bE1VQoNxG+vgOjBYxe0sfusTNWMygLWMmHTxxkIbL69tgDDdtNQang9LSMwRO/gbQ++3gqTyxvgP70CF64wxEIZBa857zln9jy4jK9LqI+9/vU8kEpTNwaAyDQ0KQXhLyyQI+V6hDPe0v7QT35cXuJjSoKpIifZycx6rto3NaJtj+4+Rq5CRbp95C0U4vOTRO+RogeDILjv4JgFFoj5GENhncT0SOQI6ZEMsMkpP4qgRxTGHEyZ31d4E8+MqTgRcNsw+ZNJH0vIKWuc0TIDsBo/Dmd2fxACBpPwaTpMJtwhiW6F6La7Pu7UdnhH4shEAGAVlqU1AKZAazE4lX0de/Z2HOzl5NMKutAxg/pl6x4whKwarYfVTIJ8PzPyI0wSnuv3jFBZ1RGAf0O4V/qwQZ5pIspKFhx0m44rvhJebs4TUOV0UN2uLnlJUlW+aCLVh+D4/zMWvzMaTMJIl3ecIG0X8X0C1WCYUXE96HgxsWQspDmdcIvc/d+avfMAiPN6CTS9+DQ10LtLK4xCnP/BaZDOxZU6GLgPYiPWNNO87AGTp/DOzjFWDYYxmUzdo87VaDgDpi9rbj6MOc0MnC3G1B1ikref1aJCBVuAFeshbvS+o3OYnLnpbxWAx3P1xUhggSZm49jNuRfv8Vk4sILzc5MgeGYILSPcqZCIJHL2T7EQjsGHjTwoXXFoeUNguNsVFWMakwOYIhQvrAxl4mXrbw5jfLa+uMzAcPDxq2owhPwceMLb8oRwX/Zt0p7EwZOfxKEZKiAEMblpTwiSVIeAB/6SXckgTfMQJiPkn6zYJ33h9vdMRyKcvgyuvLRSaH166Inu5yQ+L0pTpeToLjZSF77XpZcoSyHTbu++CYSkt7f1BgbtskSvdQOSi8I0c2lifGKJeOEP7auFikcbb674FxXpyJoA8+jq/xMhWRI1RkZGPp9kNqpGC+XsKZjEO4v6POgQPrFk0q5qQJ2li1+3s1OFqjv/PikMInuw0/5w50rbjNOAD/A9jqAjMWkCMAAAAAAElFTkSuQmCC" alt="Telegram" />
                </a>
            </div>
        </div>
    </div>
</body>
</html>
`.trim();
}
function generateInviteTextEn(name) {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invitation to the Commune — a new type of society</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            animation: slideUp 0.8s ease-out;
        }
        
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            padding: 40px 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: rotate 20s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .logo {
            width: 96px;
            height: 96px;
            background: white;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            z-index: 2;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .header h1 {
            color: white;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            position: relative;
            z-index: 2;
        }
        
        .header p {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            position: relative;
            z-index: 2;
        }
        
        .content {
            padding: 40px 30px;
        }
        
        .greeting {
            font-size: 20px;
            color: #2c3e50;
            margin-bottom: 30px;
            font-weight: 600;
        }
        
        .message {
            color: #5a6c7d;
            font-size: 16px;
            margin-bottom: 25px;
            line-height: 1.8;
        }
        
        .features {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border-left: 4px solid #3498db;
        }
        
        .features h3 {
            color: #2c3e50;
            font-size: 18px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }
        
        .features h3::before {
            content: '✨';
            margin-right: 10px;
            font-size: 20px;
        }
        
        .feature-list {
            list-style: none;
        }
        
        .feature-list li {
            color: #5a6c7d;
            margin-bottom: 15px;
            padding-left: 25px;
            position: relative;
            line-height: 1.7;
        }
        
        .feature-list li::before {
            content: '🔹';
            position: absolute;
            left: 0;
            top: 0;
        }
        
        .feature-list li strong {
            color: #2c3e50;
            font-weight: 600;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            text-decoration: none;
            padding: 16px 40px;
            border-radius: 50px;
            font-weight: 600;
            font-size: 16px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(52, 152, 219, 0.3);
            transform: translateY(0);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 40px rgba(52, 152, 219, 0.4);
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .footer {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .footer p {
            color: #6c757d;
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .social-links {
            margin-top: 20px;
        }
        
        .social-links a {
            display: inline-block;
            width: 40px;
            height: 40px;
            margin: 0 10px;
            border-radius: 50%;
            text-decoration: none;
            color: white;
            font-size: 18px;
            line-height: 40px;
            transition: transform 0.3s ease;
        }
        
        .social-links a:hover {
            transform: scale(1.1);
        }
        
        @media (max-width: 600px) {
            .email-container {
                margin: 10px;
                border-radius: 15px;
            }
            
            .header {
                padding: 30px 20px;
            }
            
            .content {
                padding: 30px 20px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .greeting {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <img class="logo" src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAMUExURf///wAAAAADAAAAAL8seJ0AAAAEdFJOU////wBAKqn0AAAACXBIWXMAAA6/AAAOvwE4BVMkAAAAGHRFWHRTb2Z0d2FyZQBQYWludC5ORVQgNS4xLjgbaeqoAAAAtmVYSWZJSSoACAAAAAUAGgEFAAEAAABKAAAAGwEFAAEAAABSAAAAKAEDAAEAAAACAAAAMQECABAAAABaAAAAaYcEAAEAAABqAAAAAAAAAI12AQDoAwAAjXYBAOgDAABQYWludC5ORVQgNS4xLjgAAwAAkAcABAAAADAyMzABoAMAAQAAAAEAAAAFoAQAAQAAAJQAAAAAAAAAAgABAAIABAAAAFI5OAACAAcABAAAADAxMDAAAAAAy1oKaOdC6EEAAAPHSURBVGhDvZfRjt04DEO7nf//57WlI1m2aScB2h7gJjJNkXPRh878+nnFLwFXD7ywkSfBcuHJQtAFjCeu90Q8gVtzuWX9DWwojnesvoWtndMNex9gcUXr7HyE5Rml4lf812AUsD8hRNyVCO352SCaSKjsGt5BCbWxzj4WCBlsCsZCSbLxWrDn8Q6wVSzJo/Q4Q1CwnDFNlCg9LhAF8xGLE9sepQkLb4cwZzphcHLdozTp8AGIM+qBa6Ps+6gpBhuBwM69oG0xHCnWAomNOlbY+gCLQGYt4CZg7TWsBYSWAi4S9l7DWkLsuUA1cHO7GkRuvDfYDFAT5AC1EMG8kjD7HpiywaUTir0NknkluO0F3AgwGLkakOzPxEwz3GjwFLhoeLQ/E1wD9CPYBugNj57zNz/yBYwJckcVLH7EK1gB0YgCjoDRSKk/+ORQrgdIIAuKH+GpQGzAQwFnouIjCsQKWAFzgLWa+2zC+DAEttDhHPyxgq4YHIOpAEuAuukTWM7LrYBp9SA2ECRYGgiA+FjA/ICwmtQpBVyB0k4or2uXb4DWQZFg6aA4aCvcdlA6KBIsHZQOigJHsSAcwdZAuMVvJk4PLGaOGjzi5zqBrYHwteBegcVAelXAyUGTYHGUVuDaQLpmDzDLhAEXDloH5Qi2DoqDNkB30Bw0CRYHzUEboDtoDpoEi4PmoA3QHbQOyhFsHRQHrcCFgfSY7mCWCSvqGk2CxVHaCp7JhCTBYiB9LUA4gq2B8KmA0wOLmaMGj/i5TmBrINwKcHRQOigSLAZSA2GFWwet006/f7u6gaWD4qB1Xv2nfywwZq/j2r/9teVkubZgaSAA4q1geDhLsJyXW8FocLCUH65JJsdQDomtNDgGf+y3a1vocA5UgTDbGJ/vf4Acv0LanwqwNxAC+ScUViOl/uCzF2A2kEAVYATEK1gB0aGgNmBLkC9gTJA7lv1Q8NiAbYDeyYLSgKvAxQFMBS4aHu3PASZ7ATcCDEauJh5tz/krxHtgygaXTij2NkjmteN7CeoAHRALHhwFooHNCje3q0EE894aWHwNawmxf62A1FKwNLD2ARaB0FowN+QWw5FiLRDZKOPUUJZ81BSDjUBgp87bd8jhSDp8AOKMc0HiURosM6Q5y0lRovS4QBasR0GJ0uMMScF23ilJNtbZxwo5ySbsFSXJxmsBIYNdkV8i3jV1TxdhqkD/UzslX8D+hBRvFWfYXTjJ32Fz5aR/rWBr53zzpYINxe3ubQVuzf32TQfGE0/3DYIkWC68sHTIm+Dqys/P/x9tIYfiZGHlAAAAAElFTkSuQmCC" alt="Лого" />
            <h1>Commune</h1>
            <p>Digital society of a new type</p>
        </div>
        
        <div class="content">
            <div class="greeting">
                ${name ? `Hello, ${name}!` : "Hello!"}
            </div>
            
            <div class="message">
                I am writing to invite you to become one of the first participants in the Digital Society «Commune» project.
            </div>
            
            <div class="message">
                We are creating Commune as a response to what modern platforms have become: noisy, impersonal spaces where the main thing is the fight for attention. We believe it's possible to do things differently.
            </div>
            
            <div class="message">
                Commune is our attempt to build a digital environment based on trust and meaningful communication. A place where you can be yourself among "your people", where your voice is heard, and your contribution is truly valued. We don't have advertising and never will, because our goal is not to extract profit from participants, but to create the most comfortable and useful conditions for them.
            </div>
            
            <div class="features">
                <h3>Inside you will find:</h3>
                <ul class="feature-list">
                    <li>A system of small communities — <strong>communes</strong>, where you can unite with like-minded people.</li>
                    <li><strong>Reactor</strong> — our common platform for deep discussions and exchange of ideas, free from information noise.</li>
                    <li>Our own language, calendar and <strong>The Law</strong> — a system of rules created taking into account the experience of the modern world.</li>
                </ul>
            </div>
            
            <div class="message">
                The project is currently at its earliest stage. We are not just asking you to "register". We invite you to dialogue. It is your perspective, your ideas and criticism that can now determine what Commune will become in the future. You get the opportunity not just to observe, but to influence the creation of a community where you yourself will want to stay.
            </div>
            
            <div class="message">
                Our goal is to go beyond online and direct common resources towards real projects that will improve the lives of participants. But it all starts here and now, with the formation of our core — people who share our values.
            </div>
            
            <div class="message">
                Join us. At this stage, all platform functionality is available to all users without restrictions.
            </div>
            
            <center>
                <a href="https://commune.my" class="cta-button" target="_blank">
                    Join Us
                </a>
            </center>
        </div>
        
        <div class="footer">
            <p>Best regards,<br><strong>The «Commune» Project Team</strong></p>
            
            <div class="social-links">
                <a href="https://t.me/ds_commune_en" title="Telegram" target="_blank">
                    <img src="data:image/png;base64,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" alt="Telegram" />
                </a>
            </div>
        </div>
    </div>
</body>
</html>
`.trim();
}
function generateInviteText(name, locale) {
    switch (locale) {
        case "ru":
            return generateInviteTextRu(name);
        case "en":
            return generateInviteTextEn(name);
    }
}
//# sourceMappingURL=invite-text.js.map