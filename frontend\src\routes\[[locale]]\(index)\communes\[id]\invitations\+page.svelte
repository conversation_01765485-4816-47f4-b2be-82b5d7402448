<script lang="ts">
  import { onMount } from "svelte";
  import { Consts } from "@commune/api";
  import { getClient } from "$lib/acrpc";
  import { formatDate } from "$lib";

  const i18n = {
    en: {
      _page: {
        title: "Invitations — Commune",
      },
      communeInvitations: "Invitations",
      loading: "Loading...",
      noInvitations: "No invitations found",
      errorFetchingInvitations: "Failed to fetch invitations",
      errorOccurred: "An error occurred while fetching invitations",
      loadingMore: "Loading more invitations...",
      cancel: "Cancel Invitation",
      pending: "Pending",
      accepted: "Accepted",
      rejected: "Rejected",
      expired: "Expired",
      sentOn: "Sent on",
      cancelingInvitation: "Canceling...",
      errorCancelingInvitation: "Failed to cancel invitation",
      invitationCanceled: "Invitation canceled",
      backToCommune: "Back to Commune",
      invitedUser: "Invited Person",
      status: "Status",
      actions: "Actions",
      confirmCancel: "Are you sure you want to cancel this invitation?",
    },
    ru: {
      _page: {
        title: "Приглашения — Коммуна",
      },
      communeInvitations: "Приглашения",
      loading: "Загрузка...",
      noInvitations: "Приглашения не найдены",
      errorFetchingInvitations: "Не удалось загрузить приглашения",
      errorOccurred: "Произошла ошибка при загрузке приглашений",
      loadingMore: "Загружаем больше приглашений...",
      cancel: "Отменить приглашение",
      pending: "Ожидает",
      accepted: "Принято",
      rejected: "Отклонено",
      expired: "Истекло",
      sentOn: "Отправлено",
      cancelingInvitation: "Отменяем...",
      errorCancelingInvitation: "Не удалось отменить приглашение",
      invitationCanceled: "Приглашение отменено",
      backToCommune: "Назад к коммуне",
      invitedUser: "Приглашённый человек",
      status: "Статус",
      actions: "Действия",
      confirmCancel: "Вы уверены, что хотите отменить это приглашение?",
    },
  };

  const { fetcher: api } = getClient();

  const { data } = $props();
  const { locale, routeLocale, toLocaleHref, getAppropriateLocalization } = $derived(data);

  const t = $derived(i18n[locale]);

  // State using runes
  let invitations = $state(data.invitations);
  let error = $state<string | null>(null);

  let isLoadingMore = $state(false);
  let currentPage = $state(1);
  let isHasMoreInvitations = $state(data.isHasMoreInvitations);

  // Reference to the sentinel element for intersection observer
  let sentinelElement = $state<HTMLElement | null>(null);

  // Track loading states for individual invitations
  let loadingStates = $state<Record<string, "canceling" | null>>({});

  // Function to load more invitations
  async function loadMoreInvitations() {
    if (isLoadingMore || !isHasMoreInvitations) return;

    isLoadingMore = true;
    error = null;

    try {
      const nextPage = currentPage + 1;

      const newInvitations = await api.commune.invitation.list.get({
        communeId: data.commune.id,
        pagination: {
          page: nextPage,
        },
      });

      const users = newInvitations.length
        ? await api.user.list.get({
            ids: newInvitations.map((invitation) => invitation.userId),
          })
        : [];

      const userMap = new Map(users.map((user) => [user.id, user]));

      const invitationsWithDetails = newInvitations.map((invitation) => ({
        ...invitation,
        user: userMap.get(invitation.userId)!,
      }));

      invitations = [...invitations, ...invitationsWithDetails];
      currentPage = nextPage;

      // Check if there are more invitations to load
      isHasMoreInvitations = newInvitations.length === Consts.PAGE_SIZE;
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorOccurred;
      console.error(err);
    } finally {
      isLoadingMore = false;
    }
  }

  // Function to cancel an invitation
  async function cancelInvitation(invitationId: string) {
    if (!confirm(t.confirmCancel)) {
      return;
    }

    loadingStates[invitationId] = "canceling";
    error = null;

    try {
      await api.commune.invitation.delete({ id: invitationId });

      // Remove the invitation from the list
      invitations = invitations.filter((inv) => inv.id !== invitationId);
    } catch (err) {
      error = err instanceof Error ? err.message : t.errorCancelingInvitation;
      console.error(err);
    } finally {
      loadingStates[invitationId] = null;
    }
  }

  // Setup intersection observer for infinite scroll
  onMount(() => {
    let observer: IntersectionObserver;

    const setupObserver = () => {
      if (!sentinelElement) return;

      observer = new IntersectionObserver(
        (entries) => {
          const entry = entries[0];
          if (entry.isIntersecting && isHasMoreInvitations && !isLoadingMore) {
            loadMoreInvitations();
          }
        },
        {
          rootMargin: "100px", // Start loading 100px before the sentinel comes into view
          threshold: 0.1,
        },
      );

      observer.observe(sentinelElement);
    };

    // Try to setup observer immediately, or wait for element to be available
    if (sentinelElement) {
      setupObserver();
    } else {
      // Use a small delay to allow the DOM to render
      setTimeout(setupObserver, 100);
    }

    // Cleanup observer on component destroy
    return () => {
      if (observer) {
        observer.disconnect();
      }
    };
  });

  // Helper function to get status badge class
  function getStatusBadgeClass(status: string) {
    switch (status) {
      case "pending":
        return "bg-warning text-dark";
      case "accepted":
        return "bg-success";
      case "rejected":
        return "bg-danger";
      case "expired":
        return "bg-secondary";
      default:
        return "bg-secondary";
    }
  }

  // Helper function to get status text
  function getStatusText(status: string) {
    switch (status) {
      case "pending":
        return t.pending;
      case "accepted":
        return t.accepted;
      case "rejected":
        return t.rejected;
      case "expired":
        return t.expired;
      default:
        return status;
    }
  }
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-4 mb-5">
  <div class="d-flex justify-content-between align-items-center my-4">
    <div>
      <h1>{t.communeInvitations}</h1>
      <p class="text-muted mb-0">
        {getAppropriateLocalization(data.commune.name)}
      </p>
    </div>
    <a href={toLocaleHref(`/communes/${data.commune.id}`)} class="btn btn-outline-secondary">
      {t.backToCommune}
    </a>
  </div>

  {#if invitations.length === 0}
    <div class="text-center py-5">
      <p class="text-muted">{t.noInvitations}</p>
    </div>
  {:else}
    <!-- Desktop table view -->
    <div class="d-none d-md-block">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>{t.invitedUser}</th>
              <th>{t.status}</th>
              <th>{t.sentOn}</th>
              <th>{t.actions}</th>
            </tr>
          </thead>
          <tbody>
            {#each invitations as invitation (invitation.id)}
              <tr>
                <td>
                  <div class="d-flex align-items-center">
                    {#if invitation.user.image}
                      <img
                        src={`/images/${invitation.user.image}`}
                        alt="User avatar"
                        class="rounded me-2"
                        style="width: 32px; height: 32px; object-fit: contain;"
                      />
                    {:else}
                      <div
                        class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-2"
                        style="width: 32px; height: 32px;"
                      >
                        <i class="bi bi-person text-white"></i>
                      </div>
                    {/if}
                    <div>
                      <div class="fw-medium">
                        {getAppropriateLocalization(invitation.user.name)}
                      </div>
                    </div>
                  </div>
                </td>
                <td>
                  <span class={`badge ${getStatusBadgeClass(invitation.status)}`}>
                    {getStatusText(invitation.status)}
                  </span>
                </td>
                <td class="text-muted">{formatDate(invitation.createdAt, locale)}</td>
                <td>
                  {#if invitation.status === "pending"}
                    <button
                      class="btn btn-sm btn-outline-danger"
                      disabled={loadingStates[invitation.id] === "canceling"}
                      onclick={() => cancelInvitation(invitation.id)}
                    >
                      {#if loadingStates[invitation.id] === "canceling"}
                        <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                        {t.cancelingInvitation}
                      {:else}
                        {t.cancel}
                      {/if}
                    </button>
                  {:else}
                    <span class="text-muted">—</span>
                  {/if}
                </td>
              </tr>
            {/each}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Mobile card view -->
    <div class="d-md-none">
      <div class="row g-3">
        {#each invitations as invitation (invitation.id)}
          <div class="col-12">
            <div class="card">
              <div class="card-body">
                <div class="d-flex align-items-start justify-content-between mb-3">
                  <div class="d-flex align-items-center">
                    {#if invitation.user.image}
                      <img
                        src={`/images/${invitation.user.image}`}
                        alt="User avatar"
                        class="rounded me-3"
                        style="width: 48px; height: 48px; object-fit: contain;"
                      />
                    {:else}
                      <div
                        class="rounded-circle bg-secondary d-flex align-items-center justify-content-center me-3"
                        style="width: 48px; height: 48px;"
                      >
                        <i class="bi bi-person text-white"></i>
                      </div>
                    {/if}
                    <div>
                      <div class="fw-medium">
                        {getAppropriateLocalization(invitation.user.name)}
                      </div>
                    </div>
                  </div>
                  <span class={`badge ${getStatusBadgeClass(invitation.status)}`}>
                    {getStatusText(invitation.status)}
                  </span>
                </div>

                <div class="d-flex justify-content-between align-items-center">
                  <small class="text-muted">
                    {t.sentOn}
                    {formatDate(invitation.createdAt, locale)}
                  </small>
                  {#if invitation.status === "pending"}
                    <button
                      class="btn btn-sm btn-outline-danger"
                      disabled={loadingStates[invitation.id] === "canceling"}
                      onclick={() => cancelInvitation(invitation.id)}
                    >
                      {#if loadingStates[invitation.id] === "canceling"}
                        <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                        {t.cancelingInvitation}
                      {:else}
                        {t.cancel}
                      {/if}
                    </button>
                  {/if}
                </div>
              </div>
            </div>
          </div>
        {/each}
      </div>
    </div>
  {/if}

  <!-- Infinite scroll sentinel element -->
  {#if isHasMoreInvitations}
    <div bind:this={sentinelElement} class="text-center py-3">
      {#if isLoadingMore}
        <div class="spinner-border spinner-border-sm" role="status">
          <span class="visually-hidden">{t.loadingMore}</span>
        </div>
        <p class="text-muted mt-2 mb-0">{t.loadingMore}</p>
      {/if}
    </div>
  {/if}

  {#if error}
    <div class="alert alert-danger" role="alert">
      {error}
    </div>
  {/if}
</div>
