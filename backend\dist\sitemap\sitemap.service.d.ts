import { Sitemap } from "@commune/api";
import { CommuneService } from "src/commune/commune.service";
import { ReactorHubService } from "src/reactor/reactor-hub.service";
import { ReactorPostService } from "src/reactor/reactor-post.service";
import { ReactorCommunityService } from "src/reactor/reactor-community.service";
export declare class SitemapService {
    private readonly communeService;
    private readonly reactorPostService;
    private readonly reactorHubService;
    private readonly reactorCommunityService;
    constructor(communeService: CommuneService, reactorPostService: ReactorPostService, reactorHubService: ReactorHubService, reactorCommunityService: ReactorCommunityService);
    getSitemapGenerationData(key: string): Promise<Sitemap.GetSitemapGenerationDataOutput>;
}
