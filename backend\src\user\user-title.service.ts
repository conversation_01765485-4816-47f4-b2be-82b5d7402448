import { Common, User } from "@commune/api";
import { ForbiddenException, Injectable } from "@nestjs/common";
import { CurrentUser } from "src/auth/types";
import { getError } from "src/common/errors";
import { PrismaService } from "src/prisma/prisma.service";
import { toPrismaLocalizations } from "src/utils";

@Injectable()
export class UserTitleService {
    constructor(private readonly prisma: PrismaService) {}

    async getUserTitles(
        input: User.GetUserTitlesInput,
        currentUser: CurrentUser,
    ) {
        const { userId, ids, isActive } = input;

        return await this.prisma.userTitle.findMany({
            where: {
                userId,
                id: ids && { in: ids },
                isActive: isActive ?? undefined,
            },
            select: {
                id: true,
                userId: true,
                color: true,
                isActive: true,
                name: {
                    select: {
                        locale: true,
                        value: true,
                    },
                },
                createdAt: true,
                updatedAt: true,
                deletedAt: currentUser.isAdmin,
            },
        });
    }

    async createUserTitle(
        input: User.CreateUserTitleInput,
        currentUser: CurrentUser,
    ) {
        if (!currentUser.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        return await this.prisma.userTitle.create({
            data: {
                userId: input.userId,
                name: {
                    create: toPrismaLocalizations(input.name, "name"),
                },
                isActive: input.isActive,
                color: input.color,
            },
        });
    }

    async updateUserTitle(
        input: User.UpdateUserTitleInput,
        currentUser: CurrentUser,
    ) {
        const userTitle = await this.prisma.userTitle.findUniqueOrThrow({
            where: {
                id: input.id,
                deletedAt: currentUser.isAdmin ? undefined : null,
            },
        });

        if (!currentUser.isAdmin) {
            if (userTitle.userId !== currentUser.id) {
                throw new ForbiddenException(
                    ...getError("must_be_admin_or_owner"),
                );
            }
        }

        return await this.prisma.userTitle.update({
            where: {
                id: input.id,
            },
            data: {
                name: input.name && {
                    deleteMany: {},
                    create: toPrismaLocalizations(input.name, "name"),
                },
                isActive: input.isActive,
                color: input.color,
            },
        });
    }

    async deleteUserTitle(
        input: Common.ObjectWithId,
        currentUser: CurrentUser,
    ) {
        if (!currentUser.isAdmin) {
            throw new ForbiddenException(...getError("must_be_admin"));
        }

        return await this.prisma.userTitle.update({
            where: {
                id: input.id,
            },
            data: {
                deletedAt: new Date(),
            },
        });
    }
}
