import { Auth } from "@commune/api";
import * as prisma from "@prisma/client";
import { UserService } from "src/user/user.service";
import { EmailService } from "src/email/email.service";
import { EmailOtpService } from "src/email/email-otp.service";
import { AdminService } from "src/admin/admin.service";
import { ConfigService } from "src/config/config.service";
type InfoDto = {
    ipAddress: string | null;
    userAgent: string | null;
};
export declare class AuthService {
    private readonly configService;
    private readonly emailService;
    private readonly userService;
    private readonly userOtpService;
    private readonly adminService;
    constructor(configService: ConfigService, emailService: EmailService, userService: UserService, userOtpService: EmailOtpService, adminService: AdminService);
    protected createSessionUser(user: prisma.User): {
        id: string;
        email: string;
        role: prisma.$Enums.UserRole;
    };
    protected generateOtp(): string;
    otp(dto: Auth.SendOtpInput & InfoDto): Promise<boolean>;
    login(dto: Auth.SigninInput & InfoDto): Promise<{
        user: {
            id: string;
            email: string;
            role: prisma.$Enums.UserRole;
        };
    }>;
    register(dto: Auth.SignupInput & InfoDto): Promise<{
        user: {
            id: string;
            email: string;
            role: prisma.$Enums.UserRole;
        };
    }>;
}
export {};
