import { Body, Controller, Get, Headers, Param, Query } from "@nestjs/common";
import { AppService } from "./app.service";

@Controller()
export class AppController {
    constructor(private readonly appService: AppService) {}

    @Get()
    hello(): string {
        return this.appService.getHello();
    }

    @Get("test")
    test(
        @Headers() headers: object,
        @Param() params: object,
        @Query() query: object,
        @Body() body: object,
    ) {
        return { headers, params, query, body };
    }
}
