import { Common } from "@commune/api";
import { Injectable } from "@nestjs/common";

import {
    VARIANT_COUNT as EPITHET_VARIANT_COUNT,
    getRandomEnEpithet,
    getRandomRuEpithet,
} from "./epithets";

import {
    VARIANT_COUNT as ANIMAL_VARIANT_COUNT,
    getRandomEnAnimal,
    getRandomRuAnimal,
} from "./animals";

function capitalize(word: string) {
    return word.charAt(0).toUpperCase() + word.slice(1);
}

@Injectable()
export class UserNameGeneratorService {
    generateUserName(): Common.Localizations {
        const epithetVariant = Math.floor(
            Math.random() * EPITHET_VARIANT_COUNT,
        );
        const animalVariant = Math.floor(Math.random() * ANIMAL_VARIANT_COUNT);

        return [
            {
                locale: "en",
                value: this.generateEnName(epithetVariant, animalVariant),
            },
            {
                locale: "ru",
                value: this.generateRuName(epithetVariant, animalVariant),
            },
        ];
    }

    private generateEnName(epithetVariant: number, animalVariant: number) {
        const epithet = getRandomEnEpithet(epithetVariant);
        const animal = getRandomEnAnimal(animalVariant);

        return `${capitalize(epithet)} ${capitalize(animal)}`;
    }

    private generateRuName(epithetVariant: number, animalVariant: number) {
        const [animal, form] = getRandomRuAnimal(animalVariant);
        const epithet = getRandomRuEpithet(epithetVariant, form);

        return `${capitalize(epithet)} ${capitalize(animal)}`;
    }
}
