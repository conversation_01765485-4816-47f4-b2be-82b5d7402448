import type { PageLoad } from "./$types";

import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load: PageLoad = async ({ fetch, url }) => {
  const { fetcher: api } = getClient();

  const searchQuery = url.searchParams.get("search");

  const [
    me,
    hubs,
  ] = await Promise.all([
    api.user.me.get({ fetch, ctx: { url } }),
    api.reactor.hub.list.get({ query: searchQuery ?? undefined }, { fetch, ctx: { url } }),
  ]);

  return {
    me,
    hubs,
    searchQuery,
    isHasMoreHubs: hubs.length === Consts.PAGE_SIZE,
  };
};
