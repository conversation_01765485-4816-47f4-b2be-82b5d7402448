// @ts-nocheck
import type { PageLoad } from "./$types";

import { error } from "@sveltejs/kit";
import { Consts } from "@commune/api";
import { getClient } from "$lib/acrpc";

export const load = async ({ fetch, params, url }: Parameters<PageLoad>[0]) => {
  const { fetcher: api } = getClient();

  const [
    me,
    [hub],
    communities,
  ] = await Promise.all([
    api.user.me.get({ fetch, skipInterceptor: true })
      .catch(() => null),
    api.reactor.hub.list.get({ ids: [params.id] }, { fetch, ctx: { url } }),
    api.reactor.community.list.get({ hubId: params.id }, { fetch, ctx: { url } }),
  ]);

  if (!hub) {
    throw error(404, "Hub not found");
  }

  // Check if user can edit this hub (admin or head user)
  const canEdit = me && (me.role === "admin" || me.id === hub.headUser.id);

  return {
    me,
    hub,
    communities,
    canEdit,
    isHasMoreCommunities: communities.length === Consts.PAGE_SIZE,
  };
};
