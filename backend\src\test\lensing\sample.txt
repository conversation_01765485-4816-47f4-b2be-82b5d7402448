&& || = != > < >= <= ~
and or is isnt more less moreeq lesseq like

hub
group
author
rating
usefulness
tag
difficulty? (easy, medium, hard)
title
body
duration
age

(
    (
        hub = ["guid", "guid", "guid"]
        && group = ["guid", "guid"]
    )
    || (
        hub = ["guid"]
        && group != ["guid", "guid"]
        && author != ["guid"]
    )
)
&& rating >= 200
&& usefulness >= 8
&& difficulty = ["easy", "medium"]
&& duration < 10
&& age < 3d
&& tag != ["guid", "guid"]
&& title ~ "python"
&& body ~ "pandas"



(
    (
        hub is ["guid", "guid", "guid"]
        and group is ["guid", "guid"]
    )
    or (
        hub is ["guid"]
        and group isnt ["guid", "guid"]
        and author isnt ["guid"]
    )
)
and rating moreeq 200
and usefulness moreeq 8
and difficulty is ["easy", "medium"]
and duration less 10
and age less 3d
and tag isnt ["guid", "guid"]
and title like "python"
and body like "pandas"


(
    (
        hub = ["guid", "guid", "guid"]
        and group = ["guid", "guid"]
    )
    or (
        hub = ["guid"]
        and group != ["guid", "guid"]
        and author != ["guid"]
    )
)
and rating >= 200
and usefulness >= 8
and difficulty = ["easy", "medium"]
and duration < 10
and age < 3d
and tag != ["guid", "guid"]
and title like "python"
and body like "pandas"
