"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.toPrismaLocalizationsWhere = toPrismaLocalizationsWhere;
function toPrismaLocalizationsWhere(value, isCaseSensitive = false) {
    return {
        some: {
            value: {
                contains: value,
                mode: isCaseSensitive ? "default" : "insensitive",
            },
        },
    };
}
//# sourceMappingURL=to-prisma-localizations-where.js.map