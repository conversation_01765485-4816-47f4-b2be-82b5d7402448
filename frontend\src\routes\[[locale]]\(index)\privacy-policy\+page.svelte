<script lang="ts">
  const i18n = {
    en: {
      _page: {
        title: "Privacy Policy — Commune",
      },
      title: "Digital Society Commune — Privacy Policy",
      effectiveDate: "Effective date: August 23, 2025",
      whoWeAre: {
        title: "1. Who we are",
        description:
          'Digital Society Commune (also "DS Commune", "Commune", "we", "us", "our") operates the website commune.my.',
        contact:
          "If you have privacy questions or want to exercise any privacy rights, contact <NAME_EMAIL>.",
        note: "Note: We do not have a Data Protection Officer (DPO) listed. For legal compliance in some jurisdictions we may appoint a representative or DPO later.",
      },
      scope: {
        title: "2. Scope / who this policy covers",
        description:
          'This policy explains how we collect, use, share, and store personal data from people who use commune.my and related services (the "Service"). It applies to visitors, registered users, customers, and contributors worldwide. You may be subject to specific rights under laws such as the EU GDPR, UK GDPR, California CCPA/CPRA, COPPA (for children), and Russia\'s personal data law (152-FZ).',
      },
      dataCollection: {
        title: "3. What we collect",
        description:
          "We collect only the data needed to run the Service and keep it safe. Principal categories:",
        categories: [
          "Account & contact: Email (required to register and sign in); display name (optional/free text).",
          "Technical & safety: IP address, browser user agent, session cookie. These are used for security, fraud prevention, and to keep the service working.",
          "User content: Any files, images, text or other content you upload or publish in the forum, blog comments, bookings, or SaaS features — this can include personal data if you (or others) put personal information in uploads. We do not proactively screen uploads for personal data.",
          "Other: Any information you choose to add to your profile, messages, or public posts.",
        ],
      },
      howWeCollect: {
        title: "4. How we collect data",
        methods: [
          "Directly from you via forms and registration pages.",
          "Automatically when you use the Service (IP, user agent, session cookie).",
          "From content you upload or publish.",
        ],
      },
      whyWeProcess: {
        title: "5. Why we process your data (Purposes) and legal bases",
        description: "We use personal data for the following purposes:",
        purposes: [
          "To provide and operate the Service (create accounts, login, manage bookings, deliver SaaS features). Legal basis (GDPR): performance of a contract/necessary for service.",
          "Account security & fraud prevention (monitor logins, block abuse). Legal basis: legitimate interests (keeping the platform secure).",
          "Customer support and communications (responding to <NAME_EMAIL>). Legal basis: contract performance or legitimate interest.",
          "Analytics and product improvement (aggregate metrics, error logs). Legal basis: legitimate interests or consent where required.",
          "Billing / future payments (if enabled in future) — we will disclose processors and legal basis when implemented.",
        ],
        californiaNote:
          "If you are a California resident, you have specific rights under CCPA/CPRA (right to know, delete, opt-out of sale or sharing). We do not sell personal information.",
      },
      cookies: {
        title: "6. Cookies and similar technologies",
        description:
          "We use a single session cookie to keep you signed in and to secure sessions. We will show a cookie banner that explains this and (if/when we add analytics or other optional cookies) will request consent for non-essential cookies.",
      },
      sharing: {
        title: "7. Sharing, processors and third parties",
        description:
          "At present you stated no regular sharing with third parties for analytics, marketing or resale. If we add hosting, analytics, email delivery, or payment providers, we will list them and use processors under contracts.",
        noSale: "We will not sell personal data (this is consistent with your project principles).",
      },
      storage: {
        title: "8. International transfers and storage",
        description:
          "Our servers are located in the Netherlands (NL). Data stored in NL is processed inside the European Union. If we transfer personal data outside the EU/EEA (for example, to a vendor outside the EEA), we will use appropriate safeguards such as the European Commission's Standard Contractual Clauses (SCCs) or other lawful mechanisms to protect transfers.",
      },
      retention: {
        title: "9. Retention",
        periods: [
          "Account data (email, profile): retained until you delete your account or request deletion.",
          "Backups & logs (technical): typically retained for a limited period for security and operations — suggested default: 12 months for logs; 90 days for backups unless needed for security or legal reasons.",
          "Uploaded content: retained until you delete it, unless removal is required by law.",
        ],
        note: "If you need different retention periods to meet local legal requirements, we will update the policy and communicate retention details on request.",
      },
      rights: {
        title: "10. Your rights (summary)",
        description: "We try to keep this easy. You can exercise the following rights:",
        list: [
          "Access — ask what personal data we hold about you.",
          "Rectification — ask to correct inaccurate data.",
          "Deletion — ask us to delete your account and personal data (subject to legal exceptions).",
          "Restriction — ask us to limit how we use your data.",
          "Portability — request a machine-readable copy of the personal data you provided.",
          "Object — object to processing based on legitimate interests (we'll explain impacts).",
        ],
        californiaSpecific:
          "California-specific: California residents can request disclosure of categories and specific pieces of personal information we collect, request deletion, and opt-out of sale/sharing (we do not sell).",
        contact:
          "To exercise any right, contact <EMAIL> with a clear description of your request. We will verify your identity before acting on certain requests.",
        coppaNote:
          "We allow users with emails to register and do not knowingly restrict general registration to adults. If we obtain actual knowledge that we are collecting personal information from a child under 13, COPPA requirements will be triggered (parental notice and verifiable consent). We will comply with COPPA rules where they apply. If you are a parent and believe we have collected your child's information, contact us.",
      },
      security: {
        title: "11. Security",
        description:
          "We use HTTPS/TLS, session management, and passwordless authentication. We implement reasonable administrative, technical, and physical safeguards to protect personal data. No system is perfectly secure — if we become aware of a breach involving your personal data we will follow applicable laws and notify you and relevant authorities where required.",
      },
      breachNotifications: {
        title: "12. Data breach and notifications",
        description:
          "If a security incident affects your personal data we will investigate and, where required by law, notify affected users and supervisory authorities without undue delay in accordance with applicable law (for example, GDPR notification timelines).",
      },
      children: {
        title: "13. Children",
        description:
          "We do not knowingly target children under 13. If you are under 13 (or local minimum age), do not register without parental consent. If you believe a child under 13 has registered without parent/guardian consent, contact us so we can remove the data.",
      },
      changes: {
        title: "14. Changes to this policy",
        description:
          "We may update this policy as the Service or laws change. Material changes will be posted on the site with a new effective date.",
      },
      contact: {
        title: "15. Contact & how to exercise rights",
        description:
          "Email: <EMAIL>. Please include enough detail so we can locate your account and verify your request. We typically respond within the time frames required by local law.",
      },
    },

    ru: {
      _page: {
        title: "Политика конфиденциальности — Коммуна",
      },
      title: "Цифровое сообщество Коммуна — Политика конфиденциальности",
      effectiveDate: "Дата вступления в силу: 23 августа 2025 г.",
      whoWeAre: {
        title: "1. Кто мы",
        description:
          'Цифровое сообщество Коммуна (также "DS Commune", "Коммуна", "мы", "нас", "наш") управляет веб-сайтом commune.my.',
        contact:
          "Если у вас есть вопросы о конфиденциальности или вы хотите воспользоваться правами на конфиденциальность, свяжитесь с нами по адресу <EMAIL>.",
        note: "Примечание: У нас нет указанного сотрудника по защите данных (DPO). Для соблюдения законодательства в некоторых юрисдикциях мы можем назначить представителя или DPO позже.",
      },
      scope: {
        title: "2. Область применения / кого охватывает эта политика",
        description:
          'Эта политика объясняет, как мы собираем, используем, передаем и храним персональные данные людей, которые используют commune.my и связанные услуги ("Сервис"). Она применяется к посетителям, зарегистрированным пользователям, клиентам и участникам по всему миру. На вас могут распространяться специальные права в соответствии с такими законами, как EU GDPR, UK GDPR, California CCPA/CPRA, COPPA (для детей) и российский закон о персональных данных (152-ФЗ).',
      },
      dataCollection: {
        title: "3. Что мы собираем",
        description:
          "Мы собираем только данные, необходимые для работы Сервиса и обеспечения его безопасности. Основные категории:",
        categories: [
          "Аккаунт и контакты: Email (обязателен для регистрации и входа); отображаемое имя (необязательно/свободный текст).",
          "Технические данные и безопасность: IP-адрес, пользовательский агент браузера, сессионный cookie. Они используются для безопасности, предотвращения мошенничества и поддержания работы сервиса.",
          "Пользовательский контент: Любые файлы, изображения, текст или другой контент, который вы загружаете или публикуете на форуме, в комментариях блога, бронированиях или SaaS-функциях — это может включать персональные данные, если вы (или другие) размещаете личную информацию в загрузках. Мы не проверяем загрузки на наличие персональных данных проактивно.",
          "Прочее: Любая информация, которую вы решите добавить в свой профиль, сообщения или публичные посты.",
        ],
      },
      howWeCollect: {
        title: "4. Как мы собираем данные",
        methods: [
          "Напрямую от вас через формы и страницы регистрации.",
          "Автоматически при использовании Сервиса (IP, пользовательский агент, сессионный cookie).",
          "Из контента, который вы загружаете или публикуете.",
        ],
      },
      whyWeProcess: {
        title: "5. Зачем мы обрабатываем ваши данные (Цели) и правовые основания",
        description: "Мы используем персональные данные для следующих целей:",
        purposes: [
          "Для предоставления и работы Сервиса (создание аккаунтов, вход, управление бронированиями, предоставление SaaS-функций). Правовое основание (GDPR): исполнение договора/необходимо для сервиса.",
          "Безопасность аккаунта и предотвращение мошенничества (мониторинг входов, блокировка злоупотреблений). Правовое основание: законные интересы (обеспечение безопасности платформы).",
          "Поддержка клиентов и коммуникации (ответы на письма на <EMAIL>). Правовое основание: исполнение договора или законный интерес.",
          "Аналитика и улучшение продукта (агрегированные метрики, журналы ошибок). Правовое основание: законные интересы или согласие, где требуется.",
          "Биллинг / будущие платежи (если будет включено в будущем) — мы раскроем процессоры и правовое основание при внедрении.",
        ],
        californiaNote:
          "Если вы житель Калифорнии, у вас есть специальные права в соответствии с CCPA/CPRA (право знать, удалять, отказаться от продажи или передачи). Мы не продаем персональную информацию.",
      },
      cookies: {
        title: "6. Cookies и подобные технологии",
        description:
          "Мы используем один сессионный cookie, чтобы поддерживать ваш вход в систему и обеспечивать безопасность сессий. Мы покажем баннер cookie, который объясняет это, и (если/когда мы добавим аналитику или другие дополнительные cookie) запросим согласие на несущественные cookie.",
      },
      sharing: {
        title: "7. Передача, процессоры и третьи стороны",
        description:
          "В настоящее время мы не передаем данные третьим сторонам регулярно для аналитики, маркетинга или перепродажи. Если мы добавим хостинг, аналитику, доставку email или платежных провайдеров, мы перечислим их и будем использовать процессоры по договорам.",
        noSale:
          "Мы не будем продавать персональные данные (это соответствует принципам нашего проекта).",
      },
      storage: {
        title: "8. Международные передачи и хранение",
        description:
          "Наши серверы расположены в Нидерландах (NL). Данные, хранящиеся в NL, обрабатываются внутри Европейского Союза. Если мы передаем персональные данные за пределы ЕС/ЕЭЗ (например, поставщику за пределами ЕЭЗ), мы будем использовать соответствующие гарантии, такие как Стандартные договорные положения (SCC) Европейской комиссии или другие законные механизмы для защиты передач.",
      },
      retention: {
        title: "9. Хранение",
        periods: [
          "Данные аккаунта (email, профиль): хранятся до тех пор, пока вы не удалите свой аккаунт или не запросите удаление.",
          "Резервные копии и журналы (технические): обычно хранятся в течение ограниченного периода для безопасности и операций — предлагаемое по умолчанию: 12 месяцев для журналов; 90 дней для резервных копий, если не требуется для безопасности или правовых причин.",
          "Загруженный контент: хранится до тех пор, пока вы не удалите его, если удаление не требуется по закону.",
        ],
        note: "Если вам нужны разные периоды хранения для соответствия местным правовым требованиям, мы обновим политику и сообщим детали хранения по запросу.",
      },
      rights: {
        title: "10. Ваши права (краткое изложение)",
        description:
          "Мы стараемся сделать это простым. Вы можете воспользоваться следующими правами:",
        list: [
          "Доступ — спросить, какие персональные данные мы храним о вас.",
          "Исправление — попросить исправить неточные данные.",
          "Удаление — попросить нас удалить ваш аккаунт и персональные данные (с учетом правовых исключений).",
          "Ограничение — попросить нас ограничить использование ваших данных.",
          "Переносимость — запросить машиночитаемую копию персональных данных, которые вы предоставили.",
          "Возражение — возразить против обработки на основе законных интересов (мы объясним последствия).",
        ],
        californiaSpecific:
          "Специально для Калифорнии: жители Калифорнии могут запросить раскрытие категорий и конкретных частей персональной информации, которую мы собираем, запросить удаление и отказаться от продажи/передачи (мы не продаем).",
        contact:
          "Чтобы воспользоваться любым правом, свяжитесь с <EMAIL> с четким описанием вашего запроса. Мы проверим вашу личность перед выполнением определенных запросов.",
        coppaNote:
          "Мы разрешаем пользователям с email регистрироваться и сознательно не ограничиваем общую регистрацию только взрослыми. Если мы получим фактические знания о том, что собираем персональную информацию от ребенка младше 13 лет, будут запущены требования COPPA (уведомление родителей и проверяемое согласие). Мы будем соблюдать правила COPPA там, где они применяются. Если вы родитель и считаете, что мы собрали информацию вашего ребенка, свяжитесь с нами.",
      },
      security: {
        title: "11. Безопасность",
        description:
          "Мы используем HTTPS/TLS, управление сессиями и аутентификацию без пароля. Мы внедряем разумные административные, технические и физические меры защиты для защиты персональных данных. Ни одна система не является абсолютно безопасной — если мы узнаем о нарушении, затрагивающем ваши персональные данные, мы будем следовать применимым законам и уведомим вас и соответствующие органы, где это требуется.",
      },
      breachNotifications: {
        title: "12. Нарушения данных и уведомления",
        description:
          "Если инцидент безопасности затрагивает ваши персональные данные, мы проведем расследование и, где требуется по закону, уведомим затронутых пользователей и надзорные органы без неоправданной задержки в соответствии с применимым законом (например, сроки уведомления GDPR).",
      },
      children: {
        title: "13. Дети",
        description:
          "Мы сознательно не ориентируемся на детей младше 13 лет. Если вам меньше 13 лет (или местного минимального возраста), не регистрируйтесь без согласия родителей. Если вы считаете, что ребенок младше 13 лет зарегистрировался без согласия родителя/опекуна, свяжитесь с нами, чтобы мы могли удалить данные.",
      },
      changes: {
        title: "14. Изменения в этой политике",
        description:
          "Мы можем обновлять эту политику по мере изменения Сервиса или законов. Существенные изменения будут размещены на сайте с новой датой вступления в силу.",
      },
      contact: {
        title: "15. Контакты и как воспользоваться правами",
        description:
          "Email: <EMAIL>. Пожалуйста, включите достаточно деталей, чтобы мы могли найти ваш аккаунт и проверить ваш запрос. Мы обычно отвечаем в сроки, требуемые местным законодательством.",
      },
    },
  };

  const { data } = $props();
  const { locale } = $derived(data);

  const t = $derived(i18n[locale]);
</script>

<svelte:head>
  <title>{t._page.title}</title>
</svelte:head>

<div class="container my-5">
  <div class="responsive-container">
    <h1 class="mb-4">{t.title}</h1>
    <p class="text-muted mb-4"><strong>{t.effectiveDate}</strong></p>

    <!-- Who We Are Section -->
    <section class="mb-5">
      <h2>{t.whoWeAre.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.whoWeAre.description}</p>
          <p>{t.whoWeAre.contact}</p>
          <blockquote class="blockquote">
            <p class="mb-0 text-muted"><small>{t.whoWeAre.note}</small></p>
          </blockquote>
        </div>
      </div>
    </section>

    <!-- Scope Section -->
    <section class="mb-5">
      <h2>{t.scope.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.scope.description}</p>
        </div>
      </div>
    </section>

    <!-- Data Collection Section -->
    <section class="mb-5">
      <h2>{t.dataCollection.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.dataCollection.description}</p>
          <ul>
            {#each t.dataCollection.categories as category}
              <li>
                <strong>{category.split(":")[0]}:</strong>
                {category.split(":").slice(1).join(":").trim()}
              </li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- How We Collect Section -->
    <section class="mb-5">
      <h2>{t.howWeCollect.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.howWeCollect.methods as method}
              <li>{method}</li>
            {/each}
          </ul>
        </div>
      </div>
    </section>

    <!-- Why We Process Section -->
    <section class="mb-5">
      <h2>{t.whyWeProcess.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.whyWeProcess.description}</p>
          <ul>
            {#each t.whyWeProcess.purposes as purpose}
              <li>{purpose}</li>
            {/each}
          </ul>
          <p class="text-muted"><small>{t.whyWeProcess.californiaNote}</small></p>
        </div>
      </div>
    </section>

    <!-- Cookies Section -->
    <section class="mb-5">
      <h2>{t.cookies.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.cookies.description}</p>
        </div>
      </div>
    </section>

    <!-- Sharing Section -->
    <section class="mb-5">
      <h2>{t.sharing.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            <li>{t.sharing.description}</li>
            <li>{t.sharing.noSale}</li>
          </ul>
        </div>
      </div>
    </section>

    <!-- Storage Section -->
    <section class="mb-5">
      <h2>{t.storage.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.storage.description}</p>
        </div>
      </div>
    </section>

    <!-- Retention Section -->
    <section class="mb-5">
      <h2>{t.retention.title}</h2>
      <div class="card">
        <div class="card-body">
          <ul>
            {#each t.retention.periods as period}
              <li>
                <strong>{period.split(":")[0]}:</strong>
                {period.split(":").slice(1).join(":").trim()}
              </li>
            {/each}
          </ul>
          <p class="text-muted"><small>{t.retention.note}</small></p>
        </div>
      </div>
    </section>

    <!-- Rights Section -->
    <section class="mb-5">
      <h2>{t.rights.title}</h2>
      <div class="card">
        <div class="card-body">
          <p class="lead">{t.rights.description}</p>
          <ul>
            {#each t.rights.list as right}
              <li>
                <strong>{right.split("—")[0]}—</strong>
                {right.split("—").slice(1).join("—").trim()}
              </li>
            {/each}
          </ul>
          <p class="text-muted"><small>{t.rights.californiaSpecific}</small></p>
          <p>{t.rights.contact}</p>

          <h4 class="h5 mt-4">Note about COPPA and minors</h4>
          <p class="text-muted"><small>{t.rights.coppaNote}</small></p>
        </div>
      </div>
    </section>

    <!-- Security Section -->
    <section class="mb-5">
      <h2>{t.security.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.security.description}</p>
        </div>
      </div>
    </section>

    <!-- Breach Notifications Section -->
    <section class="mb-5">
      <h2>{t.breachNotifications.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.breachNotifications.description}</p>
        </div>
      </div>
    </section>

    <!-- Children Section -->
    <section class="mb-5">
      <h2>{t.children.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.children.description}</p>
        </div>
      </div>
    </section>

    <!-- Changes Section -->
    <section class="mb-5">
      <h2>{t.changes.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.changes.description}</p>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="mb-5">
      <h2>{t.contact.title}</h2>
      <div class="card">
        <div class="card-body">
          <p>{t.contact.description}</p>
        </div>
      </div>
    </section>
  </div>
</div>
