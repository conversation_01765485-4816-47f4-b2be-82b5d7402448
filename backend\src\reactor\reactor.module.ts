import { <PERSON>du<PERSON> } from "@nestjs/common";
import { RatingModule } from "src/rating/rating.module";
import { MinioModule } from "src/minio/minio.module";
import { ReactorController } from "./reactor.controller";
import { ReactorHubService } from "./reactor-hub.service";
import { ReactorPostService } from "./reactor-post.service";
import { ReactorLensService } from "./lens/reactor-lens.service";
import { ReactorCommentService } from "./reactor-comment.service";
import { ReactorCommunityService } from "./reactor-community.service";

@Module({
    imports: [RatingModule, MinioModule],
    controllers: [ReactorController],
    providers: [
        ReactorPostService,
        ReactorCommentService,
        ReactorLensService,
        ReactorHubService,
        ReactorCommunityService,
    ],
    exports: [
        ReactorPostService,
        ReactorCommentService,
        ReactorLensService,
        ReactorHubService,
        ReactorCommunityService,
    ],
})
export class ReactorModule {}
