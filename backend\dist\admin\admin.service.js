"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const utils_1 = require("../utils");
const email_service_1 = require("../email/email.service");
const prisma_service_1 = require("../prisma/prisma.service");
let AdminService = class AdminService {
    constructor(prisma, emailService) {
        this.prisma = prisma;
        this.emailService = emailService;
    }
    async isInviteExists(email) {
        const invite = await this.prisma.userInvite.findUnique({
            where: {
                email,
            },
        });
        return !!invite;
    }
    async useInvite(email) {
        return await this.prisma.userInvite.update({
            where: {
                email,
            },
            data: {
                isUsed: true,
            },
        });
    }
    async getUserInvites(input, user) {
        if (!user.isAdmin) {
            throw new common_1.NotFoundException();
        }
        return await this.prisma.userInvite.findMany({
            ...(0, utils_1.toPrismaPagination)(input.pagination),
            orderBy: {
                isUsed: "asc",
            },
        });
    }
    async upsertUserInvite(input, user) {
        if (!user.isAdmin) {
            throw new common_1.NotFoundException();
        }
        return await this.prisma.$transaction(async (trx) => {
            const invite = await trx.userInvite.upsert({
                where: {
                    email: input.email,
                },
                update: {
                    name: input.name,
                    locale: input.locale,
                    isUsed: false,
                },
                create: {
                    email: input.email,
                    name: input.name,
                    locale: input.locale,
                },
            });
            await this.emailService.sendInvite({
                to: input.email,
                name: input.name,
                locale: input.locale,
            });
            return invite;
        });
    }
    async deleteUserInvite(input, user) {
        if (!user.isAdmin) {
            throw new common_1.NotFoundException();
        }
        return await this.prisma.userInvite.delete({
            where: {
                id: input.id,
            },
        });
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService,
        email_service_1.EmailService])
], AdminService);
//# sourceMappingURL=admin.service.js.map